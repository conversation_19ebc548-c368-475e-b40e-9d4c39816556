// FONT FAMILY **/
$font-family-base: 'Averta PE', 'Helvetica Neue', 'Roboto', sans-serif;

// BREAKPOINTS **/
// The tiny screens break point should be used scarcely It's only for special cases
// where mobile styles fit tiny screens
$breakpoint-tiny-screens: 375px;
$breakpoint-tablet: 600px;
$breakpoint-desktop-sm: 768px;
$breakpoint-desktop: 1027px;
$breakpoint-desktop-md: 1280px;
$breakpoint-desktop-xl: 1442px;

// COLORS **/
$kpyblue: rgb(35, 118, 243);
$kpyblue-dark: rgba(35, 118, 243, 0.8);
$kpyblue-medium: rgba(35, 118, 243, 0.5);
$kpyblue-light: rgba(35, 118, 243, 0.3);
$kpyblue-vlight: rgba(35, 118, 243, 0.1);

$dblue: rgb(16, 38, 73);
$dblue-light: rgba(16, 38, 73, 0.3);
$dblue-medium: rgba(16, 38, 73, 0.5);
$dblue-dark: rgba(16, 38, 73, 0.8);

// $lblue: rgb(218, 230, 247);
// $lblue-light: rgba(218, 230, 247, 0.3);
// $lblue-medium: rgba(218, 230, 247, 0.5);
// $lblue-dark: rgba(218, 230, 247, 0.8);

$grey: rgb(74, 74, 74);
$grey-light: rgba(74, 74, 74, 0.2);
$grey-medium: rgba(74, 74, 74, 0.5);
$grey-dark: rgba(74, 74, 74, 0.8);
$light: #94a7b7;

// $green: rgb(72, 206, 176);

// $white: rgb(255, 255, 255);
// $black: rgb(0, 0, 0);

$danger: #ff5661;
$success: #1cce74;
$light-yellopw: #fff8e1;
$accent: #7b61ff;

// Spinners **/

$spinner-width: 2rem !default;
$spinner-height: $spinner-width !default;
$spinner-border-width: 0.25em !default;

$spinner-width-sm: 1rem !default;
$spinner-height-sm: $spinner-width-sm !default;
$spinner-border-width-sm: 0.2em !default;
$spinner-border-width-xs: 0.1em !default;
