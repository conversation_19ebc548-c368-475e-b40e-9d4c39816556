import { FormikProps } from 'formik';
import { DefaultMerchantType } from './global-store-types';

export interface IBusinessProfileViewProps {
  defaultMerchant: Partial<DefaultMerchantType>;
  businessProfile:
    | {
        business_description?: string;
        website?: string;
        incorporated_trustee_category?: {
          label: string;
        };
        industry?: {
          label: string;
        };
      }
    | undefined;
  setIsEditing: (isEditing: boolean) => void;
  defaultBusiness: {
    business_type: string | undefined | null;
    details: {
      contact: {
        support_phone: string;
        support_email: string;
      };
      business_profile: {
        incorporated_trustee_category: {
          label: string;
        };
        industry: {
          label: string;
        };
        business_description: string;
        website?: string;
      };
    };
  };
}

export interface IBusinessProfileFormProps {
  initialValues: {
    description: string;
    email: string;
    website: string;
    phone_number: string;
    country: string;
    business_name: string;
  };
  handleSubmit: (values: IBusinessProfileFormProps['initialValues']) => void;
  isLoading: boolean;
  setIsEditing: (isEditing: boolean) => void;
  initializing: boolean;
  formikRef?: React.RefObject<FormikProps<IBusinessProfileFormProps['initialValues']>>;
}
