import { PaymentMethodType } from './common';
import { BanksType } from './transaction-types';

export type SplitAccountsSummaryType = {
  account_number: string;
  account_name: string;
  bank_code: string;
  bank_name: string;
  total_settlement: string;
  last_settlement_date: string;
  reference: string;
};

export type SplitPaymentsType = {
  status: string;
  settlement_payout_reference: string;
  recipient_name: string;
  recipient_number: string;
  settlement_payout_amount: string;
  transaction_date: string;
};

export type SingleSplitAccountSummary = SplitAccountsSummaryType & {
  created_at: string;
};

export type CreateSplitPaymentResponseType = {
  name: string;
  description: string;
  split_type: string;
  currency: string;
  reference: string;
};

export type CreateSplitAccountResponseType = {
  reference: string;
  account_number: string;
  account_name: string;
  bank_code: string;
  bank_name: string;
  relationship: string;
};

export type CreateSplitAccountSendOtpResponseType = {
  auth_data: {
    type: 'otp';
    identifier: string;
    service_identifier: string;
  };
};

export type SingleSplitPaymentResponseType = {
  status: string;
  trace_id: null;
  amount: string;
  fee: string;
  vat: string;
  reference: string;
  created_at: string;
  completed_at: string;
  description: string;
  recipient_information: {
    bank_name: BanksType;
    account_name: string;
    account_number: string;
    payment_method: PaymentMethodType;
  };
  amount_charged: string;
  settlement: {
    channel: string;
    currency: string;
    reference: string;
  };
};

export type SplitSettingsResponseType = {
  enabled: boolean;
  split_account: { max_count: number; max_total_rate: number; min_settlement_amount: number };
};

export type SplitType = 'flat' | 'percentage' | '';

export type SplitPaymentAccountsArrayType = {
  value: string;
  split_destination_type: string;
  split_account_reference: string;
};

export type SplitRulePayloadType = {
  currency: string;
  split_name: string;
  split_description: string;
  split_type: SplitType;
  split_accounts?: SplitPaymentAccountsArrayType[];
};

export type CreateSplitAccountType = {
  currency: string;
  account_number: string;
  bank_code: string;
};

export type SingleSplitRuleType = Omit<SplitRulePayloadType, 'split_accounts'> & {
  split_reference: string;
  split_accounts: {
    value: string;
    split_destination_type: string;
    split_destination: {
      reference: string;
      account_number: string;
      account_name: string;
      bank_code: string;
      bank_name: string;
    };
  }[];
};
