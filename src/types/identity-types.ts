import { IResponse } from './api-services-types';

export interface IEventSummary {
  totalVerifications: number;
  successCount: number;
  failedCount: number;
  pendingCount: number;
  kycCount: number;
  kybCount: number;
  successRate: number;
}

export interface IdentityResponseList<T> {
  data: IResponse<T>;

  isFetching: boolean;
  refetch: () => void;
}

export interface IdentityResponse<T> {
  data: {
    data: T;
  };
  isFetching: boolean;
  refetch: () => void;
}

export type IdentityServiceAccessType = {
  has_access: boolean;
  has_declined_request: boolean;
  has_pending_request: boolean;
  active: boolean;
  attempts?: number;
  max_attempts?: number;
};

export type MetaDataType = {
  [key in 'document_details' | 'files']: Array<{
    key: string;
    description: string;
  }>;
};
export type VerificationMerchantType = {
  reference: string;
  name: string;
  email: string;
};

export type VerificationDataType = {
  reference: string;
  id: string;
  type: string;
  class: string;
  identity_type: string;
  identity_type_label: string;
  identity_type_description: string;
  full_name: string;
  country: string;
  requested_by: string;
  merchant: VerificationMerchantType;
  status: string;
  result: string;
  date_created: string;
  metadata: MetaDataType;
};
export type BillingHistoryDataType = {
  reference: string;
  verification_reference: string;
  amount: number;
  currency: string;
  narration: string;
  verification_type: string;
  verification_class: string;
  type: string;
  identity_type: string;
  status: string;
  date_created: string;
};
