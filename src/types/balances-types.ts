import React from 'react';

import { BalancesDataType, DefaultMerchantType, FetchBanksResponseType } from '+types';

export interface WithdrawProps {
  banks: FetchBanksResponseType[];
  currency: string;
  balances: BalancesDataType;
  refetchBalance: () => void;
  isFetchingBanks: boolean;
  bankPayoutLimit: number;
  walletPayoutLimit: number;
  minBankPayoutLimit: number;
  minWalletPayoutLimit: number;
  minMobileMoneyLimit: number;
  mobileMoneyLimit: number;
  disabled: boolean;
  payoutLimitDetails: Partial<DefaultMerchantType>['payout_limits'];
  settlementAccounts: Record<string, unknown[]>;
  bankWithdrawalLimit: unknown;
  balanceCount: number;
}

export type BankAccount = {
  bank?: string;
  account?: string;
  account_name?: string;
  bank_name?: string;
  beneficiary_type?: string;
  first_name?: string;
  last_name?: string;
  account_type?: string;
  address_information?: {
    country: string;
    state: string;
    city: string;
    zip_code: string;
    street: string;
    full_address: string;
  };
  payment_method?: string;
  routing_number?: string;
  sender_name?: string;
  business_name?: string;
  swift_bic?: string;
  iban?: string;
  sort_code?: string;
  account_number_type?: string;
  intermediary_routing_number?: string;
};

export type MobileMoneyType = {
  operator: string;
  mobile_number: string;
};

export type Customer = {
  name?: string;
  email?: string;
};

export type Destination = {
  type: 'bank_account' | 'mobile_money';
  amount: number | null;
  narration: string;
  currency: string;
  bank_account?: BankAccount;
  mobile_money?: MobileMoneyType;
  customer: Customer;
};

export type TransferData = {
  two_factor_type: string;
  reference: string;
  destination: Destination;
};

export type Bank = {
  code: string;
  name: string;
};

export interface IPayoutsModalProps {
  close?: () => void;
  currency: string;
  balance?: number;
  initiateTransfer?: Record<string, unknown>;
  processTransfer?: Record<string, unknown>;
  resendOTP?: Record<string, unknown>;
  paymentRef?: string;
  identifier?: string;
  minPayoutLimit?: number;
  maxPayoutLimit?: number;
  get2FAType?: (twoFactorType?: string) => void;
}

export interface BankPayoutsModalProps extends IPayoutsModalProps {
  identifier: string;
  banks: Bank[];
  isFetchingBanks: boolean;
  settlementAccounts?: Record<string, unknown>;
  withdrawalLimit?: {
    settlement_account: number;
    other_accounts: number;
  };
  networkOperator: string[];
  type: 'otp' | 'totp';
  onFormSubmit?: () => void;
  requireApproval: boolean;
}

export type MobileMoneyOperator = {
  id: string;
  name: string;
  slug: string;
  code: string;
  min?: number;
  max?: number;
  country?: string;
};

export interface MobilePayoutsModalProps {
  close: () => void;
  currency: string;
  balance?: number;
  initiateTransfer: Record<string, unknown>;
  processTransfer: Record<string, unknown>;
  resendOTP: Record<string, unknown>;
  paymentRef: string;
  identifier: string;
  minPayoutLimit: number;
  maxPayoutLimit: number;
  get2FAType?: () => void;
}

export type WithdrawalDetails = {
  amount: number | null;
  description: string | null;
  pin: string;
  mobileNo?: string;
  operatorCode?: string;
  username: string;
  mobileMoneyCode?: string;
  bankNumber?: string;
  bankName?: string;
};

export type MobileNetworkDataType = Array<{
  name?: string;
  slug?: string;
  country?: string;
  code?: string;
  status?: string;
  available: boolean;
}>;

type PaymentMethod = {
  type: string;
  label: string;
};

export type CurrencyData = {
  currency: string;
  paymentMethods: PaymentMethod[];
};

export type Country = {
  name: string;
  iso2: string;
  states: string[];
};

export type StatesListType = {
  [key: string]: string[];
};

export type CountryList = {
  value: string;
  label: string;
};

type InitiateTransferResponseType = {
  status: string;
  payment_reference: string;
  authData: {
    type: string;
    identifier: string;
  };
};

export type InitiateTransferType = {
  mutateAsync: (data: unknown) => Promise<InitiateTransferResponseType>;
};

type ProcessTransferResponseType = {
  id: number;
  status: string;
  amount: string;
  fee: string;
  vat: string;
  merchant_bears_cost: boolean;
  currency: string;
  reference: string;
  unique_reference: string;
  message: string;
  createdAt: string;
  payment_destination_type: 'mobile_money' | 'bank_account' | 'wallet';
  approval_required: boolean;
  requires_approval: boolean;
};

export type ProcessTransferType = {
  mutateAsync: (data: {
    payment_reference: string | undefined;
    auth_data: {
      two_factor_type: string;
      identifier: string;
      code: string;
    };
  }) => Promise<ProcessTransferResponseType>;
};

export type FormValuesType = {
  first_name: string;
  last_name: string;
  sender_name: string;
  sender_email: string;
  business_name: string;
  payment_method: string;
  account_number: string;
  swift_bic: string;
  routing_number: string;
  bank_name: string;
  country: string;
  recipient_address: string;
  state: string;
  city: string;
  zip_code: string;
  account_number_type: string;
  sort_code?: string;
  iban?: string;
  beneficiary_type: string;
  fps?: string;
  account_type?: string;
  intermediary_routing_number?: string;
};

export type FormTouchedType = Partial<Record<keyof FormValuesType, boolean>>;
export type FormErrorsType = Partial<Record<keyof FormValuesType, string>>;

export type RequestFormPropsType = {
  values: FormValuesType;
  touched: FormTouchedType;
  errors: FormErrorsType;
  isValid: boolean;
  getFieldProps: (name: keyof FormValuesType) => {
    name: string;
    value: string | undefined;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onBlur: () => void;
  };
  setFieldValue: (name: keyof FormValuesType, value: string) => void;
  handleBlur: (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => void;
};

export type ForeignBankProps = {
  countriesList?: Array<{ value: string; label: string; isoCode?: string }>;
  statesList?: Record<string, string[]>;
  onFormSubmit?: () => void;
  currency: string;
  onDirtyChange?: (isDirty: boolean) => void;
  onFormChange?: (formValues: FormValuesType) => void;
  onValidityChange?: (isValid: boolean) => void;
};

export type CountryItemType = { value: string; label: string; isoCode: string };

export type ForeignPayoutsPropsType = ForeignBankProps & {
  onValidityChange?: (isValid: boolean) => void;
  currency?: string;
  onFormChange?: (formValues: FormValuesType) => void;
};

export type FormRefType = {
  isDirty?: () => boolean;
  resetForm?: () => void;
  getFormValues?: () => FormValuesType;
};

export type BuildBankAccountParams = {
  withdrawalDetails: WithdrawalDetails;
  currency: string;
  isForeignTrx: boolean;
  foreignFormValues: FormValuesType;
  foreignFormType: 'local' | 'international';
};
