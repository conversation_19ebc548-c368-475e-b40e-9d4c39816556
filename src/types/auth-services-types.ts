import { CountryCode } from 'libphonenumber-js';

export type SignInResponseType = {
  token_type: 'Bearer' | 'auth';
  expires_in: number;
  access_token: string;
  refresh_token: string;
  refresh_token_expires_in: number;
  impossible_travel_detected: boolean;
  two_factor_auth: {
    required: boolean;
    identifier: string;
    type: string;
  };
  account: {
    id: number;
    email: string;
    firstname: string;
    lastname: string;
    two_factor_enabled: boolean;
    merchants: Array<{
      id: number;
      name: string;
      email: string;
      description: string;
      avatar: string;
      status: 'active' | 'inactive';
      kyc_status: 'pending' | 'approved' | 'rejected' | 'verified' | 'unverified';
      is_owner: boolean;
      env: 'live' | 'demo' | 'test';
      meta_data: {
        show_kyc_page_after_login: boolean;
      };
      payin_limits: Record<
        string,
        {
          min: number;
          max: number;
        }
      >;
    }>;
  };
  bvn: {
    feedbackMessage: string;
    showRequestPage: boolean;
  };
  enhanced_kyc: {
    is_enabled: boolean;
  };
};

export type SignInFormType = {
  email: string;
  password: string;
};

export type VerifyTokenResponseType = {
  is_supported_country: boolean;
  country_name: string;
  merchant_name: string;
  email: string;
};

export type AccessControlType = {
  collection: {
    card: boolean;
    bankTransfer: boolean;
    payWithBank: boolean;
    disbursementWallet: boolean;
    virtualBankAccount: boolean;
  };
  disbursement: {
    bankAccount: boolean;
    mobileMoney: boolean;
    disbursementWallet: boolean;
  };
};

export type ErrorResponseType = {
  response?: {
    data?: {
      error?: string;
      message?: string;
      [key: string]: unknown;
    };
  };
};

export type AuthDataType = {
  code: string;
  identifier: string;
  two_factor_type: string;
};

export interface ISignupData {
  email: string;
  first_name: string;
  last_name: string;
  bname: string;
  btype: string;
  industry: string;
  phone_number: string;
  country: string;
  phone_country_code: string;
  password: string;
  confirm_password: string;
  sra: string;
  tandc: string;
  referral?: {
    referral_code?: string;
    provider_referral_code?: string;
  };
}

export type SignupFirstStageDataType = {
  btype: string;
  industry: string;
  country: string;
  countryCode: CountryCode;
  registeredBusinessType: string;
};

export type BusinessTypes = 'individual' | 'registered_business_sme' | 'registered_business_non_sme' | 'ngo';

export type BusinessTypesRequirementsType = {
  country: string;
  business_types: {
    [key in BusinessTypes]: {
      description: string;
      documents: string[];
    };
  };
};
export type OptionType = { value: number | string; label: string; isoCode?: string; country?: string; regex?: string };
