import { IRVCMerchantDetailsResponse } from './card-issuance-types';
import { CurrencyType } from './common';
import { BalancesDataType } from './transaction-types';

/* eslint-disable no-unused-vars */

export type ProductAccessType<T = Partial<Record<CurrencyType, boolean>>> = {
  card_issuance: T;
  identity: T;
  pool_account: T;
};

interface ICurrencyLimits {
  min: number;
  max: number;
}

interface ICurrencyConversions {
  [currency: string]: ICurrencyLimits;
}

interface IConversionData {
  [baseCurrency: string]: ICurrencyConversions;
}

export type DefaultMerchantType = {
  id: number;
  avatar: string | null;
  description: string;
  email: string;
  userEmail: string;
  fees: string[];
  available_currency: Array<CurrencyType>;
  name: string;
  risk_level: 'low_risk' | 'medium_risk' | 'high_risk';
  env: 'live' | 'test';
  can_go_live: boolean;
  status: 'active' | 'inactive';
  kyc_status: 'verified' | 'pending' | 'unverified';
  productAccess: ProductAccessType;
  country: {
    id: number;
    name: string;
    iso2: string;
    is_active: boolean;
  };
  payout_limit: number;
  payout_limits: {
    mobile_money: Record<Partial<CurrencyType>, { min: number; max: number }>;
    bank_account: Record<Partial<CurrencyType>, { min: number; max: number }>;
    disbursement_wallet: Record<Partial<CurrencyType>, { min: number; max: number }>;
    bulk_bank_account: Record<Partial<CurrencyType>, { min: number; max: number }>;
    bulk_mobile_money: Record<Partial<CurrencyType>, { min: number; max: number }>;
  };
  bulk_payout_limits: {
    min_allowed_bulk_payouts: number;
    max_allowed_bulk_payouts: number;
  };
  withdrawal_limits: {
    bank_account: Record<Partial<CurrencyType>, { min: number; max: number }>;
  };
  payin_limits: Record<Partial<CurrencyType>, { min: number; max: number }>;
  checkout_limits: Record<Partial<CurrencyType>, { min: number; max: number }>;
  issuing_wallet_limits: Record<Partial<CurrencyType>, { min: number; max: number }>;
  conversion_limits?: IConversionData;
};

type ProfileType = Partial<{
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar: string;
  created: string;
}>;

export type TierLevelType = {
  name: string;
  description: string;
  id: number;
};

export type ProductLimitType = {
  min: number;
  max: number;
  daily: number;
};

export interface MerchantSlice {
  is2FactorEnabled: boolean;
  sessionActive: boolean;
  canGoLive: boolean;
  merchantEnv: string;
  profile: ProfileType;
  defaultMerchant: Partial<DefaultMerchantType>;
  bulkPayoutDraftData: [
    {
      reference: string;
      data: [];
      newUpdate: boolean;
    }
  ];
  tierLevel: TierLevelType | null;
  productTierLimit: number;
  merchantBalanceDetails: Partial<BalancesDataType>;
}

// CARD ISSUANCE
type CardType = 'reserved' | 'customer';
export type AccessStatusType = 'active' | 'inactive' | 'pending' | 'success' | 'suspended' | 'disabled' | 'enabled';
type CardAccessType = {
  customer: { status: Exclude<AccessStatusType, 'success'> };
  reserved: { status: Exclude<AccessStatusType, 'success'> };
};
type CurrencyAccessType = {
  [key: string]: { status: Exclude<AccessStatusType, 'success'> };
};
export type AccessResponseType = {
  category: CardAccessType;
  currency: CurrencyAccessType;
};
export type WalletBalanceType = Partial<{ [K in CurrencyType]: { available_balance: number; low_balance_threshold: number | null } }>;
export type CardFeesType = {
  [K in CardType]: {
    issuance: number;
    chargebacks: number;
  };
};

export interface IssuanceSlice {
  cardAccess: CardAccessType;
  currencyAccess: CurrencyAccessType;
  merchantDetails: IRVCMerchantDetailsResponse;
  states: Array<{ label: string; value: string }>;
  walletBalance: WalletBalanceType;
  cardFees: CardFeesType;
  addCardAccess: (arg: CardAccessType) => void;
  addCurrencyAccess: (arg: CurrencyAccessType) => void;
  addCardFees: (arg: CardFeesType) => void;
  addWalletBalance: (arg: WalletBalanceType) => void;
  addStates: (arg: Array<{ label: string; value: string }>) => void;
  addMerchantDetails: (arg: IRVCMerchantDetailsResponse) => void;
}
