import React from 'react';

import { TwoFactorAuthType } from '+types';

export type SubtabKeyType = 'single_payout' | 'bulk_payout';

export type actionType = 'addInitiator' | 'reviewInitiator' | 'addApprover' | 'reviewApprover';
export type payoutType = 'single_payout' | 'bulk_payout' | null;

export type SetDisabledType = 'addInitiator' | 'reviewInitiator' | 'addApprover' | 'reviewApprover';

interface Role {
  name: string;
  slug: string;
}

type AuthType = {
  code: string;
  identifier: string;
  two_factor_type: TwoFactorAuthType;
};

export interface IFetchedUser {
  id: number;
  firstname: string;
  lastname: string;
  kora_id: number | string;
  email?: string;
  phoneNumber?: string | null;
  role?: Role;
}

export interface IFilteredUsers {
  id?: number;
  firstname: string;
  lastname: string;
  email?: string;
}

export type defaultInitiatorsStateType = {
  searchTerm: string;
  designatedInitiators: string[];
  filteredMembers: IFilteredUsers[];
  showAll: boolean;
  loading: boolean;
  error: string;
};

export type defaultApproversStateType = {
  searchTerm: string;
  designatedApprovers: string[];
  filteredMembers: IFilteredUsers[];
  showAll: boolean;
  loading: boolean;
  error: string;
};

export interface ApprovalRowProps {
  workflow_reference: string;
  firstname: string;
  email: string;
  workflow_type: string;
  createdAt: string;
  id: string;
  [key: string]: string;
}

export interface IremoveApprovalDetails {
  reference: string;
  data: {
    type: string;
    initiators?: {
      user_id: string;
      user_kora_id: string;
    }[];
    approvers?: {
      user_id: string;
      user_kora_id: string;
    }[];
  };
}

export interface IApprovalWorkflowModal {
  close?: () => void;
  visible?: boolean;
  payoutType?: payoutType;
  initiatorState: {
    searchTerm: string;
    designatedInitiators: string[];
    filteredMembers: IFilteredUsers[];
    showAll: boolean;
    loading: boolean;
    error: string;
  };
  setInitiatorState: React.Dispatch<
    React.SetStateAction<{
      searchTerm: string;
      designatedInitiators: string[];
      filteredMembers: IFilteredUsers[];
      showAll: boolean;
      loading: boolean;
      error: string;
    }>
  >;
  approverState: {
    searchTerm: string;
    designatedApprovers: string[];
    filteredMembers: IFilteredUsers[];
    showAll: boolean;
    loading: boolean;
    error: string;
  };
  setApproverState: React.Dispatch<
    React.SetStateAction<{
      searchTerm: string;
      designatedApprovers: string[];
      filteredMembers: IFilteredUsers[];
      showAll: boolean;
      loading: boolean;
      error: string;
    }>
  >;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>, type: 'initiator' | 'approver') => void;
  handleSelectApprover: (name: string) => void;
  handleRemoveApprover: (name: string) => void;
  handleSelectInitiator: (name: string) => void;
  handleRemoveInitiator: (name: string) => void;
  handleOtp: (e: React.ChangeEvent<HTMLInputElement>) => void;
  view: {
    visibleMembers: string[];
    hiddenMembers: string[];
    visibleApprovers: string[];
    hiddenApprovers: string[];
  };
  triggerInitiateApprovalWorkflow: () => void;
  createApprovalWorkflow: () => void;
  isSuccess: boolean;
  authState: AuthType;
}

export interface IInitiator {
  id: number;
  kora_id: number;
  email: string;
  firstname: string;
  lastname: string;
}

export interface IApprover {
  id: number;
  kora_id: number;
  email: string;
  firstname: string;
  lastname: string;
}

export interface IApprovalWorkflow {
  data: {
    reference: string;
    type: string;
    initiators: IInitiator[];
    approvers: IApprover[];
    enabled: boolean;
    createdAt: string;
    updatedAt: string;
  }[];
}

export interface IApproverAccordionProps {
  close?: () => void;
  approversCount: number;
  approvalWorkflows: IApprovalWorkflow;
  isLoading: boolean;
  refetchTableData: () => void;
  searchQueryValue: { [key: string]: string };
  setQuery: (query: { [key: string]: string }) => void;
  isModalOpen: boolean;
  setIsModalOpen: (isOpen: boolean) => void;
  approverState: {
    searchTerm: string;
    designatedApprovers: string[];
    filteredMembers: IFilteredUsers[];
    showAll: boolean;
    loading: boolean;
    error: string;
  };
  setApproverState: React.Dispatch<
    React.SetStateAction<{
      searchTerm: string;
      designatedApprovers: string[];
      filteredMembers: IFilteredUsers[];
      showAll: boolean;
      loading: boolean;
      error: string;
    }>
  >;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>, type: 'initiator' | 'approver') => void;
  handleSelectApprover: (name: string) => void;
  handleRemoveApprover: (name: string) => void;
  handleOtp: (e: React.ChangeEvent<HTMLInputElement>) => void;
  view: {
    visibleApprovers: string[];
    hiddenApprovers: string[];
  };
  resetApproverState: () => void;
  initiateUpdateApprovers: () => void;
  authorizeUpdateApprovers: (refetchSingleApproversWorkflow: () => void, refetchBulkApproversWorkflow: () => void) => Promise<void>;
  refetchApprovalWorkflows: () => void;
  handleUpdateProductRef: (reference: string) => void;
  workflowReference: { single_payout: string; bulk_payout: string };
  authState: AuthType;
}

export interface InitiatorAccordionProps {
  close?: () => void;
  initiatorsCount: number;
  approvalWorkflows: IApprovalWorkflow;
  isLoading: boolean;
  refetchTableData: () => void;
  searchQueryValue: { [key: string]: string };
  setQuery: (query: { [key: string]: string }) => void;
  isModalOpen: boolean;
  setIsModalOpen: (isOpen: boolean) => void;
  initiatorState: {
    searchTerm: string;
    designatedInitiators: string[];
    filteredMembers: IFilteredUsers[];
    showAll: boolean;
    loading: boolean;
    error: string;
  };
  setInitiatorState: React.Dispatch<
    React.SetStateAction<{
      searchTerm: string;
      designatedInitiators: string[];
      filteredMembers: IFilteredUsers[];
      showAll: boolean;
      loading: boolean;
      error: string;
    }>
  >;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>, type: 'initiator' | 'approver') => void;
  handleSelectInitiator: (name: string) => void;
  handleRemoveInitiator: (name: string) => void;
  handleOtp: (e: React.ChangeEvent<HTMLInputElement>) => void;
  view: {
    visibleMembers: string[];
    hiddenMembers: string[];
  };
  resetInitiatorState: () => void;
  initiateUpdateInitiators: () => void;
  refetchApprovalWorkflows: () => void;
  authorizeUpdateInitiators: (refetchSingleInitiatorsWorkflow: () => void, refetchBulkInitiatorsWorkflow: () => void) => Promise<void>;
  handleUpdateProductRef: (reference: string) => void;
  workflowReference: { single_payout: string; bulk_payout: string };
  authState: AuthType;
}

export interface IManageApprovalWorkflowModal {
  close: () => void;
  visible: boolean;
  onToggle: () => void;
  actionType: 'ENABLE' | 'DISABLE';
  authState: AuthType;
  otp: string;
  setOtp: (otp: string) => void;
  triggerInitiateManageApprovalWorkflow: () => Promise<void>;
  authorizeManageApprovalWorkflow: ({ enabled }: { enabled: boolean }) => Promise<void>;
  handleOtpResend: () => Promise<void>;
}

export interface ToggleButtonProps {
  isOn: boolean;
  isLoading: boolean;
  onToggle: () => void;
}

export interface IInitiateApprovalWorkflowData {
  data: {
    data: {
      reference: string;
      auth: {
        identifier: string;
        two_factor_type: TwoFactorAuthType;
      };
    };
  };
}

export interface IInitiateManageApprovalWorkflowData {
  data: {
    data: {
      reference: string;
      enabled: boolean;
      auth: {
        identifier: string;
        two_factor_type: TwoFactorAuthType;
      };
    };
  };
}

export interface RemoveApprovalWorkflowType {
  close: () => void;
  type?: string;
  removeApprovalDetails: IremoveApprovalDetails | undefined;
  refetchBulkInitiatorsWorkflow?: () => void;
  refetchSingleInitiatorsWorkflow?: () => void;
  refetchBulkApproversWorkflow?: () => void;
  refetchSingleApproversWorkflow?: () => void;
  refetchApprovalWorkflows?: () => void;
}

export interface IUpdateInitiatorsProps {
  close: () => void;
  initiatorState: {
    searchTerm: string;
    designatedInitiators: string[];
    filteredMembers: IFilteredUsers[];
    showAll: boolean;
    loading: boolean;
    error: string;
  };
  setInitiatorState: React.Dispatch<
    React.SetStateAction<{
      searchTerm: string;
      designatedInitiators: string[];
      filteredMembers: IFilteredUsers[];
      showAll: boolean;
      loading: boolean;
      error: string;
    }>
  >;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>, type: 'initiator' | 'approver') => void;
  handleSelectInitiator: (name: string) => void;
  handleRemoveInitiator: (name: string) => void;
  handleOtp: (e: React.ChangeEvent<HTMLInputElement>) => void;
  view: {
    visibleMembers: string[];
    hiddenMembers: string[];
  };
  initiateUpdateInitiators: () => void;
  handleUpdateInitiators: () => Promise<void>;
  approvalWorkflows: IApprovalWorkflow;
  handleUpdateProductRef: (reference: string) => void;
  authState: AuthType;
}

export interface IUpdateApproversProps {
  close: () => void;
  approverState: {
    searchTerm: string;
    designatedApprovers: string[];
    filteredMembers: IFilteredUsers[];
    showAll: boolean;
    loading: boolean;
    error: string;
  };
  setApproverState: React.Dispatch<
    React.SetStateAction<{
      searchTerm: string;
      designatedApprovers: string[];
      filteredMembers: IFilteredUsers[];
      showAll: boolean;
      loading: boolean;
      error: string;
    }>
  >;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>, type: 'initiator' | 'approver') => void;
  handleSelectApprover: (name: string) => void;
  handleRemoveApprover: (name: string) => void;
  handleOtp: (e: React.ChangeEvent<HTMLInputElement>) => void;
  view: {
    visibleApprovers: string[];
    hiddenApprovers: string[];
  };
  initiateUpdateApprovers: () => void;
  handleUpdateApprovers: () => Promise<void>;
  approvalWorkflows: IApprovalWorkflow;
  handleUpdateProductRef: (reference: string) => void;
  authState: AuthType;
}

export type ViewType = {
  hiddenMembers: string[];
  hiddenApprovers: string[];
};

export type GetAddInitiatorContentProps = {
  initiatorSearchTerm: string;
  errorInitiators: string | null;
  loadingInitiators: boolean;
  filteredInitiatorMembers: { id: string; firstname: string; lastname: string }[];
  designatedInitiators: string[];
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>, type: string) => void;
  handleSelectInitiator: (name: string) => void;
  handleRemoveInitiator: (name: string) => void;
  view: ViewType;
  setInitiatorState: React.Dispatch<React.SetStateAction<defaultInitiatorsStateType>>;
  showAllInitiators: boolean;
};

export type GetReviewInitiatorContentProps = {
  designatedInitiators: string[];
  handleRemoveInitiator: (name: string) => void;
};

export type GetAddApproverContentProps = {
  approverSearchTerm: string;
  errorApprovers: string | null;
  loadingApprovers: boolean;
  filteredApproverMembers: { id: string; firstname: string; lastname: string }[];
  designatedApprovers: string[];
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>, type: string) => void;
  handleSelectApprover: (name: string) => void;
  handleRemoveApprover: (name: string) => void;
  view: ViewType;
  setApproverState: React.Dispatch<React.SetStateAction<defaultApproversStateType>>;
  showAllInitiators: boolean;
};

export type GetReviewApproverContentProps = {
  designatedInitiators: string[];
  designatedApprovers: string[];
  handleRemoveInitiator: (name: string) => void;
  handleRemoveApprover: (name: string) => void;
  authState: { two_factor_type: string };
  otpLabel: string;
  otpPlaceholder: string;
  merchantEmail: string;
  handleOtp: (e: React.ChangeEvent<HTMLInputElement>) => void;
  setOtp: (otp: string) => void;
};
