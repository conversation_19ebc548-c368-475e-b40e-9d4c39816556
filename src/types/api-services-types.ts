export interface IVerificationDetails {
  number: string;
  type: string;
  country: string;
}

export interface IBVNDetails {
  bvn: string;
}

export interface IMobileNameEnquiry {
  mobileMoneyCode: string;
  phoneNumber: string;
  currency: string;
}

export interface IBankNameEnquiry {
  bank: string;
  account: string;
  currency: string;
}

export interface IAPIErrorResponse<T = null> {
  message: string;
  error: string;
  data: T;
}

export interface IPaging {
  total_items: number;
  page_size: number;
  current: number;
  count: number;
  next: number;
}

export interface IResponse<T> {
  /** The API response data. */
  data: {
    data: T;
    paging: IPaging;
    links: Array<{
      href: string;
      rel: 'current' | 'next';
      method: 'GET';
    }>;
  };
  /** The API response status. */
  status: boolean;
  /** The API response message. */
  message: string;
  paging?: IPaging;
}

export interface ISendResponse<T> {
  /** The API response data. */
  data: T;
  /** The API response status. */
  status: boolean;
  /** The API response message. */
  message: string;
}
