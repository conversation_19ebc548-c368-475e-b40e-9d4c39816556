import React from 'react';
import { FormikProps } from 'formik';

import { IOption } from './common';

export interface IOperators {
  name: string;
  slug: string;
  code: string;
  min: number;
  max: number;
  country: string;
}

export interface IOptions {
  value: string;
  label: string;
}

export type RoleType = {
  label: string;
  id: string;
};

export interface IBanks {
  name: string;
  slug: string;
  code: string;
  country: string;
  nibss_bank_code: string;
}

export type File = {
  name?: string;
  description?: string;
  url?: string;
};

export type DocumentFileType = {
  type: string;
  file: File;
};
export interface IKYCMetaData {
  industries: { industries: Array<Omit<IOption, 'value'>> };
  incorporated_trustee_categories: { incorporated_trustee_categories: Array<Omit<IOption, 'value'>> };
  representative_roles: Array<RoleType>;
  sme_types: { sme_types: Array<Omit<IOption, 'value'>> };
}

export interface IMerchantKycInfo {
  merchant_id: number;
  business_type: string;
  bvn_required: boolean;
  details: {
    contact: {
      support_phone: string;
    };
    documents: Array<{
      id?: string;
      file: {
        name: string;
        description: string;
        url: string;
      };
      type: string;
      is_additional_document: boolean;
    }>;
    validation: {
      bvn?: {
        dob: {
          match: boolean;
          value: string;
        };
        name: {
          match: boolean;
          value: string;
        };
        phone: {
          match: boolean;
          value: string;
        };
        number: string;
        is_valid: boolean;
      };
      nin?: {
        dob: {
          match: boolean;
          value: string;
        };
        name: {
          match: boolean;
          value: string;
        };
        number: string;
        is_valid: boolean;
      };
      voters_card?: {
        dob: {
          match: boolean;
          value: string;
        };
        name: {
          match: boolean;
          value: string;
        };
        number: string;
      };
      international_passport?: {
        dob: {
          match: boolean;
          value: string;
        };
        name: {
          match: boolean;
          value: string;
        };
        number: string;
      };
    };
    business_profile: {
      website: string;
      industry: {
        id: string;
        label: string;
      };
      date_of_birth: string;
      business_description: string;
      business_address_details: {
        lga: string;
        city: string;
        state: string;
        landmark: string;
        address_line: string;
      };
    };
    validation_result: {
      nin?: {
        dob: {
          match: boolean;
          value: string;
        };
        name: {
          match: boolean;
          value: string;
        };
        number: string;
        is_valid: boolean;
      };
    };
    settlement_accounts: Record<string, unknown>;
  };
  compliance: {
    feedback: {
      documents?: Array<{
        note: string;
        date_added: string;
      }>;
      business_profile?: Array<{
        note: string;
        date_added: string;
      }>;
      settlement_accounts?: Array<{
        note: string;
        date_added: string;
      }>;
      representatives?: Array<{
        note: string;
        date_added: string;
      }>;
    };
    requirements: {
      documents?: {
        status: string;
      };
      business_type?: {
        status: string;
      };
      business_profile?: {
        status: string;
      };
      settlement_accounts?: {
        status: string;
      };
      representatives?: {
        status: string;
      };
    };
    last_review_date: string;
    flagged_categories: string[];
    submitted_for_review_at: string;
    last_submitted_categories: string[];
    status: string;
  };
}

export interface IKycResponse {
  message: string;
  status: boolean;
  data: IMerchantKycInfo;
}

export interface IKycForm {
  merchantCountry: string;
  kycStage: string;
  businessType: string;
  updateStage: (num: number) => void;
  dataClearFeedback?: boolean;
  updateSubmittedKyc: (data: IMerchantKycInfo) => void;
}

export interface IIndividualBusinessProfileValues {
  date_of_birth: string;
  website: string;
  business_description?: string;
  address_line: string;
  LGA: string;
  city: string;
  landmark: string;
  state: string;
  industry: string;
}

export interface INGOBusinessProfileValues {
  incorporated_trustee_category: string;
  business_description?: string;
  address_line: string;
  LGA: string;
  city: string;
  landmark: string;
  state: string;
  website: string;
  industry: string;
}

export interface ISettlementAccountValues {
  isBankAccount?: string;
  currency: string;
  bvn?: string;
  operator?: string;
  mobile_number?: string;
  bank?: string;
  bank_code?: string;
  account_number: string;
  account_name: string;
}

export type AddressType = {
  LGA?: string;
  city?: string;
  state?: string;
  landmark?: string;
  address_line?: string;
};
export interface IRegisteredBusinessProfileValues {
  website: string | null;
  sme_type: string;
  amount?: number;
  currency?: string;
  address_line?: string;
  LGA?: string;
  city?: string;
  landmark?: string;
  state?: string;
  business_address?: string;
  expected_monthly_income?: {
    amount: number;
    currency?: string;
  };
  business_address_details?: AddressType;
}

export type CertificateOfIncorporationType = {
  id?: string;
  type: string;
  file: File;
};

export interface IHandleIDNumberValues {
  idNumber?: string;
  certificate_of_incorporation: CertificateOfIncorporationType;
}

export interface IDocumentsValues {
  idNumber?: string;
  ke_tax_pin?: string;
  certificate_of_incorporation: CertificateOfIncorporationType;
  additional_documents?: DocumentFileType[];
}

export type DocumentToSendType = CertificateOfIncorporationType | DocumentFileType | { type: 'ke_tax_pin'; id: string };

export type DocumentType = {
  id?: string;
  file: File;
  type: string;
  is_additional_document: boolean;
  role?: {
    label: string;
  };
  name?: string;
  documents?: { file: File }[];
};

export type IdResponseType = {
  label: string;
  value: string;
  regex: string;
  country: string;
};

export type DocumentFormValuesType = {
  idType?: IdResponseType;
  idNumber?: string;
  vnin?: string;
  validIdName?: string;
  validIdDescription?: string;
  proofOfAddressName?: string;
  proofOfAddressDescription?: string;
  additional_documents: Array<{ type: string; file: { name: string; description: string } }>;
};

export type DocumentsPropsType = {
  submittedDocuments: DocumentType[];
  readKycFeedback: string;
  merchantCountry: string;
  updateStage: (num: number) => void;
  updateSubmittedKyc: (data: IMerchantKycInfo) => void;
  formRef: React.RefObject<FormikProps<DocumentFormValuesType>>;
  setMobileActionBtnState: () => void;
  validIdOptions: IdResponseType[];
};

export type DocumentsUploadType = {
  type: string | IdResponseType;
  id?: string;
  file?: File;
  text?: { name?: string; value?: string };
};

export type RepresentativeResponseType = {
  name: string;
  role: {
    id: string;
    label: string;
  };
  shareholding_company_name?: string;
  documents: Array<{
    type: string | IdResponseType;
    id?: string;
    file?: File;
    text?: { name?: string; value?: string };
  }>;
};
export type RepresentativeType = {
  name: string;
  role: string;
  shareholding_company_name?: string;
  documents: Array<DocumentsUploadType>;
};

export type RepresentativesFormValuesType = {
  type?: {
    regex?: string;
    label?: string;
    value?: string;
  };
  id?: string;
  file?: {
    name?: string;
    description?: string;
  };
  text?: {
    name?: string;
    value?: string;
  };
};

export type UploadResponseType = {
  data: Array<{
    id: number;
    identifier: string;
    category: string;
    original_name: string;
    encoding: string;
    path: string;
    mime: string;
    updatedAt: string;
    createdAt: string;
  }>;
};

export interface IRepresentativeProps {
  merchantCountry: string;
  kycStage: string;
  updateStage: (stage: number) => void;
  registeredType: string;
  readKycFeedback?: string;
  submittedRepresentatives?: RepresentativeResponseType[];
  updateSubmittedKyc: (data?: IMerchantKycInfo) => void;
  formRef: React.RefObject<HTMLFormElement>;
  setMobileActionBtnState: (state: { disabled?: boolean; isLoading?: boolean }) => void;
  validIdOptions: IdResponseType[];
}

export type SubmitValuesType = {
  is_auto_save?: boolean;
  representatives: RepresentativeType[];
};
