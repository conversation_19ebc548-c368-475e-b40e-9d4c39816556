export type ConversionsPayloadType = {
  enable?: boolean;
  from_currency?: string;
  to_currency?: string;
  markup?: number;
};
export type TConversionsError = { response: { data: { message: string } } };

export type TCurrencyMarkup = {
  [key: string]: {
    activated: boolean;
    enabled: boolean;
    markup: {
      kora: {
        value: number;
      };
      merchant: {
        value: number;
        limit: number;
      };
    };
  };
};

export type TConversionsState = {
  activeModal: string;
  settlementCurrency: string;
  currencyList: string[];
  rateMarkup: string;
  enabled: boolean;
  activated: boolean;
};

export type TCurrencyStructure = { [key: string]: { activated: boolean; rate: string; limit: string; enabled: boolean } };
export type TError = { response: { data: { data: { message: string } } } };

export interface IConversionData {
  status: boolean;
  code: string;
  message: string;
  data: {
    source_currency: string;
    destination_currency: string;
    exchange_rate: string;
    source_amount: string;
    converted_amount: string;
    status: string;
    reference: string;
    channel: string;
    customer_name: string;
    customer_email: string;
    narration: string | null;
    transaction_date: string;
    from_currency_rate: string;
  };
}
