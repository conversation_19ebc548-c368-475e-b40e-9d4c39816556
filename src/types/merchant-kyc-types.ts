import { ISettlementDetails } from './settlement-types';

interface IContact {
  support_phone: string;
  support_email: string;
}

interface IDetails {
  contact: IContact;
  settlement_accounts: Record<string, ISettlementDetails>;
  business_profile: {
    business_description: string;
    website: string;
    incorporated_trustee_category: {
      label: string;
    };
    industry: {
      label: string;
    };
  };
}

interface ICompliance {
  feedback: Record<string, IFeedback[]>;
  status: string;
}

interface IFeedback {
  date_added: string;
  note: string;
}

export interface IMerchantKYC {
  merchant_id: number;
  business_type: string | null;
  bvn_required: boolean;
  details: IDetails;
  compliance: ICompliance;
}
