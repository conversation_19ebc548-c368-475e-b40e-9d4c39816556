import { useState } from 'react';

import { UtilServices } from '+services/util-services';
import { Country, CountryList, StatesListType } from '+types';

const useCountriesAndStates = () => {
  const [countriesList, setCountriesList] = useState<CountryList[]>([]);
  const [statesList, setStatesList] = useState<StatesListType>({});

  const createStatesList = (listOfCountriesData: Array<Country>) => {
    const newState: StatesListType = listOfCountriesData.reduce(
      (accum, next) => ({
        ...accum,
        [next.iso2]: Array.isArray(next.states) ? next.states.map(state => (typeof state === 'string' ? state : state.name)) : []
      }),
      {}
    );
    setStatesList(newState);
  };

  UtilServices.useFetchCountries({
    params: { includeStates: true, forSignup: true },
    onSuccess: data => {
      const options = data?.data.map((item: Country) => ({
        value: item.iso2,
        label: item.name
      }));
      setCountriesList(options);
      createStatesList(data?.data);
    }
  });

  return { countriesList, statesList };
};

export default useCountriesAndStates;
