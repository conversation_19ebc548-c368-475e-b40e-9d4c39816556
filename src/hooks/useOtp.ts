import { useEffect, useRef, useState } from 'react';

import { closeFeedback, feedbackInit } from '+hooks/feedbackHandler';
import { AuthServices } from '+services/auth-services';
import { AuthDataType, ErrorType } from '+types';

interface IUseOtpProps {
  action: string;
  authData: AuthDataType;
  setAuthData: ({ code, identifier, two_factor_type }: Partial<AuthDataType>) => void;
  setTwoFactorType?: (type: 'otp' | 'totp' | 'totp_recovery_code') => void;
  onInitiationComplete?: () => void;
}

const useOtp = ({ action, authData, setAuthData, setTwoFactorType, onInitiationComplete }: IUseOtpProps) => {
  const [timeLeft, setTimeLeft] = useState(60);
  const intervalRef = useRef<NodeJS.Timer | undefined>();
  const [initializing, setInitializing] = useState(false);
  const onError = (error: ErrorType, timeout = true) => {
    const data = error?.response?.data;
    setInitializing(false);
    feedbackInit({
      isActive: true,
      message: data?.message || 'We are sorry, something went wrong. Please try again.',
      type: 'danger',
      componentLevel: true
    });
    if (timeout) {
      setTimeout(() => {
        closeFeedback();
      }, 5000);
    }
  };

  const initiateOTP = AuthServices.useInitiateOTPToken({
    bannerLevel: true,
    onSuccess: data => {
      const { data: initiateOTPData } = data;
      setAuthData({
        identifier: initiateOTPData?.identifier
      });
      setTwoFactorType?.(initiateOTPData?.type as 'otp' | 'totp' | 'totp_recovery_code');
      setTimeLeft(60);
      onInitiationComplete?.();
      setInitializing(false);
    },
    onError: error => onError(error as ErrorType)
  });

  const resendOTP = AuthServices.useResendOTPAction({
    bannerLevel: true,
    showErrorMessage: false,
    onError: error => onError(error as ErrorType),
    customBaseURL: process.env.REACT_APP_MERCHANT_MIDDLEWARE_API_BASE
  });

  const initiateToken = async ({isAsync= false}): Promise<void> => {
    setInitializing(true);

    if (isAsync) {
      try {
        await initiateOTP.mutateAsync({ action });
      } catch (error) {
        throw error;
      }
    } else {
      initiateOTP.mutate({ action });
    }
  };

  const onResendToken = () => {
    resendOTP.mutate({
      identifier: authData.identifier
    });
    setTimeLeft(60);
  };

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setTimeLeft(t => t - 1);
    }, 1000);
    if (timeLeft <= 0) {
      clearInterval(intervalRef.current as NodeJS.Timeout);
    }
    return () => clearInterval(intervalRef.current as NodeJS.Timeout);
  }, [timeLeft]);

  return {
    timeLeft,
    initiateToken,
    onResendToken,
    initializing
  };
};

export default useOtp;
