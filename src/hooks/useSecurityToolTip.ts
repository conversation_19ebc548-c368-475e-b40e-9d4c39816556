import { useCallback, useEffect, useState } from 'react';

import { GuideList } from '+dashboard/TipsAndGuides/data';
import useStore from '+store';
import useLocalStore from '+store/localStore';
import { ICreateTipsAndGuideSlice } from '+types/tips-and-guide';
import { history } from '+utils';

import useIpWhiteList from './useIpWhiteList';

import auditLogTip from '+assets/img/dashboard/auditlogTip.png';
import ipWhiteListIntro from '+assets/img/dashboard/ipwlisting1.png';
import ipWhiteListIntro2 from '+assets/img/dashboard/ipwlisting2.png';
import ipWhiteListIntro3 from '+assets/img/dashboard/ipwlisting3.png';
import ipWhiteListIntro4 from '+assets/img/dashboard/ipwlisting4.png';
import teamTip from '+assets/img/dashboard/teamTip.png';

export const useSecurityToolTip = ({ enableIpWhiteListQuery = true }: { enableIpWhiteListQuery?: boolean } = {}) => {
  const { ipEnabled, isSuccess: isIPSuccess, isError: isIPError } = useIpWhiteList({ enabled: enableIpWhiteListQuery });
  const { authDetails } = useStore();
  const userToken = authDetails?.access_token;
  const [permissions, setPermissions] = useState<{ [property: string]: string }>({});
  const profile = useStore(state => state.profile);
  const ipReader = useLocalStore((state: ICreateTipsAndGuideSlice) => state.ip);
  const teamReader = useLocalStore((state: ICreateTipsAndGuideSlice) => state.team);
  const auditReader = useLocalStore((state: ICreateTipsAndGuideSlice) => state.audit);
  const guideListReader = useLocalStore((state: ICreateTipsAndGuideSlice) => state.list);
  const setList = useLocalStore(state => state.setList);
  const setIp = useLocalStore(state => state.setIp);
  const setTeam = useLocalStore(state => state.setTeam);
  const setAudit = useLocalStore(state => state.setAudit);
  const [securityTipType, setSecurityTipType] = useState<string | null>(null);
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [openSecurityTip, setOpenSecurityTip] = useState(false);
  const [unClicked, setUnClicked] = useState<number>(GuideList.length);
  const ip = ipReader?.[profile?.email || ''];
  const team = teamReader?.[profile?.email || ''];
  const audit = auditReader?.[profile?.email || ''];
  const guideList = guideListReader?.[profile?.email || ''];

  const isIPQueryComplete = isIPSuccess || isIPError;
  const generateSecurityTips = () => {
    if (!profile?.email) return;
    const tips: string[] = [];
    if (!ipEnabled && !ip?.deny && permissions?.ip_address_configuration === 'manage') tips.push('ip');
    if (!team?.deny && permissions?.team === 'manage') tips.push('team');
    if (!audit?.deny && permissions?.audit_log === 'view') tips.push('audit');

    const updatedList = GuideList.filter(({ type }) => {
      if (type === 'ip') return permissions?.ip_address_configuration === 'manage';
      if (type === 'team') return permissions?.team === 'manage';
      if (type === 'audit') return permissions?.audit_log === 'view';
      return true;
    });
    setList({ email: profile.email, value: updatedList });
    const randomTip = tips[Math.floor(Math.random() * tips.length)];
    setSecurityTipType(randomTip);
  };

  const showOneSecurityTipAndDisable = useCallback(() => {
    if (!profile?.email) return false;

    const sessionKey = `securityTipShown_${profile.email}`;
    const hasBeenShownInSession = sessionStorage.getItem(sessionKey) === 'true';

    if (hasBeenShownInSession) {
      return false;
    }

    const availableTips: string[] = [];
    if (!ipEnabled && !ip?.deny && permissions?.ip_address_configuration === 'manage') availableTips.push('ip');
    if (!team?.deny && permissions?.team === 'manage') availableTips.push('team');
    if (!audit?.deny && permissions?.audit_log === 'view') availableTips.push('audit');

    if (availableTips.length > 0) {
      const randomTip = availableTips[Math.floor(Math.random() * availableTips.length)];
      setSecurityTipType(randomTip);

      sessionStorage.setItem(sessionKey, 'true');

      return true;
    }
    return false;
  }, [
    profile?.email,
    ipEnabled,
    ip?.deny,
    permissions?.ip_address_configuration,
    team?.deny,
    permissions?.team,
    audit?.deny,
    permissions?.audit_log
  ]);

  const shouldShowSecurityTips = useCallback(() => {
    if (!profile?.email) return false;

    const hasIpTip = !ipEnabled && !ip?.deny && permissions?.ip_address_configuration === 'manage';
    const hasTeamTip = !team?.deny && permissions?.team === 'manage';
    const hasAuditTip = !audit?.deny && permissions?.audit_log === 'view';

    return hasIpTip || hasTeamTip || hasAuditTip;
  }, [
    profile?.email,
    ipEnabled,
    ip?.deny,
    permissions?.ip_address_configuration,
    team?.deny,
    permissions?.team,
    audit?.deny,
    permissions?.audit_log
  ]);

  useEffect(() => {
    const unClickedTips = [ip, audit, team].reduce((initial, tip) => {
      let tipCounter = initial;
      if (tip?.clicked) tipCounter -= 1;
      return tipCounter;
    }, guideList?.length || 0);
    setUnClicked(unClickedTips);
  }, [ip?.clicked, team?.clicked, audit?.clicked, guideList]);

  useEffect(() => {
    if (userToken) {
      try {
        const jwt = JSON.parse(atob(userToken.split('.')[1])) as {
          permissions?: { permissions?: { [property: string]: string } };
        };
        const jwtPermissions = jwt?.permissions?.permissions || {};
        setPermissions(jwtPermissions);
      } catch (error) {
        console.warn('Failed to parse JWT token permissions:', error);
      }
    }
  }, [userToken]);

  const getSecurityTipProps = () => {
    switch (securityTipType) {
      case 'ip':
        return {
          checked: ip?.deny,
          onCheckedChange: () => setIp({ email: profile?.email || '', value: { deny: !ip?.deny } }),
          onSubmit: () => !ipEnabled && history.push('/dashboard/settings/security/ip-whitelist'),
          secondButtonFinalText: ipEnabled ? 'Done' : 'Get Started',
          removeIconOnFinalStep: true,
          hasFirstButton: true,
          data: [
            {
              title: 'Introducing IP Whitelisting!',
              description: 'IP Whitelisting guarantees secure API access: Safeguard your API access in three simple steps.',
              image: ipWhiteListIntro
            },
            {
              title: 'Find the IP Whitelist page',
              description: "Navigate to the 'IP Whitelist' tab within your dashboard's Settings Page.",
              image: ipWhiteListIntro2
            },
            {
              title: 'Add the IP addresses you want to authorise for access',
              description: "Click 'Add IP Address' to include the chosen IP addresses and provide access to your business dashboard.",
              image: ipWhiteListIntro3
            },
            {
              title: 'Enable IP Whitelisting!',
              description:
                "After adding the desired IP addresses for authorisation, click 'Enable IP Whitelist' to ensure access is restricted exclusively to the listed IP addresses",
              image: ipWhiteListIntro4
            }
          ]
        };
      case 'audit':
        return {
          checked: audit?.deny,
          onCheckedChange: () => setAudit({ email: profile?.email || '', value: { deny: !audit?.deny } }),
          onSubmit: () => history.push('/dashboard/audit-logs'),
          secondButtonFinalText: 'Take me to Audit Logs',
          data: [
            {
              title: 'Track activities with Audit Logs!',
              description:
                'Audit log serves as a documentation for organizations, providing a comprehensive historical record of activities on your dashboard.',
              image: auditLogTip
            }
          ]
        };
      case 'team':
        return {
          checked: team?.deny,
          onCheckedChange: () => setTeam({ email: profile?.email || '', value: { deny: !team?.deny } }),
          onSubmit: () => history.push('/dashboard/settings/teams'),
          secondButtonFinalText: 'Go to Team Settings',
          data: [
            {
              title: 'Introducing Teams!',
              description:
                'You can add team members to collaborate on your dashboard. Streamline responsibilities with designated roles for each member.',
              image: teamTip
            }
          ]
        };
      default:
        return null;
    }
  };

  return {
    ipEnabled,
    generateSecurityTips,
    shouldShowSecurityTips,
    showOneSecurityTipAndDisable,
    unClicked,
    securityTipType,
    setSecurityTipType,
    getSecurityTipProps,
    ip,
    audit,
    team,
    setIp,
    setTeam,
    setAudit,
    isTipVisible,
    setIsTipVisible,
    openSecurityTip,
    setOpenSecurityTip,
    permissions,
    isIPQueryComplete
  };
};
