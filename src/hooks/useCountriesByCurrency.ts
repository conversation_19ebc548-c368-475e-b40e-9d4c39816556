import { useState } from 'react';

import { UtilServices } from '+services/util-services';
import { Country, StatesListType } from '+types';

const useCountriesByCurrrency = (currency: string) => {
  const [countries, setCountries] = useState<Array<{ value: string; label: string; isoCode: string }>>([]);
  const [states, setStates] = useState<StatesListType>({});

  const createStatesList = (listOfCountriesData: Array<Country>) => {
    const newState: StatesListType = listOfCountriesData.reduce((accum, next) => ({ ...accum, [next.iso2]: next.states }), {});
    setStates(newState);
  };

  UtilServices.useCountriesByCurrency({
    currency: currency,
    params: { includeStates: true },
    onSuccess: data => {
      const options = data?.data?.map((item: { id: number; name: string; iso2: string }) => ({
        value: String(item.id),
        label: item.name,
        isoCode: item.iso2
      }));
      setCountries(options);
      createStatesList(data?.data);
    },
    bannerLevel: true
  });

  return { countries, states };
};

export default useCountriesByCurrrency;
