/* eslint-disable no-unused-vars */

import { AxiosResponse } from 'axios';

import { useFetch } from '+hooks';
import useStore from '+store';
import {
  BalanceHistoryType,
  BalancesDataType,
  CurrencyType,
  ExportActionType,
  IResponse,
  LienDataType,
  ReserveHistoryType,
  WalletBalanceType
} from '+types';

export const BalanceServices = {
  // use get balance here
  useGetBalances: ({
    onSuccess,
    onError,
    showErrorMessage,
    bannerLevel,
    enabled,
    refetchOnCloseFeedbackError,
    errorMessage
  }: {
    onSuccess?: (data: AxiosResponse<BalancesDataType>) => void;
    onError?: (e: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage?: boolean;
    enabled?: boolean;
    refetchOnCloseFeedbackError?: boolean;
    errorMessage?: string;
  }) =>
    useFetch<{ data: WalletBalanceType }>('merchants/profile/balances', {
      onError,
      errorMessageBannerLevel: bannerLevel,
      showErrorMessage,
      enabled,
      refetchOnCloseFeedbackError,
      errorMessage,
      onSuccess(data) {
        useStore.setState({
          merchantBalanceDetails: (data as AxiosResponse<BalancesDataType>)?.data
        });
        onSuccess?.(data as AxiosResponse<BalancesDataType>);
      }
    }),

  // use get Rolling Reserve History here
  useGetRollingReserveHistory: ({
    onSuccess,
    onError,
    showErrorMessage,
    bannerLevel,
    enabled,
    params,
    currency,
    refetchOnCloseFeedbackError
  }: {
    onSuccess?: (data?: IResponse<ReserveHistoryType[]>) => void;
    onError?: (e: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage?: boolean;
    enabled?: boolean;
    currency?: string;
    params?: { page?: string | number; limit?: string } | null;
    refetchOnCloseFeedbackError?: boolean;
  }) =>
    useFetch(`/transactions/rolling-reserves/${currency}/transactions`, {
      onError,
      errorMessageBannerLevel: bannerLevel,
      showErrorMessage,
      enabled,
      onSuccess,
      params,
      refetchOnCloseFeedbackError,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  // use get balance History here
  useGetBalanceHistory: ({
    onSuccess,
    onError,
    bannerLevel,
    params,
    currency,
    refetchOnCloseFeedbackError
  }: {
    onSuccess?: (data: IResponse<BalanceHistoryType[]>) => void;
    onError?: (e: Error) => void;
    bannerLevel?: boolean;
    currency?: string;
    params?: { page?: string | number; limit?: string } | null;
    refetchOnCloseFeedbackError?: boolean;
  }) =>
    useFetch(`/transactions/balances/${currency}/transactions`, {
      onError,
      errorMessageBannerLevel: bannerLevel,
      onSuccess,
      params,
      refetchOnCloseFeedbackError,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useExportBalanceHistory: ({
    enabled,
    bannerLevel,
    errorMessage,
    params,
    onSuccess,
    onError
  }: {
    enabled?: boolean;
    onSuccess?: (data: IResponse<BalanceHistoryType[]>) => void;
    onError?: () => void;
    bannerLevel?: boolean;
    errorMessage?: string;
    params: {
      currency: CurrencyType;
      format: ExportActionType['format'];
      dateFrom?: string;
      dateTo?: string;
      sortingParams?: string;
    } | null;
  }) => {
    const { format, dateFrom, dateTo, sortingParams, currency } = params || {};
    const formattedParams = { format, date_from: dateFrom, date_to: dateTo, currency };

    return useFetch(`/transactions/balances/${currency}/transactions?fieldsToExport[]=${sortingParams}`, {
      enabled,
      onError,
      onSuccess,
      errorMessageBannerLevel: bannerLevel,
      errorMessage,
      params: formattedParams,
      requestConfig: { responseType: 'blob' },
      useEnvironmentHandler: true,
      showReturnDefaultResponse: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    });
  },

  useExportRollingReserveHistory: ({
    enabled,
    bannerLevel,
    errorMessage,
    params,
    onSuccess,
    onError
  }: {
    enabled?: boolean;
    onSuccess?: (data: IResponse<ReserveHistoryType[]>) => void;
    onError?: () => void;
    bannerLevel?: boolean;
    errorMessage?: string;
    params: {
      currency: CurrencyType;
      format: ExportActionType['format'];
      dateFrom?: string;
      dateTo?: string;
      sortingParams?: string;
    } | null;
  }) => {
    const { currency, format, dateFrom, dateTo } = params || {};
    const formattedParams = { currency, format, dateFrom, dateTo };

    return useFetch(`/transactions/rolling-reserves/${currency}/transactions`, {
      enabled,
      onSuccess,
      onError,
      errorMessageBannerLevel: bannerLevel,
      errorMessage,
      params: formattedParams,
      requestConfig: { responseType: 'blob' },
      useEnvironmentHandler: true,
      showReturnDefaultResponse: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    });
  },
  useExportLienHistory: ({
    enabled,
    onSuccess,
    onError,
    bannerLevel,
    refetchOnCloseFeedbackError,
    params,
    errorMessage,
    successMessage
  }: {
    enabled?: boolean;
    onSuccess?: (data: IResponse<LienDataType[]>) => void;
    onError?: (e: Error) => void;
    bannerLevel?: boolean;
    refetchOnCloseFeedbackError?: boolean;
    params: {
      format?: string;
      sortingParams?: string;
      currency: CurrencyType;
      fieldToExport: ExportActionType['fieldsToExport'];
      kind?: string;
      dateFrom?: string;
      dateTo?: string;
    } | null;
    errorMessage?: string;
    successMessage?: string;
  }) => {
    const { format, currency, fieldToExport, dateFrom, dateTo } = params || {};
    const formattedParams = { format, currency, fieldsToExport: fieldToExport, dateFrom, dateTo };
    return useFetch(`/liens`, {
      enabled,
      onSuccess,
      onError,
      errorMessageBannerLevel: bannerLevel,
      refetchOnCloseFeedbackError,
      params: formattedParams,
      errorMessage,
      successMessage,
      showSuccessMessage: true,
      useEnvironmentHandler: true,
      showReturnDefaultResponse: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      requestConfig: { responseType: 'blob' }
    });
  }
};
