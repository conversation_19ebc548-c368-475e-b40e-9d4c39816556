/* eslint-disable no-unused-vars */

import { useFetch, useSend } from '+hooks';
import { IInitiateApprovalWorkflowData, IInitiateManageApprovalWorkflowData, IResponse, ISendResponse } from '+types';

export const ApprovalServices = {
  useGetApprovalWorkflowUsers: ({
    onError,
    bannerLevel,
    errorMessage
  }: {
    onError?: () => void;
    bannerLevel?: boolean;
    errorMessage?: string;
  }) =>
    useFetch(`/approval-workflow/users`, {
      onError,
      errorMessage,
      errorMessageBannerLevel: bannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useGetApprovalWorkflows: ({
    onError,
    bannerLevel,
    errorMessage
  }: {
    onError?: () => void;
    bannerLevel?: boolean;
    errorMessage?: string;
  }) =>
    useFetch(`/approval-workflow`, {
      onError,
      errorMessage,
      errorMessageBannerLevel: bannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useGetApprovalWorkflowList: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    queryData
  }: {
    onSuccess?: (data: IInitiateApprovalWorkflowData) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    queryData: {
      params?: { user_type: string; limit: string; page: string };
      workflowReference?: string;
    };
  }) =>
    useFetch(`/approval-workflow/${queryData?.workflowReference}/users`, {
      params: queryData?.params,
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useInitiateApprovalWorkflow: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel
  }: {
    onSuccess?: (data: IInitiateApprovalWorkflowData) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
  }) =>
    useSend({
      url: `/approval-workflow/initiate`,
      method: 'post',
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useAuthorizeApprovalWorkflow: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel
  }: {
    onSuccess?: (data: ISendResponse<unknown>) => void;
    onError?: (error: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
  }) =>
    useSend({
      url: `/approval-workflow/authorize`,
      method: 'post',
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useInitiateManageApprovalWorkflow: ({
    onSuccess,
    onError,
    errorMessageBannerLevel,
    showErrorMessage,
    queryData,
    showSuccessMessage
  }: {
    onSuccess?: (data: IInitiateManageApprovalWorkflowData) => void;
    onError?: (error: Error) => void;
    errorMessageBannerLevel: boolean;
    showErrorMessage: boolean;
    queryData: {
      approvalWorkflowRef: string;
    };
    showSuccessMessage: boolean;
  }) =>
    useSend({
      url: `/approval-workflow/${queryData.approvalWorkflowRef}/initiate`,
      method: 'patch',
      onSuccess,
      onError,
      errorMessageBannerLevel,
      showErrorMessage,
      showSuccessMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useAuthorizeManageApprovalWorkflow: ({
    onSuccess,
    onError,
    showErrorMessage,
    queryData,
    enabled,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel
  }: {
    onSuccess?: (data: ISendResponse<unknown>) => void;
    onError?: (error: Error) => void;
    showErrorMessage?: boolean;
    queryData: {
      approvalWorkflowRef: string;
    };
    enabled: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
  }) =>
    useSend({
      url: `/approval-workflow/${queryData.approvalWorkflowRef}/authorize`,
      method: 'patch',
      onSuccess,
      onError,
      errorMessageBannerLevel,
      showErrorMessage,
      showSuccessMessage,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      data: {
        enabled
      }
    }),

  useInitiateUpdateWorkflow: ({
    onSuccess,
    onError,
    bannerLevel,
    showErrorMessage
  }: {
    onSuccess?: (data: ISendResponse<unknown>) => void;
    onError?: (error: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage?: boolean;
  }) =>
    useSend({
      url: `/approval-workflow/initiate`,
      method: 'patch',
      onSuccess,
      onError,
      errorMessageBannerLevel: bannerLevel,
      showErrorMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useAuthorizeUpdateWorkflow: ({
    onSuccess,
    onError,
    bannerLevel,
    showErrorMessage
  }: {
    onSuccess?: (data: ISendResponse<unknown>) => void;
    onError?: (error: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage?: boolean;
  }) =>
    useSend({
      url: `/approval-workflow/authorize`,
      method: 'patch',
      onSuccess,
      onError,
      errorMessageBannerLevel: bannerLevel,
      showErrorMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),

  useRemoveApprovalWorkflow: ({
    onSuccess,
    onError,
    bannerLevel,
    showErrorMessage,
    showSuccessMessage,
    reference
  }: {
    onSuccess?: (data: ISendResponse<unknown>) => void;
    onError?: (error: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    reference: string;
  }) =>
    useSend({
      url: `/approval-workflow/${reference}/users/remove`,
      method: 'patch',
      onSuccess,
      onError,
      errorMessageBannerLevel: bannerLevel,
      showErrorMessage,
      showSuccessMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useInitiateSinglePayoutApproval: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel
  }: {
    onSuccess?: (data: IResponse<unknown>) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
  }) =>
    useSend({
      url: `/transfers/approval/initiate`,
      method: 'post',
      onSuccess,
      onError,
      errorMessageBannerLevel,
      showErrorMessage,
      showSuccessMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useAuthorizeApproveSinglePayout: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel
  }: {
    onSuccess?: (data: IResponse<unknown>) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
  }) =>
    useSend({
      url: `/transfers/approval/approve`,
      method: 'post',
      onSuccess,
      onError,
      errorMessageBannerLevel,
      showErrorMessage,
      showSuccessMessage,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useAuthorizeDeclineSinglePayout: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel
  }: {
    onSuccess?: (data: IResponse<unknown>) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
  }) =>
    useSend({
      url: `/transfers/approval/decline`,
      method: 'post',
      onSuccess,
      onError,
      errorMessageBannerLevel,
      showErrorMessage,
      showSuccessMessage,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useInitiateBulkPayoutApproval: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    ref
  }: {
    onSuccess?: (data: IResponse<unknown>) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    ref: string;
  }) =>
    useSend({
      url: `/transactions/bulk/${ref}/approval/initiate`,
      method: 'patch',
      onSuccess,
      onError,
      errorMessageBannerLevel,
      showErrorMessage,
      showSuccessMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useAuthorizeApproveBulkPayout: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel,
    ref
  }: {
    onSuccess?: (data: IResponse<unknown>) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
    ref: string;
  }) =>
    useSend({
      url: `/transactions/bulk/${ref}/approval/approve`,
      method: 'patch',
      onSuccess,
      onError,
      errorMessageBannerLevel,
      showErrorMessage,
      showSuccessMessage,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useAuthorizeDeclineBulkPayout: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel,
    ref
  }: {
    onSuccess?: (data: IResponse<unknown>) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
    ref: string;
  }) =>
    useSend({
      url: `/transactions/bulk/${ref}/approval/decline`,
      method: 'patch',
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useInitiateUpdateInitiators: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    reference
  }: {
    onSuccess?: (data: IInitiateApprovalWorkflowData) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    reference: string;
  }) =>
    useSend({
      url: `/approval-workflow/${reference}/initiate`,
      method: 'patch',
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useAuthorizeUpdateInitiators: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel,
    reference
  }: {
    onSuccess?: (data: ISendResponse<unknown>) => void;
    onError?: (error: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
    reference: string;
  }) =>
    useSend({
      url: `/approval-workflow/${reference}/authorize`,
      method: 'patch',
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useInitiateUpdateApprovers: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    reference
  }: {
    onSuccess?: (data: IInitiateApprovalWorkflowData) => void;
    onError?: (error: Error) => void;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    reference: string;
  }) =>
    useSend({
      url: `/approval-workflow/${reference}/initiate`,
      method: 'patch',
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    }),
  useAuthorizeUpdateApprovers: ({
    onSuccess,
    onError,
    showErrorMessage,
    showSuccessMessage,
    errorMessageBannerLevel,
    successMessageBannerLevel,
    reference
  }: {
    onSuccess?: (data: ISendResponse<unknown>) => void;
    onError?: (error: Error) => void;
    bannerLevel?: boolean;
    showErrorMessage: boolean;
    showSuccessMessage: boolean;
    errorMessageBannerLevel: boolean;
    successMessageBannerLevel: boolean;
    reference: string;
  }) =>
    useSend({
      url: `/approval-workflow/${reference}/authorize`,
      method: 'patch',
      onSuccess,
      onError,
      showErrorMessage,
      showSuccessMessage,
      errorMessageBannerLevel,
      successMessageBannerLevel,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE
    })
};
