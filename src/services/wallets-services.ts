/* eslint-disable no-unused-vars */
import { useFetch } from '+hooks';

export const WalletsServices = {
  useFetchWallets: ({
    onError,
    bannerLevel,
    enabled,
    errorMessage,
    onSuccess
  }: {
    onError?: () => void;
    bannerLevel?: boolean;
    reference?: string;
    currency: string;
    enabled?: boolean;
    errorMessage?: string;
    onSuccess?: () => void;
  }) =>
    useFetch(`/merchants/profile/wallets`, {
      onError,
      errorMessageBannerLevel: bannerLevel,
      useEnvironmentHandler: true,
      enabled,
      errorMessage,
      onSuccess
    })
};
