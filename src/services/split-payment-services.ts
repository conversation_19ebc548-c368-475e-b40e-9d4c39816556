import { keepPreviousData } from '@tanstack/react-query';

import { IOptions, useFetch, useSend } from '+hooks';
import {
  CreateSplitAccountResponseType,
  CreateSplitAccountSendOtpResponseType,
  CreateSplitPaymentResponseType,
  IResponse,
  ISendResponse,
  SingleSplitAccountSummary,
  SingleSplitPaymentResponseType,
  SingleSplitRuleType,
  SplitAccountsSummaryType,
  SplitPaymentsType,
  SplitSettingsResponseType
} from '+types';

export const SplitPaymentServices = {
  useGetSplitSettings: ({ params }: { params: { currency: string } }) =>
    useFetch<IResponse<SplitSettingsResponseType>['data']>(`/split-payments/settings`, {
      params,
      showErrorMessage: false,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      queryKey: ['SPLIT_PAYMENT_SETTINGS']
    }),

  useGetAllSplitRules: ({
    onError,
    onSuccess,
    errorMessage,
    showReturnDefaultResponse,
    params
  }: IOptions<IResponse<SingleSplitRuleType[]>, { currency: string; limit: number; page: number }>) =>
    useFetch(`/split-payments`, {
      onError,
      showReturnDefaultResponse,
      onSuccess,
      errorMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      queryKey: ['SPLIT_PAYMENT_RULES', params],
      params
    }),

  useCreateSplitRule: ({ onSuccess }: { onSuccess: (data: ISendResponse<CreateSplitPaymentResponseType>) => void }) =>
    useSend({
      url: `split-payments`,
      method: 'post',
      onSuccess,
      showSuccessMessage: false,
      errorMessageBannerLevel: true,
      reInvalidate: true,
      hasFeedbackTimeout: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      reInvalidationKey: ['SPLIT_PAYMENT_RULES']
    }),

  useGetSplitAccounts: ({ params }: { params: { currency: string } }) =>
    useFetch<IResponse<CreateSplitAccountResponseType[]>['data']>(`/split-payments/accounts`, {
      params,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      queryKey: ['SPLIT_PAYMENT_ACCOUNTS']
    }),

  useGetAllSplitAccountsSummary: ({ params, enabled }: { params: { [key: string]: string }; enabled: boolean }) =>
    useFetch<IResponse<SplitAccountsSummaryType[]>>(`/split-payments/accounts/summary`, {
      params,
      enabled,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      queryKey: ['SPLIT_PAYMENT_ACCOUNTS_SUMMARY', ...Object.values(params)]
    }),

  useGetSingleSplitAccountSummary: (arg: { account: string; enabled: boolean }) =>
    useFetch<IResponse<SingleSplitAccountSummary>['data']>(`/split-payments/accounts/${arg.account}`, {
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      enabled: arg.enabled
    }),

  useGetSingleSplitPayment: ({ id, onError }: { id: string; onError: () => void }) =>
    useFetch<IResponse<SingleSplitPaymentResponseType>['data']>(`/split-payments/settlements/${id}`, {
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      onError
    }),

  useGetSplitPayments: ({
    isExport = false,
    params,
    enabled,
    onSuccess,
    onError,
    errorMessage
  }: {
    isExport?: boolean;
    enabled?: boolean;
    onError?: () => void;
    onSuccess?: (data: Blob | IResponse<SplitPaymentsType[]>) => void;
    errorMessage?: string;
    params: {
      format?: string;
      status?: string | string[];
      sortingParams?: Record<string, unknown>;
      currency?: string;
      fieldsToExport?: string;
      [key: string]: string | string[] | Record<string, unknown> | undefined;
    };
  }) => {
    const { format, status, sortingParams, currency, fieldsToExport } = params || {};

    const formattedParams = {
      status,
      format,
      ...sortingParams,
      currency,
      ...(isExport && { 'fieldsToExport[]': fieldsToExport })
    };

    return useFetch<IResponse<SplitPaymentsType[]>>(`/split-payments/settlements`, {
      enabled: isExport ? undefined : enabled,
      onSuccess,
      params: isExport ? formattedParams : params,
      showReturnDefaultResponse: true,
      ...(isExport ? { requestConfig: { responseType: 'blob' } } : {}),
      queryKey: isExport ? ['SPLIT_PAYMENTS_EXPORT', params] : ['SPLIT_PAYMENTS', params],
      placeholderData: isExport ? undefined : keepPreviousData,
      onError,
      errorMessage,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      useEnvironmentHandler: true
    });
  },

  useCreateSplitAccount: ({ onSuccess }: { onSuccess: (data: ISendResponse<CreateSplitAccountResponseType>) => void }) =>
    useSend({
      url: `split-payments/accounts`,
      method: 'post',
      onSuccess,
      reInvalidate: true,
      showSuccessMessage: false,
      errorMessageBannerLevel: true,
      hasFeedbackTimeout: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      reInvalidationKey: ['SPLIT_PAYMENT_ACCOUNTS']
    }),

  useSendOtpToCreateSplitAccount: ({ onSuccess }: { onSuccess: (data: ISendResponse<CreateSplitAccountSendOtpResponseType>) => void }) =>
    useSend({
      url: `split-payments/accounts/2fa/send-otp`,
      method: 'post',
      onSuccess,
      reInvalidate: true,
      showSuccessMessage: false,
      errorMessageBannerLevel: true,
      hasFeedbackTimeout: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      reInvalidationKey: ['SEND_OTP_SPLIT_PAYMENT_ACCOUNTS']
    }),

  useVerifyOtpToCreateSplitAccount: ({ onSuccess }: { onSuccess: (data: ISendResponse<CreateSplitAccountResponseType>) => void }) =>
    useSend({
      url: `split-payments/accounts/2fa/verify-otp`,
      method: 'post',
      onSuccess,
      reInvalidate: true,
      showSuccessMessage: false,
      errorMessageBannerLevel: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      hasFeedbackTimeout: true
    }),

  useUpdateSplitRule: ({ onSuccess, id }: { onSuccess: (data: ISendResponse<CreateSplitAccountResponseType>) => void; id: string }) =>
    useSend({
      url: `split-payments/${id}`,
      method: 'put',
      onSuccess,
      showSuccessMessage: false,
      errorMessageBannerLevel: true,
      reInvalidate: true,
      hasFeedbackTimeout: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      reInvalidationKey: ['SPLIT_PAYMENT_RULES']
    }),

  useDeleteSplitRule: ({ onSuccess, id }: { onSuccess: (data: ISendResponse<CreateSplitAccountResponseType>) => void; id: string }) =>
    useSend({
      url: `split-payments/${id}`,
      method: 'delete',
      showSuccessMessage: false,
      errorMessageBannerLevel: true,
      hasFeedbackTimeout: true,
      onSuccess,
      reInvalidate: true,
      customBaseURL: process.env.REACT_APP_PUBLIC_MERCHANT_MIDDLEWARE_API_BASE,
      reInvalidationKey: ['SPLIT_PAYMENT_RULES']
    })
};
