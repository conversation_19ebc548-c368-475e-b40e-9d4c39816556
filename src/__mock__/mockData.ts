/* eslint-disable import/prefer-default-export */
export const mockedPayIns = {
  data: {
    data: [
      {
        reference: 'KPY-CM-OGqUSfjmhH2w',
        status: 'failed',
        amount: '100.00',
        fee: '0.00',
        vat: '0.00',
        currency: 'NGN',
        amount_charged: '100.00',
        amount_paid: '100.00',
        payment_source_type: 'pay_with_bank',
        channel: 'modal',
        narration: 'payment for shoes',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment failed',
        transaction_reference: 'KPY-CM-OGqUSfjmhH2w',
        payment_reference: 'KPY-PAY-qESy0FhbiQgK',
        customer_name: 'lewii',
        customer_email: '<EMAIL>',
        transaction_date: '2022-05-16 23:40:51',
        completed_at: '2022-05-16 23:42:20',
        payment: {
          reference: 'KPY-PAY-qESy0FhbiQgK',
          customer: {
            id: 5946,
            name: 'lewi<PERSON>',
            email: '<EMAIL>'
          },
          account: {
            id: 25,
            name: '<PERSON><PERSON>'
          }
        }
      },
      {
        reference: 'KPY-CM-J2W6qbwc1khP',
        status: 'success',
        amount: '500.00',
        fee: '7.00',
        vat: '0.53',
        currency: 'NGN',
        amount_charged: '500.00',
        amount_paid: '492.47',
        payment_source_type: 'bank_transfer',
        channel: 'modal',
        narration: 'Testing creation of links with expired dates',
        payment_reversals_type: 0,
        meta: null,
        message: 'Successful',
        transaction_reference: 'KPY-CM-J2W6qbwc1khP',
        payment_reference: 'KPY-PAY-5lkUL3o4pKCV',
        customer_name: 'Test',
        customer_email: '<EMAIL>',
        transaction_date: '2022-04-26 14:17:10',
        completed_at: '2022-04-26 14:17:40',
        payment: {
          reference: 'KPY-PAY-5lkUL3o4pKCV',
          customer: {
            id: 5897,
            name: 'Test',
            email: '<EMAIL>'
          },
          account: {
            id: 25,
            name: 'Timi Adesoji'
          }
        }
      },
      {
        reference: 'KPY-CM-osS8mKHVPc3J',
        status: 'success',
        amount: '500.00',
        fee: '7.00',
        vat: '0.53',
        currency: 'NGN',
        amount_charged: '500.00',
        amount_paid: '492.47',
        payment_source_type: 'bank_transfer',
        channel: 'modal',
        narration: 'Payment on Timi Adesoji',
        payment_reversals_type: 0,
        meta: null,
        message: 'Successful',
        transaction_reference: 'KPY-CM-osS8mKHVPc3J',
        payment_reference: 'KPY-PAY-3c7PSng4u2Ac',
        customer_name: 'Timi Adesoji',
        customer_email: '<EMAIL>',
        transaction_date: '2022-04-06 07:48:09',
        completed_at: '2022-04-06 07:48:39',
        payment: {
          reference: 'KPY-PAY-3c7PSng4u2Ac',
          customer: {
            id: 5794,
            name: 'Timi Adesoji',
            email: '<EMAIL>'
          },
          account: {
            id: 25,
            name: 'Timi Adesoji'
          }
        }
      }
    ],
    paging: {
      total_items: 450,
      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/i/transactions/payins?page=1&limit=10&currency=NGN',
        rel: 'current',
        method: 'GET'
      },
      {
        href: 'https://api.koraapi.com/merchant/api/i/transactions/payins?page=2&limit=10&currency=NGN',
        rel: 'next',
        method: 'GET'
      }
    ]
  }
};

export const mockedAnalyticsPayIn = {
  yearAnalytics: {
    January: 0,
    February: 5000,
    March: 4006000,
    April: 14600,
    May: 10000,
    June: 0,
    July: 0,
    August: 0,
    September: 0,
    October: 0,
    November: 0,
    December: 0
  },
  monthAnalytics: {
    '1': 0,
    '2': 0,
    '3': 0,
    '4': 0,
    '5': 0,
    '6': 0,
    '7': 0,
    '8': 0,
    '9': 0,
    '10': 0,
    '11': 0,
    '12': 0,
    '13': 0,
    '14': 0,
    '15': 0,
    '16': 0,
    '17': 0,
    '18': 0,
    '19': 0,
    '20': 0,
    '21': 0,
    '22': 0,
    '23': 0,
    '24': 0,
    '25': 0,
    '26': 0,
    '27': 0,
    '28': 0,
    '29': 0,
    '30': 0,
    '31': 0
  },
  weekAnalytics: {
    Sunday: 0,
    Monday: 0,
    Tuesday: 0,
    Wednesday: 0,
    Thursday: 0,
    Friday: 0,
    Saturday: 0
  },
  totalAnalytics: '5221669.21',
  totalYearAnalytics: '4035600.00',
  totalMonthAnalytics: 0,
  totalWeekAnalytics: 0
};

export const mockedAnalyticsTransaction = {
  data: {
    allTransactionsCount: 652,
    successRate: '63.96',
    payinsTotal: 0,
    payoutsTotal: 0
  }
};

export const mockedVerificationStatusData = {
  data: {
    status: true,
    requirements: {
      business_documents: {
        status: 'complete',
        reasons: []
      },
      bank_account: {
        status: 'complete',
        reasons: []
      },
      business_profile: {
        status: 'complete',
        reasons: []
      }
    }
  }
};

export const mockedMerchantComplianceInfo = {
  data: {
    business_type: 'individual',
    bvn_required: true,
    details: {
      bvn: {
        number: '***********',
        status: 'submitted'
      },
      business_profile: {
        date_of_birth: '1960-08-01',
        website: 'https://korapay.com',
        business_description: 'A small pop shop that sell everything and anything',
        business_address: '1, Adedeji Adekola Street, Off Freedom way Lekki Phase 1, Lagos'
      },
      documents: [
        {
          type: 'valid_id',
          file: {
            description: 'my_international_passport.png',
            name: '515470e0-e780-11ea-9581-8d1eeaa9d84a.png',
            url: 'https://utilities.koraapi.com/api/files/files/515470e0-e780-11ea-9581-8d1eeaa9d84a.png'
          }
        },
        {
          type: 'proof_of_address',
          file: {
            description: 'my_phcn_bill.png',
            name: '515470e0-e780-11ea-9581-8d1eeaa9d84a.png',
            url: 'https://utilities.koraapi.com/api/files/files/515470e0-e780-11ea-9581-8d1eeaa9d84a.png'
          }
        }
      ],
      settlement_accounts: {
        NGN: [
          {
            id: 2,
            account_id: 1,
            currency: 'NGN',
            status: 'active',
            details: {
              account_number: '**********',
              account_name: 'Test Account',
              bank_code: '033'
            },
            bank: {
              id: 1,
              name: 'United Bank for Africa'
            }
          }
        ]
      },
      contact: {
        support_email: '<EMAIL>',
        support_phone: '***********'
      }
    },
    compliance: {
      feedback: {
        bvn: [
          {
            note: 'BVN does not match any of the listed representatives',
            date_added: '2024-05-09T17:38:15.389Z'
          }
        ]
      },
      status: 'verified'
    }
  }
};

export const mockedMerchantDetails = {
  data: {
    account: {
      first_name: 'Lynda ',
      last_name: 'Nwakor ',
      avatar: 'https://lh3.googleusercontent.com/a/AATXAJzUagp8poGQ35gLzI3l_wZ4zpHjnhofszKgK_6p=s96-c',
      email: '<EMAIL>',
      phone: null,
      two_factor_enabled: false,
      created_at: '2022-01-19T15:26:56.000Z'
    },
    merchant: {
      id: 106,
      avatar: '',
      description: '',
      email: '<EMAIL>',
      fees: [],
      available_currency: ['EUR', 'GBP', 'GHS', 'KES', 'NGN', 'USD', 'XAF', 'XOF', 'ZAR'],
      name: 'GIG Logistics',
      risk_level: 'medium_risk',
      env: 'live',
      can_go_live: true,
      status: 'active',
      kyc_status: 'verified',
      tier_level: {
        id: 2,
        name: 'Base Tier',
        description:
          '\n    For new merchants who have just entered Kora space and are still\n    undergoing the KYC process but need to test the system before their verification and go-live date.'
      },
      country: {
        id: 165,
        name: 'Nigeria',
        iso2: 'NG',
        is_active: true
      },
      payout_limit: 2000000,
      payout_limits: {
        mobile_money: {
          KES: {
            min: 2,
            max: 15000
          },
          NGN: {
            min: 100,
            max: ********000
          },
          XAF: {
            min: 100,
            max: 15000
          },
          XOF: {
            min: 100,
            max: 15000
          }
        },
        bank_account: {
          GBP: {
            min: 5,
            max: ********
          },
          KES: {
            min: 10,
            max: 15000
          },
          NGN: {
            min: 100,
            max: ********000
          },
          USD: {
            min: 5,
            max: ********
          },
          XAF: {
            min: 100,
            max: ********000
          },
          XOF: {
            min: 100,
            max: ********000
          },
          ZAR: {
            min: 10,
            max: ********000
          }
        },
        disbursement_wallet: {
          EUR: {
            min: 100,
            max: ********000
          },
          GBP: {
            min: 100,
            max: ********
          },
          KES: {
            min: 100,
            max: 10000
          },
          NGN: {
            min: 10000,
            max: 1000000
          },
          USD: {
            min: 50,
            max: 1000
          },
          XAF: {
            min: 100,
            max: 10000
          },
          XOF: {
            min: 100,
            max: 10000
          }
        },
        bulk_bank_account: {
          KES: {
            min: 100,
            max: ***********
          },
          NGN: {
            min: 1000000,
            max: ***********
          },
          ZAR: {
            min: 100,
            max: ***********
          }
        },
        bulk_mobile_money: {
          KES: {
            min: 100,
            max: ***********
          }
        }
      },
      bulk_payout_limits: {
        min_allowed_bulk_payouts: 2,
        max_allowed_bulk_payouts: 100
      },
      withdrawal_limits: {
        bank_account: {
          GBP: {},
          KES: {},
          NGN: {
            settlement_account: 30000,
            other_accounts: 40000
          },
          USD: {},
          XAF: {},
          XOF: {},
          ZAR: {}
        }
      },
      payin_limits: {
        EUR: {
          min: 1,
          max: 1
        },
        KES: {
          min: 10,
          max: ********000
        },
        NGN: {
          min: 100,
          max: ********,
          daily: 100000
        },
        USD: {
          min: 1,
          max: 1000000
        },
        XAF: {
          min: 1,
          max: ********000
        },
        XOF: {
          min: 1,
          max: ********000
        },
        ZAR: {
          min: 100,
          max: 1000000
        }
      },
      checkout_limits: {
        EUR: {
          min: 1,
          max: 1
        },
        KES: {
          min: 10,
          max: 70000
        },
        NGN: {
          min: 100,
          max: ********,
          daily: 100000
        },
        USD: {
          min: 100,
          max: 10000
        },
        XAF: {
          min: 1,
          max: 1000000
        },
        XOF: {
          min: 1,
          max: 2000000
        },
        ZAR: {
          min: 100,
          max: 1000000
        }
      },
      issuing_wallet_limits: {
        USD: {
          min: 100
        }
      },
      conversion_limits: {
        NGN: {
          min: 1000,
          max: 100000,
          GHS: { min: 3000, max: ******** },
          KES: { min: 3000, max: ******** },
          USD: { min: 1000, max: ******** },
          ZAR: { min: 3000, max: ******** }
        },
        USD: {
          min: 2,
          max: 1000,
          GHS: { min: 1, max: 100000 },
          KES: { min: 1, max: 100000 },
          NGN: { min: 1, max: 100000 },
          ZAR: { min: 1, max: 100000 }
        },
        GHS: {
          KES: { min: 100, max: ******** },
          NGN: { min: 100, max: ******** },
          USD: { min: 100, max: ******** },
          ZAR: { min: 100, max: ******** }
        },
        KES: {
          NGN: { min: 1000, max: ******** },
          USD: { min: 1000, max: ******** },
          ZAR: { min: 1000, max: ******** }
        },
        ZAR: {
          GHS: { min: 300, max: ******** },
          KES: { min: 300, max: ******** },
          NGN: { min: 300, max: ******** },
          USD: { min: 300, max: ******** }
        }
      }
    }
  }
};

export const mockedUSDMerchantDetails = {
  available_currency: ['NGN', 'USD', 'KES'],
  avatar: null,
  can_go_live: true,
  description: null,
  email: '<EMAIL>',
  env: 'live',
  fees: [],
  id: 106,
  merchant_info: {
    address: 'Lagos',
    description: 'Tech meets fashion',
    rc_number: '123456',
    support_email: '<EMAIL>',
    support_number: '*************',
    support_website: 'https://korapay.com'
  },
  name: 'LANJ',
  payout_limit: 2000000,
  risk_level: 'medium_risk',
  settlement_bank: {
    account_number: '**********',
    bank_code: '033',
    bank_name: 'United Bank for Africa',
    bvn: '***********',
    currency: 'NGN',
    is_active: 1,
    name: 'Test Account'
  },
  wallet_payout_limit: {
    NGN: { min: 1000, max: ********0 },
    USD: { min: 100, max: ********000 },
    KES: { min: 100, max: ********000 }
  }
};

export const mockedMerchantBankAccount = {
  account_name: 'Korapay-Kor',
  account_number: '**********',
  bank_code: '232',
  bank_name: 'Sterling bank',
  fee_definition: [
    {
      type: 'flat',
      range: [0, 5000],
      value: 25,
      currency: 'NGN',
      base_fee: 25,
      vat: 1.875,
      vat_inclusive: false,
      total_fee: 26.88,
      cap: 26.88
    },
    {
      type: 'flat',
      range: [5000, 10000],
      value: 10,
      currency: 'NGN',
      base_fee: 10,
      vat: 0.75,
      vat_inclusive: false,
      total_fee: 10.75,
      cap: 10.75
    }
  ]
};

export const mockedNGNPayout = {
  data: {
    data: [
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-A3NDXCYbNVeciGle',
        current_balance: '********.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'NGN',
        description: 'Settlement For KPY-SET-A3NDXCYbNVeciGle',
        created_at: '2022-06-23T08:57:55.000Z',
        history_date: '2022-06-23 09:57:55',
        transaction_reference: 'KPY-SET-A3NDXCYbNVeciGle',
        balance_after: '********.63',
        balance_before: '31045137.63'
      },
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-y9I28dcrI7pWhJWe',
        current_balance: '31045137.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'NGN',
        description: 'Settlement For KPY-SET-y9I28dcrI7pWhJWe',
        created_at: '2022-06-22T12:37:24.000Z',
        history_date: '2022-06-22 13:37:24',
        transaction_reference: 'KPY-SET-y9I28dcrI7pWhJWe',
        balance_after: '31045137.63',
        balance_before: '31045037.63'
      },
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-DOvNUQCYOUFmBW8C',
        current_balance: '31045037.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'NGN',
        description: 'Settlement For KPY-SET-DOvNUQCYOUFmBW8C',
        created_at: '2022-06-22T09:59:54.000Z',
        history_date: '2022-06-22 10:59:54',
        transaction_reference: 'KPY-SET-DOvNUQCYOUFmBW8C',
        balance_after: '31045037.63',
        balance_before: '31044937.63'
      },
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-S50H3fHUBYoVsP35',
        current_balance: '31044937.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'NGN',
        description: 'Settlement For KPY-SET-S50H3fHUBYoVsP35',
        created_at: '2022-06-22T09:54:43.000Z',
        history_date: '2022-06-22 10:54:43',
        transaction_reference: 'KPY-SET-S50H3fHUBYoVsP35',
        balance_after: '31044937.63',
        balance_before: '31044837.63'
      }
    ],
    paging: {
      total_items: 94,
      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    }
  }
};

export const mockedUSDPayout = {
  data: {
    data: [
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-GgmBj5RwgdHmcZLC',
        current_balance: '5897091.46',
        amount: '1200.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-24T15:02:50.000Z',
        history_date: '2022-06-24 16:02:50',
        transaction_reference: 'KPY-DW-GgmBj5RwgdHmcZLC',
        balance_after: '5897091.46',
        balance_before: '5898291.46'
      },
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-teoX45VNeekRFFB4',
        current_balance: '5898291.46',
        amount: '1000.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-24T15:00:55.000Z',
        history_date: '2022-06-24 16:00:55',
        transaction_reference: 'KPY-DW-teoX45VNeekRFFB4',
        balance_after: '5898291.46',
        balance_before: '5899291.46'
      },
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-AwvoAMSafPpWhuRl',
        current_balance: '5899291.46',
        amount: '150.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-23T07:58:02.000Z',
        history_date: '2022-06-23 08:58:02',
        transaction_reference: 'KPY-DW-AwvoAMSafPpWhuRl',
        balance_after: '5899291.46',
        balance_before: '5899441.46'
      },
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-wjnajIQV6nVPxi0p',
        current_balance: '5899441.46',
        amount: '200.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-23T07:30:33.000Z',
        history_date: '2022-06-23 08:30:33',
        transaction_reference: 'KPY-DW-wjnajIQV6nVPxi0p',
        balance_after: '5899441.46',
        balance_before: '5899641.46'
      },
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-6uZwbduowzHeTGWy',
        current_balance: '5899641.46',
        amount: '500.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-22T15:43:52.000Z',
        history_date: '2022-06-22 16:43:52',
        transaction_reference: 'KPY-DW-6uZwbduowzHeTGWy',
        balance_after: '5899641.46',
        balance_before: '5900141.46'
      },
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-XsALPKAmZs1Wv6h1',
        current_balance: '5900141.46',
        amount: '1000.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-22T12:42:13.000Z',
        history_date: '2022-06-22 13:42:13',
        transaction_reference: 'KPY-DW-XsALPKAmZs1Wv6h1',
        balance_after: '5900141.46',
        balance_before: '5901141.46'
      },
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-ZQsVAfrRe4wmaVm6',
        current_balance: '5901141.46',
        amount: '2000.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-22T11:43:46.000Z',
        history_date: '2022-06-22 12:43:46',
        transaction_reference: 'KPY-DW-ZQsVAfrRe4wmaVm6',
        balance_after: '5901141.46',
        balance_before: '5903141.46'
      },
      {
        source_type: 'payment_destination',
        source_reference: 'KPY-DW-UiIa5hM0jEPTVaIw',
        current_balance: '5903141.46',
        amount: '100.00',
        direction: 'debit',
        currency: 'USD',
        description: 'Transfer to cjay-biz',
        created_at: '2022-06-22T11:36:29.000Z',
        history_date: '2022-06-22 12:36:29',
        transaction_reference: 'KPY-DW-UiIa5hM0jEPTVaIw',
        balance_after: '5903141.46',
        balance_before: '5903241.46'
      },
      {
        source_type: 'rolling_reserve',
        source_reference: 'KPY-SET-1vq8tmT87Jmlo02F',
        current_balance: '5903096.71',
        amount: '144.75',
        direction: 'credit',
        currency: 'USD',
        description: 'Rolling Reserve Release For Settlement KPY-SET-1vq8tmT87Jmlo02F',
        created_at: '2022-04-19T00:30:00.000Z',
        history_date: '2022-04-19 01:30:00',
        transaction_reference: 'KPY-SET-1vq8tmT87Jmlo02F',
        balance_after: '5903096.71',
        balance_before: '5902951.96'
      },
      {
        source_type: 'rolling_reserve',
        source_reference: 'KPY-SET-XCei0YGvNsFZHTQv',
        current_balance: '5903096.71',
        amount: '144.75',
        direction: 'credit',
        currency: 'USD',
        description: 'Rolling Reserve Release For Settlement KPY-SET-XCei0YGvNsFZHTQv',
        created_at: '2022-04-19T00:30:00.000Z',
        history_date: '2022-04-19 01:30:00',
        transaction_reference: 'KPY-SET-XCei0YGvNsFZHTQv',
        balance_after: '5903096.71',
        balance_before: '5902951.96'
      }
    ],
    paging: {
      total_items: 81,
      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    }
  }
};

export const mockedKESPayout = {
  data: {
    data: [
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-A3NDXCYbNVeciGle',
        current_balance: '********.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'KES',
        description: 'Settlement For KPY-SET-A3NDXCYbNVeciGle',
        created_at: '2022-06-23T08:57:55.000Z',
        history_date: '2022-06-23 09:57:55',
        transaction_reference: 'KPY-SET-A3NDXCYbNVeciGle',
        balance_after: '********.63',
        balance_before: '31045137.63'
      },
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-y9I28dcrI7pWhJWe',
        current_balance: '31045137.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'KES',
        description: 'Settlement For KPY-SET-y9I28dcrI7pWhJWe',
        created_at: '2022-06-22T12:37:24.000Z',
        history_date: '2022-06-22 13:37:24',
        transaction_reference: 'KPY-SET-y9I28dcrI7pWhJWe',
        balance_after: '31045137.63',
        balance_before: '31045037.63'
      },
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-DOvNUQCYOUFmBW8C',
        current_balance: '31045037.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'KES',
        description: 'Settlement For KPY-SET-DOvNUQCYOUFmBW8C',
        created_at: '2022-06-22T09:59:54.000Z',
        history_date: '2022-06-22 10:59:54',
        transaction_reference: 'KPY-SET-DOvNUQCYOUFmBW8C',
        balance_after: '31045037.63',
        balance_before: '31044937.63'
      },
      {
        source_type: 'settlement',
        source_reference: 'KPY-SET-S50H3fHUBYoVsP35',
        current_balance: '31044937.63',
        amount: '100.00',
        direction: 'credit',
        currency: 'KES',
        description: 'Settlement For KPY-SET-S50H3fHUBYoVsP35',
        created_at: '2022-06-22T09:54:43.000Z',
        history_date: '2022-06-22 10:54:43',
        transaction_reference: 'KPY-SET-S50H3fHUBYoVsP35',
        balance_after: '31044937.63',
        balance_before: '31044837.63'
      }
    ],
    paging: {
      total_items: 94,
      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    }
  }
};

export const mockedMerchantBalance = {
  data: {
    NGN: {
      pending_balance: 73501.74,
      available_balance: ********.63,
      ledger_balance: 31118739.369999997
    },
    USD: {
      pending_balance: 219769.78,
      available_balance: 5897091.46,
      ledger_balance: 6116861.24,
      reserve_balance: 0
    },
    KES: {
      pending_balance: 100000,
      available_balance: 120577.89,
      ledger_balance: 220577.89,
      reserve_balance: 0
    }
  }
};

export const mockedCountries = {
  data: [
    {
      id: 'NG',
      name: 'Nigeria',
      states: [{ name: 'Adamawa' }, { name: 'Imo' }, { name: 'Oyo' }]
    }
  ]
};

export const mockedCurrencies = {
  data: [
    {
      id: 1,
      name: 'Nigerian Naira',
      code: 'NGN',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            },
            {
              label: 'Pay with Bank Transfer',
              value: 'bank_transfer'
            },
            {
              label: 'Virtual Bank Account',
              value: 'virtual_bank_account'
            },
            {
              label: 'Pay With Bank',
              value: 'pay_with_bank'
            }
          ],
          payout: [
            {
              label: 'Bank Account',
              value: 'bank_account'
            },
            {
              label: 'Korapay Wallet',
              value: 'disbursement_wallet'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Settlement Account',
            value: 'settlement_account'
          },
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    },
    {
      id: 2,
      name: 'United States Dollar',
      code: 'USD',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            }
          ],
          payout: [
            {
              label: 'Korapay Wallet',
              value: 'disbursement_wallet'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    },
    {
      id: 3,
      name: 'Euro',
      code: 'EUR',
      enabled: false,
      meta_data: {
        payment_types: {
          payin: [],
          payout: []
        }
      }
    },
    {
      id: 4,
      name: 'Kenyan Shilling',
      code: 'KES',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            },
            {
              label: 'Mobile Money',
              value: 'mobile_money'
            }
          ],
          payout: [
            {
              label: 'Bank Account',
              value: 'bank_account'
            },
            {
              label: 'Mobile Money',
              value: 'mobile_money'
            },
            {
              label: 'Korapay Wallet',
              value: 'disbursement_wallet'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    },
    {
      id: 5,
      name: 'Ghanaian Cedi',
      code: 'GHS',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            },
            {
              label: 'Mobile Money',
              value: 'mobile_money'
            }
          ],
          payout: [
            {
              label: 'Mobile Money',
              value: 'mobile_money'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    }
  ]
};

export const mockedTeamMembers = {
  data: []
};

export const mockedSettlementAccounts = {
  data: {
    NGN: [
      {
        id: 1,
        account_id: 1,
        currency: 'NGN',
        status: 'active',
        details: {
          account_number: '**********',
          account_name: 'Test Account',
          bank_code: '033'
        },
        bank: {
          id: 1,
          name: 'United Bank for Africa'
        }
      },
      {
        id: 2,
        account_id: 1,
        currency: 'NGN',
        status: 'under_review',
        details: {
          account_number: '**********',
          account_name: 'Test Account II',
          bank_code: '033'
        },
        bank: {
          id: 1,
          name: 'United Bank for Africa'
        }
      },
      {
        id: 3,
        account_id: 1,
        currency: 'NGN',
        status: 'rejected',
        status_details: [
          {
            treated_on: '2022-06-01T05:00:00Z',
            comment: 'The account name does not match the business name. Please update'
          }
        ],
        details: {
          account_number: '**********',
          account_name: 'Test Account II',
          bank_code: '033'
        },
        bank: {
          id: 1,
          name: 'United Bank for Africa'
        }
      }
    ]
  }
};

export const mockedRollingReserveHistory = {
  data: [
    {
      source_type: 'settlement',
      amount: '289.50',
      direction: 'credit',
      currency: 'USD',
      description: 'Rolling Reserve Hold For Settlement KPY-SET-JFQSdXFFlfTgLAZr',
      available_on: '2023-10-09 01:00:00',
      history_date: '2023-04-12 16:18:48',
      transaction_reference: 'KPY-SET-JFQSdXFFlfTgLAZr',
      balance_after: '1897.69',
      balance_before: '1608.19'
    },
    {
      source_type: 'settlement',
      amount: '594.93',
      direction: 'credit',
      currency: 'USD',
      description: 'Rolling Reserve Hold For Settlement KPY-SET-thY3sdjZG4J0al2x',
      available_on: '2023-10-01 01:00:00',
      history_date: '2023-04-04 10:58:40',
      transaction_reference: 'KPY-SET-thY3sdjZG4J0al2x',
      balance_after: '1608.19',
      balance_before: '1013.26'
    }
  ]
};

export const mockedIndustries = {
  data: {
    industries: [
      { id: 'agriculture', label: 'Agriculture' },
      { id: 'betting_or_lottery', label: 'Betting or Lottery' },
      { id: 'cable_or_satellite_or_pay_television', label: 'Cable Or Satellite or Pay Television' },
      { id: 'clothing_and_accessories', label: 'Clothing and Accessories' },
      { id: 'commercial_footwear', label: 'Commercial Footwear' },
      { id: 'credit_Lending_companies', label: 'Credit Lending Companies' },
      { id: 'crowd_funding', label: 'Crowd Funding' },
      { id: 'department_stores', label: 'Department Stores' },
      { id: 'digital_goods_or_entertainment', label: 'Digital Goods or Entertainment' },
      { id: 'education', label: 'Education' },
      { id: 'electronics_store', label: 'Electronics Store' },
      { id: 'electricity_or_energy', label: 'Electricity or Energy' },
      { id: 'e-commerce', label: 'E-commerce' },
      { id: 'financial_institutions_or_funds_manager', label: 'Financial Institutions or Funds Manager' },
      { id: 'general_contractors', label: 'General Contractors' },
      { id: 'general_merchandise_stores', label: 'General Merchandise Stores' },
      { id: 'grocery_stores_and_supermarkets', label: 'Grocery Stores and Supermarkets' },
      { id: 'hospitality_or_lodging', label: 'Hospitality or Lodging' },
      { id: 'insurance', label: 'Insurance' },
      { id: 'logistics', label: 'Logistics' },
      { id: 'money_transfer', label: 'Money Transfer' },
      { id: 'ngo_or_npo', label: 'NGO Or NPO' },
      { id: 'other_financial_institution', label: 'Other Financial Institution' },
      { id: 'payment_aggregation', label: 'Payment Aggregation' },
      { id: 'professional_services', label: 'Professional Services' },
      { id: 'real_estate_or_facility_management', label: 'Real Estate or Facility Management' },
      { id: 'religious_organizations', label: 'Religious Organizations' },
      { id: 'security_broker_or_dealer', label: 'Security Broker or Dealer' },
      { id: 'tax_or_accounting_or_audit', label: 'Tax Or Accounting or Audit' },
      { id: 'mobility_service_providers_or_ride_hailing', label: 'Mobility Service Providers or Ride Hailing' },
      { id: 'telecommunication', label: 'Telecommunication' },
      { id: 'ticketing_or_events', label: 'Ticketing or Events' },
      { id: 'transportation_services', label: 'Transportation Services' },
      { id: 'travel_or_airlines', label: 'Travel or Airlines' },
      { id: 'tour', label: 'Tour' },
      { id: 'video_games', label: 'Video Games' },
      { id: 'other', label: 'Other' }
    ]
  }
};

export const exchangeRate = {
  data: [
    {
      data: {
        reference: 'KPY-CW-N7jMjG0F9okcHlOS',
        status: 'success',
        transfer_fee: 0.3,
        total_amount: 99.7,
        wallet_currency: 'NGN',
        rate: {
          time_stamp: '45s',
          base_currency: 'USD',
          base_rate: 1,
          exchange_rate: 750.63
        }
      }
    }
  ]
};
export const mockedBanks = {
  data: [
    {
      name: ' AL-Barakah Microfinance Bank',
      slug: 'al-barakah-mfb',
      code: '090133',
      nibss_bank_code: '090133',
      country: 'NG'
    },
    {
      name: '9 Payment Service Bank',
      slug: '9payment',
      code: '000802',
      nibss_bank_code: '000802',
      country: 'NG'
    },
    {
      name: 'AB Microfinance Bank',
      slug: 'ab-mfb',
      code: '090270',
      nibss_bank_code: '090270',
      country: 'NG'
    },
    {
      name: 'Abbey Mortgage Bank',
      slug: 'abbey-mb',
      code: '070010',
      nibss_bank_code: '070010',
      country: 'NG'
    },
    {
      name: 'Above Only Microfinance Bank',
      slug: 'above-only-mfb',
      code: '090260',
      nibss_bank_code: '090260',
      country: 'NG'
    },
    {
      name: 'ABU Microfinance Bank',
      slug: 'abu-mfb',
      code: '090197',
      nibss_bank_code: '090197',
      country: 'NG'
    },
    {
      name: 'Access Bank Nigeria',
      slug: 'access',
      code: '044',
      nibss_bank_code: '000014',
      country: 'NG'
    },
    {
      name: 'Access Bank Plc (Diamond)',
      slug: 'diamond',
      code: '063',
      nibss_bank_code: '000005',
      country: 'NG'
    },
    {
      name: 'United Bank for Africa',
      slug: 'uba',
      code: '033',
      nibss_bank_code: '000015',
      country: 'NG'
    }
  ]
};

export const mockedBankResolve = {
  data: {
    bank_name: 'United Bank for Africa',
    bank_code: '033',
    account_number: '**********',
    account_name: 'Test Account'
  }
};
export const mockedEmailConfigurations = {
  status: true,
  message: 'Notification configuration retrieved successfully',
  data: {
    id: 14,
    merchant_id: 44,
    configuration: {
      payin: {
        support_emails: ['<EMAIL>'],
        all_payins: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        other_payin_emails: {
          enabled: true,
          email_recipients: ['<EMAIL>']
        }
      },
      payout: {
        support_emails: ['<EMAIL>'],
        api_bulk_payout_breakdown: {
          enabled: false,
          email_recipients: ['<EMAIL>']
        },
        api_bulk_payout_initiation: {
          enabled: true,
          email_recipients: ['<EMAIL>']
        },
        api_single_payout_completion: {
          enabled: false,
          email_recipients: ['<EMAIL>']
        },
        dashboard_single_payout_completion: {
          enabled: true,
          email_recipients: ['<EMAIL>']
        }
      },
      dispute: {
        refund: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        chargeback: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        }
      },
      card_issuance: {
        transaction_limits: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        issuing_balance_funding: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        virtual_card_termination: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        }
      },
      product_and_marketing: {
        enabled: true,
        email_recipients: ['<EMAIL>', '<EMAIL>']
      }
    },
    createdAt: '2023-07-13T11:25:10.000Z',
    updatedAt: '2023-07-13T13:26:36.000Z'
  }
};

export const mockedSuccessResponse = {
  message: 'Successfully'
};
export const mockedPayoutSummary = {
  status: true,
  message: 'Bulk transaction retrieved successfully',
  data: {
    amount: '1500.00',
    total_bulk_amount: 1540.31,
    date_completed: '24 Mar 2023, 10:32AM',
    payout_count: 3,
    currency: 'NGN',
    reference: 'Test_Bulk_reference_Act1Scene1',
    status: 'processing',
    description: 'test bulk transfer',
    failed_transactions: 2,
    successful_transactions: 0,
    pending_transactions: 1,
    processing_transactions: 0
  }
};

export const mockedNGNBulkPayouts = {
  data: {
    data: [
      {
        status: 'processing',
        currency: 'NGN',
        description: 'test bulk transfer',
        batch_reference: 'Test_Bulk_reference_Act1Scene1',
        created_at: '2023-03-24T09:30:13.000Z'
      },
      {
        status: 'failed',
        currency: 'NGN',
        description: 'test bulk transfer',
        batch_reference: 'Test_Bulk_reference_Act1Scene10',
        created_at: '2023-03-26T19:25:02.000Z'
      },
      {
        status: 'complete',
        currency: 'NGN',
        description: 'test bulk transfer',
        batch_reference: 'Test_Bulk_reference_Act1Scene11',
        created_at: '2023-03-26T19:30:15.000Z'
      },
      {
        status: 'complete',
        currency: 'NGN',
        description: 'test bulk transfer',
        batch_reference: 'Test_Bulk_reference_Act1Scene12',
        created_at: '2023-03-27T10:19:20.000Z'
      },
      {
        status: 'processing',
        currency: 'NGN',
        description: 'test bulk transfer',
        batch_reference: 'Test_Bulk_reference_Act1Scene2',
        created_at: '2023-03-24T09:39:59.000Z'
      }
    ],
    paging: {
      total_items: 38,
      page_size: 5,
      current: 1,
      count: 5,
      next: 2
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/i/transactions/bulk-transfers?currency=NGN&page=1&limit=5',
        rel: 'current',
        method: 'GET'
      },
      {
        href: 'https://api.koraapi.com/merchant/api/i/transactions/bulk-transfers?currency=NGN&page=2&limit=5',
        rel: 'next',
        method: 'GET'
      }
    ]
  }
};

export const mockedNGNBulkPayoutDetials = {
  data: {
    data: [
      {
        reference: '001_bulktransfer_Act1Scene1',
        amount: '1500.00',
        currency: 'NGN',
        narration: 'Test transfer to F4B Developers',
        status: 'pending',
        batch_reference: 'Test_Bulk_reference_Act1Scene1',
        type: 'bank_account',
        customer: {
          name: 'Lynda',
          email: '<EMAIL>'
        },
        transaction_date: '2023-03-24 10:30:13',
        bank_account: {
          bank_code: '033',
          account_number: '**********',
          account_name: 'Test Account'
        }
      },
      {
        reference: '002_bulktransfer_Act1Scene12',
        amount: '2500.00',
        currency: 'NGN',
        narration: 'Test transfer to F4B Developers',
        status: 'failed',
        batch_reference: 'Test_Bulk_reference_Act1Scene1',
        type: 'bank_account',
        customer: {
          name: 'Lynda',
          email: '<EMAIL>'
        },
        transaction_date: '2023-03-24 10:30:13',
        bank_account: {
          bank_code: null,
          account_number: null,
          account_name: null
        }
      },
      {
        reference: '003_bulktransfer_Act1Scene13',
        amount: '3500.00',
        currency: 'NGN',
        narration: 'Test transfer to F4B Developers',
        status: 'failed',
        batch_reference: 'Test_Bulk_reference_Act1Scene1',
        type: 'bank_account',
        customer: {
          name: 'Chijioke',
          email: '<EMAIL>'
        },
        transaction_date: '2023-03-24 10:30:13',
        bank_account: {
          bank_code: null,
          account_number: null,
          account_name: null
        }
      }
    ]
  }
};

export const mockedCardAccessStatus = {
  data: {
    status: true,
    message: 'Merchant access and validation retrieved successfully',
    data: {
      access: {
        customer: {
          status: 'inactive'
        },
        reserved: {
          status: 'inactive'
        }
      },
      validation: {
        count: 0,
        status: 'pending'
      }
    }
  }
};

export const mockedReservedCardholder = {
  data: {
    first_name: 'Kora',
    last_name: 'HQ-Developer'
  }
};

export const mockedBvnValidation = {
  status: true,
  message: 'BVN successfully validated',
  data: {
    firstName: 'John',
    lastName: 'Doe'
  }
};

export const mockedIssuedCardDetails = {
  status: true,
  message: 'Card details retrieved successfully',
  data: {
    reference: 'aDemoRef-12345',
    type: 'virtual',
    first_six: '222329',
    last_four: '9283',
    brand: 'visa',
    expiry_month: '08',
    expiry_year: '2028',
    currency: 'USD',
    balance: 0,
    status: 'active',
    billing: {
      address1: '123 Main St',
      city: 'San Francisco',
      state: 'CA',
      country: 'US',
      zip_code: '94105'
    },
    card_holder: {
      first_name: 'Kora',
      last_name: 'Dev',
      email: '<EMAIL>',
      address: {
        city: 'Lekki',
        state: 'Lagos',
        street: 'No 14, Kora street',
        country: 'NG',
        zip_code: '100100'
      }
    },
    date_created: '2023-08-22T10:50:19.000Z'
  }
};
export const mockedWebhooksData = {
  data: {
    id: 111771,
    transaction_reference: 'KPY-RA-8DD83298337D',
    status: 'delivered',
    webhook_date: '2023-03-18 15:00:28',
    last_attempt: '2023-04-18 15:00:28',
    merchant_id: 89,
    webhook_url: 'https://webhook.site.com',
    type: 'payin',
    response_code: 200,
    repeatable: true,
    attempts: 2,
    limit: 3,
    request_payload: {
      fee: 40.32,
      amount: 500,
      status: 'success',
      currency: 'NGN',
      reference: 'KPY-RA-8DD83298337D',
      transaction_date: '2023-07-19T15:15:48.220Z',
      payment_reference: 'KPY-PRWS-564Jfjhkjf'
    },
    createdAt: '2023-05-18 15:00:28',
    updatedAt: '2023-06-18 15:00:28'
  }
};
export const mockedIDValidation = {
  message: 'Identity validated successfully',
  status: true,
  data: {
    id: '65e9f9fc8cd5636f4bf18aaf',
    nin: '***********',
    type: 'bvn',
    mobile: '***********',
    status: 'found',
    country: 'NG',
    idNumber: '***********',
    lastName: 'Doe',
    createdAt: '2024-03-07T17:31:41.661Z',
    firstName: 'John',
    isConsent: true,
    businessId: '64b8396ee0026add4ffffe81',
    middleName: null,
    dateOfBirth: '1988-04-04',
    requestedAt: '2024-03-07T17:31:41.628Z'
  }
};

export const mockedRVCBusinessMerchantDetails = {
  data: {
    street: 'Lekki Lagos',
    date_of_birth: '1988-04-04',
    country: 'Nigeria',
    name: 'John Doe',
    representatives: [
      { name: 'Jane Doe', role: 'Developer' },
      { name: 'James Doe', role: 'Developer' }
    ]
  }
};

export const mockedVerificationData = {
  status: true,
  message: 'Verifications retrieved successfully',
  data: {
    data: [
      {
        reference: 'VR-lXxzVg2Pcex91MrZW',
        id: '***********',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        phone_number: '***********',
        type: 'kyc',
        class: 'id_lookup',
        identity_type: 'ng_bvn',
        identity_type_description: 'Nigerian Bank Verification Number',
        country: 'ng',
        merchant: {
          reference: 'MC-wwsZZdfehhutwexxx',
          name: 'Kora HQ Developer',
          email: '<EMAIL>'
        },
        requested_by: 'API User',
        status: 'valid',
        date_created: '2024-04-16T10:42:47.930Z'
      }
    ],
    paging: {
      total_items: 1,
      page_size: 10,
      current: 1,
      count: 1
    }
  }
};
export const mockedVirtualAccountNumbers = {
  status: true,
  message: 'Virtual bank accounts retrieved successfully',
  data: {
    data: [
      {
        status: 'deactivated',
        account_number: '********',
        account_name: null,
        korapay_reference: 'KPY-VA-rR6xEV5QfdGPoTb',
        account_reference: 'ovie-master-1',
        bank_name: 'FINANCIAL HOUSE LIMITED',
        iban: '**********************',
        tier: 2,
        currency: 'EUR',
        created_at: '2024-02-20T08:41:16.000Z',
        merchant: {
          name: 'ovie5th',
          id: 207
        },
        account_holder: {
          first_name: 'Sarah',
          last_name: 'Doe',
          id: 72
        },
        pending_upgrade_request: false
      },
      {
        status: 'pending',
        account_number: null,
        account_name: null,
        korapay_reference: 'KPY-VA-xC9krX8aEyCbx3G',
        account_reference: 'AcmWYgVOoq3xkm51wwwvf',
        bank_name: null,
        iban: null,
        tier: 1,
        currency: 'EUR',
        created_at: '2024-02-15T05:44:01.000Z',
        merchant: {
          name: 'ovie5th',
          id: 207
        },
        account_holder: {
          first_name: 'Sarah',
          last_name: 'Doe',
          id: 13
        },
        pending_upgrade_request: false
      },
      {
        status: 'pending',
        account_number: null,
        account_name: null,
        korapay_reference: 'KPY-VA-ig5tWGOAHsWxAzx',
        account_reference: 'AcmWYgVOoq3xkm51www',
        bank_name: null,
        iban: null,
        tier: 1,
        currency: 'GBP',
        created_at: '2024-02-15T05:30:08.000Z',
        merchant: {
          name: 'ovie5th',
          id: 207
        },
        account_holder: {
          first_name: 'Sarah',
          last_name: 'Doe',
          id: 13
        },
        pending_upgrade_request: false
      },
      {
        id: 727,
        status: 'suspended',
        account_number: '**********',
        account_name: 'ovie5th',
        korapay_reference: 'KPY-VA-fzykA2n4tSEesq0',
        account_reference: '********23',
        bank_name: 'Wema Bank',
        iban: null,
        tier: null,
        currency: 'NGN',
        created_at: '2022-11-15T15:29:24.000Z',
        merchant: {
          name: 'ovie5th',
          id: 207
        },
        account_holder: null
      },
      {
        id: 723,
        status: 'suspended',
        account_number: '**********',
        account_name: 'Ovie Idholo',
        korapay_reference: 'KPY-VA-K3QWe0T0ToDxe2H',
        account_reference: '********03',
        bank_name: 'Wema Bank',
        iban: null,
        tier: null,
        currency: 'NGN',
        created_at: '2022-11-15T13:18:05.000Z',
        merchant: {
          name: 'ovie5th',
          id: 207
        },
        account_holder: null
      }
    ],
    paging: {
      total_items: 5,
      page_size: 10,
      current: 1,
      count: 5
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/i/virtual-bank-account?page=1&limit=10',
        rel: 'current',
        method: 'GET'
      }
    ]
  }
};

export const mockedSettlementConversionData = {
  data: {
    USD: {
      activated: true,
      enabled: true,
      markup: {
        kora: {
          value: 5
        },
        merchant: {
          value: 10.22,
          limit: 10
        }
      }
    }
  }
};

export const mockedGHMobileMoneyNetwork = {
  data: [
    {
      name: 'AIRTEL',
      slug: 'airtel-gh',
      code: '0005',
      min: 10,
      max: 50000,
      country: 'GH'
    },
    {
      name: 'MTN',
      slug: 'mtn-gh',
      code: '0004',
      min: 10,
      max: 50000,
      country: 'GH'
    }
  ]
};

export const mockedSplitAccounts = {
  data: [
    {
      reference: 'account-ref-1',
      account_number: '**********',
      account_name: 'First',
      bank_code: '058',
      bank_name: 'UBA',
      relationship: 'test relationship'
    },
    {
      reference: 'account-ref-2',
      account_number: '**********',
      account_name: 'Second',
      bank_code: '058',
      bank_name: 'GTBank Plc',
      relationship: 'test relationship'
    },
    {
      reference: 'account-ref-3',
      account_number: '**********',
      account_name: 'Third',
      bank_code: '044',
      bank_name: 'Access Bank Nigeria',
      relationship: 'test relationship'
    }
  ]
};

export const mockedCreateSplitAccountType = {
  data: {
    reference: 'account-ref-4',
    account_number: '**********',
    account_name: 'Fourth',
    bank_code: '033',
    bank_name: 'United Bank for Africa',
    currency: 'NGN'
  }
};

export const mockedSplitRules = {
  data: {
    data: [
      {
        split_reference: 'split-ref-1',
        currency: 'NGN',
        split_name: 'Split 1',
        split_description: 'test split 1',
        split_type: 'percentage',
        split_accounts: [
          {
            value: '50',
            split_destination_type: 'account',
            split_destination: {
              reference: 'account-ref-1',
              account_number: '**********',
              account_name: 'First',
              bank_code: '058',
              bank_name: 'GTBank Plc'
            }
          }
        ]
      }
    ],
    paging: {
      total_items: 1,
      page_size: 5,
      current: 1,
      count: 1
    }
  }
};

export const mockedLienDetailsData = {
  data: {
    data: {
      merchant_id: 87,
      reference: 'KPY-LIEN-chGzjolFFqsU0Bm',
      amount: '1180.63',
      currency: 'NGN',
      status: 'released',
      source_type: 'payment_source',
      source_reference: 'KPY-CW-Ug7IMemshdYsgN7f',
      event_history: [
        {
          status: 'active',
          reason: 'failure',
          date: '2025-02-21T11:58:47.308Z'
        },
        {
          status: 'released',
          reason: 'all',
          date: '2025-02-22T11:53:32.855Z'
        }
      ],
      date_completed: '2025-02-22T11:53:32.000Z',
      reason: 'failure',
      created_at: '2025-02-21T11:58:47.000Z',
      updated_at: '2025-02-22T11:53:32.000Z',
      merchant_name: 'GIG Logistics'
    }
  }
};
export const mockedNGNPayoutDetails = {
  data: {
    amount: '3000.00',
    fee: '75.00',
    vat: '5.63',
    amount_charged: '3080.63',
    currency: 'NGN',
    amount_paid: '3000.00',
    status: 'success',
    reference: 'payout-ref',
    narration: 'Test transfer',
    channel: 'api',
    created_at: '2024-02-18T01:49:34.000Z',
    trace_id: '54321',
    message: 'Payout successful',
    payment_destination_type: 'bank_account',
    batch_reference: 'test-payouts-batch',
    account_id: 70,
    unique_reference: 'kora-ref',
    merchant_bears_cost: true,
    transaction_date: '2024-02-18 02:49:34',
    completed_at: '2024-02-18 02:52:14',
    payment: {
      description: 'Test transfer',
      reference: 'KPY-PAY-Bj5ugX5zKkvG',
      customer: {
        id: 7794,
        name: 'Chijioke',
        email: '<EMAIL>'
      }
    },
    can_request_webhook: true,
    destination: {
      type: 'bank_account',
      details: {
        bank_name: 'Access Bank Nigeria',
        account_number: '**********',
        account_name: 'user'
      }
    }
  }
};

export const mockedLienHistoryData = {
  data: {
    data: [
      {
        merchant_id: 87,
        reference: 'KPY-LIEN-chGzjolFFqsU0Bm',
        amount: '1180.63',
        currency: 'NGN',
        status: 'released',
        source_type: 'payment_source',
        source_reference: 'KPY-CW-Ug7IMemshdYsgN7f',
        event_history: [
          {
            status: 'active',
            reason: 'failure',
            date: '2025-02-21T11:58:47.308Z'
          },
          {
            status: 'released',
            reason: 'all',
            date: '2025-02-22T11:53:32.855Z'
          }
        ],
        date_completed: '2025-02-22T11:53:32.000Z',
        reason: 'failure',
        created_at: '2025-02-21T11:58:47.000Z',
        updated_at: '2025-02-22T11:53:32.000Z',
        merchant_name: 'GIG Logistics',
        merchant: {
          name: 'GIG Logistics'
        }
      },
      {
        merchant_id: 87,
        reference: 'KPY-LIEN-tvqaFFyip0XnK0I',
        amount: '1000.00',
        currency: 'NGN',
        status: 'active',
        source_type: null,
        source_reference: null,
        event_history: [
          {
            status: 'active',
            reason: 'unsupported',
            date: '2025-02-21T00:14:43.715Z'
          }
        ],
        date_completed: null,
        reason: 'unsupported',
        created_at: '2025-02-21T00:14:43.000Z',
        updated_at: '2025-02-21T00:14:43.000Z',
        merchant_name: 'GIG Logistics',
        merchant: {
          name: 'GIG Logistics'
        }
      },
      {
        merchant_id: 87,
        reference: 'KPY-LIEN-bwNlG6r411Xd4Pn',
        amount: '30000.00',
        currency: 'NGN',
        status: 'released',
        source_type: null,
        source_reference: null,
        event_history: [
          {
            status: 'active',
            reason: 'failure',
            date: '2025-02-21T00:13:23.012Z'
          },
          {
            status: 'released',
            reason: 'all',
            date: '2025-02-21T00:21:11.774Z'
          }
        ],
        date_completed: '2025-02-21T00:21:11.000Z',
        reason: 'failure',
        created_at: '2025-02-21T00:13:23.000Z',
        updated_at: '2025-02-21T00:21:11.000Z',
        merchant_name: 'GIG Logistics',
        merchant: {
          name: 'GIG Logistics'
        }
      },
      {
        merchant_id: 87,
        reference: 'KPY-LIEN-HVdoDxt78vtl0an',
        amount: '245.84',
        currency: 'NGN',
        status: 'released',
        source_type: null,
        source_reference: null,
        event_history: [
          {
            status: 'active',
            reason: 'failure',
            date: '2025-02-20T22:48:38.677Z'
          },
          {
            status: 'released',
            reason: 'all',
            date: '2025-02-21T00:06:16.026Z'
          }
        ],
        date_completed: '2025-02-21T00:06:16.000Z',
        reason: 'failure',
        created_at: '2025-02-20T22:48:38.000Z',
        updated_at: '2025-02-21T00:06:16.000Z',
        merchant_name: 'GIG Logistics',
        merchant: {
          name: 'GIG Logistics'
        }
      },
      {
        merchant_id: 87,
        reference: 'KPY-LIEN-efRQ77NmuChjwAc',
        amount: '11000.00',
        currency: 'NGN',
        status: 'released',
        source_type: null,
        source_reference: null,
        event_history: [
          {
            status: 'active',
            reason: 'unsupported',
            date: '2025-02-20T22:46:30.684Z'
          },
          {
            status: 'released',
            reason: 'all',
            date: '2025-02-21T00:10:25.298Z'
          }
        ],
        date_completed: '2025-02-21T00:10:25.000Z',
        reason: 'unsupported',
        created_at: '2025-02-20T22:46:30.000Z',
        updated_at: '2025-02-21T00:10:25.000Z',
        merchant_name: 'GIG Logistics',
        merchant: {
          name: 'GIG Logistics'
        }
      },
      {
        merchant_id: 87,
        reference: 'KPY-LIEN-6ohauiVWBBS1r9D',
        amount: '20000.00',
        currency: 'NGN',
        status: 'released',
        source_type: null,
        source_reference: null,
        event_history: [
          {
            status: 'active',
            reason: 'fraud',
            date: '2025-02-20T09:52:29.327Z'
          },
          {
            status: 'released',
            reason: 'all',
            date: '2025-02-21T00:08:11.509Z'
          }
        ],
        date_completed: '2025-02-21T00:08:11.000Z',
        reason: 'fraud',
        created_at: '2025-02-20T09:52:29.000Z',
        updated_at: '2025-02-21T00:08:11.000Z',
        merchant_name: 'GIG Logistics',
        merchant: {
          name: 'GIG Logistics'
        }
      }
    ],
    paging: {
      total_items: 6,
      page_size: 10,
      current: 1,
      count: 6
    }
  }
};

export const mockedLienSummaryData = {
  data: {
    status: true,
    code: 'AA000',
    message: 'Active lien summary',
    data: {
      count: 1,
      totalAmount: 1000
    }
  }
};
export const mockedNGNPayinDetails = {
  data: {
    reference: 'payin-ref',
    status: 'success',
    amount: '1000.00',
    amount_charged: '1000.00',
    amount_paid: '1000.00',
    amount_collected: '1000.00',
    fee: '0.00',
    vat: '0.00',
    narration: 'great',
    payment_source_type: 'card',
    payment_source_id: null,
    payment_id: 91605,
    currency: 'NGN',
    channel: 'modal',
    payment_reversals_type: 1,
    meta: {
      stan: '1234',
      receipt: 'rnn-number',
      card_details: {
        card_type: 'mastercard',
        first_six: 'first_six',
        last_four: 'last_four',
        masked_pan: '************2595',
        expiry_year: '30',
        expiry_month: '09'
      },
      gateway_code: '00',
      tokenization: {
        can_tokenize: false
      },
      support_message: "Didn't get the OTP? Dial *322*0# on your phone (MTN, Etisalat, Airtel) Glo, use *805*0#."
    },
    auth_data: {
      auth_model: 'OTP'
    },
    message: 'Card charged successfully',
    merchant_bears_cost: true,
    transaction_date: '2024-12-18 15:00:25',
    completed_at: '2024-12-18 15:00:28',
    payment: {
      reference: 'payment-ref',
      customer: {
        name: 'John Doe',
        email: '<EMAIL>'
      },
      sentinal_transaction: null,
      payment_conversion: null
    },
    payment_reversals: [
      {
        id: 1702,
        reference: 'payment-reversal-ref',
        destination: 'customer',
        reversal_reason: 'Cancelled Transaction',
        status: 'success',
        amount: '1000.00',
        type: 'refund',
        created_at: '2025-01-03 14:48:38',
        completed_at: null,
        payment_reversal_payouts: [
          {
            account_name: 'Test Account',
            account_number: '**********',
            bank_name: 'United Bank for Africa',
            message: 'Successful',
            trace_id: '310944836922680325610861549648',
            reference: 'KPY-DR-1O3wYCKwOTFtdpJ',
            status: 'success',
            created_at: '2024-08-25T22:29:52.000Z'
          }
        ]
      }
    ],
    source: {
      type: 'card',
      details: {
        masked_pan: '************2595',
        card_type: 'mastercard'
      }
    },
    can_reverse_payment: true,
    can_request_webhook: true
  }
};

export const mockedPayinRefundQueryData = {
  data: {
    can_refund: true,
    transaction_amount: 9000,
    available_balance: 100000.0,
    available_refundable_amount: 9000,
    minimum_refundable_amount: 1,
    maximum_refundable_amount: 9000,
    payment_source_reference: 'payment-source-ref',
    refund_reason: 'Original amount for this transaction is NGN 9000'
  }
};

export const mockedPayinDetailsRelatedTransactions = {
  data: {
    data: [
      {
        reference: 'auth-ref',
        status: 'pre_authorized',
        amount: '5.00',
        amount_charged: '0.00',
        amount_paid: '0.00',
        amount_collected: '0.00',
        fee: '0.00',
        vat: '0.00',
        currency: 'NGN',
        meta: {
          stan: '00000',
          receipt: '000000000',
          card_details: {
            card_type: 'mastercard',
            first_six: '111111',
            last_four: '2222',
            masked_pan: '************2222',
            expiry_year: '29',
            expiry_month: '06'
          },
          gateway_code: '00',
          charge_operation: 'pre_authorization',
          authorization_code: '876652',
          total_captured_amount: 0,
          total_refunded_amount: 0,
          total_authorized_amount: 5
        },
        transaction_date: '2024-09-05 16:12:01',
        completed_at: null
      },
      {
        reference: 'capture-ref-1',
        status: 'success',
        amount: '5.00',
        amount_charged: '5.00',
        amount_paid: '4.59',
        amount_collected: '5.00',
        fee: '0.38',
        vat: '0.03',
        currency: 'NGN',
        meta: {
          stan: '225954',
          receipt: '000000000',
          card_details: {
            card_type: 'mastercard',
            first_six: '111111',
            last_four: '2222',
            masked_pan: '************2222',
            expiry_year: '29',
            expiry_month: '06'
          },
          gateway_code: '00',
          charge_operation: 'capture',
          authorization_code: '876652',
          total_captured_amount: 5,
          total_refunded_amount: 0,
          total_authorized_amount: 5
        },
        transaction_date: '2024-09-05 16:12:28',
        completed_at: '2024-09-05 16:12:30'
      }
    ]
  }
};

export const mockedSplitSettings = {
  data: {
    enabled: true,
    split_account: { max_count: 5, max_total_rate: 70, min_settlement_amount: 1000 }
  }
};

export const mockedAllSplitAccountsSummary = {
  data: {
    data: [
      {
        account_number: '**********',
        account_name: 'Test1',
        bank_code: '000',
        bank_name: 'Access Bank',
        total_settlement: '0.0000',
        last_settlement_date: null
      },
      {
        account_number: '**********',
        account_name: 'Test2',
        bank_code: '111',
        bank_name: 'Access Bank',
        total_settlement: '100.0000',
        last_settlement_date: '2025-05-05 09:00:00'
      }
    ],
    paging: {
      total_items: 2,
      page_size: 10,
      current: 1,
      count: 5
    }
  }
};

export const mockedAllSplitPayments = {
  data: {
    data: [
      {
        status: 'processing',
        settlement_payout_reference: 'ref1',
        recipient_name: 'test1',
        recipient_number: '**********',
        settlement_payout_amount: '100.00',
        transaction_date: '2025-05-05 09:00:00'
      },
      {
        status: 'success',
        settlement_payout_reference: 'ref2',
        recipient_name: 'test2',
        recipient_number: '**********',
        settlement_payout_amount: '200.00',
        transaction_date: '2025-06-06 08:00:00'
      }
    ],
    paging: {
      total_items: 2,
      page_size: 10,
      current: 1,
      count: 5
    }
  }
};

export const mockedCreateSplitAccountSendOtpResponse = {
  data: {
    auth_data: {
      type: 'otp',
      identifier: 'test-identifier',
      service_identifier: 'test-service-identifier'
    }
  }
};

export const mockedSplitPaymentsSettlementDetails = {
  data: {
    status: 'processing',
    trace_id: null,
    amount: '100.0000',
    fee: '20.0000',
    vat: '2.0000',
    reference: 'ref-1',
    created_at: '2025-05-05 09:00:00',
    completed_at: '2025-05-05 10:00:00',
    recipient_information: {
      bank_name: 'access bank',
      account_name: 'test1',
      account_number: '**********',
      payment_method: 'card'
    },
    amount_charged: '2000.00',
    settlement: {
      channel: 'card',
      currency: 'NGN',
      reference: 'settlement-ref-1'
    }
  }
};

export const mockedSplitAccountDetails = {
  data: {
    account_number: '**********',
    account_name: 'test1',
    bank_code: '057',
    bank_name: 'access bank',
    created_at: '2025-05-05 09:00:00',
    total_settlement: '0.0000',
    last_settlement_date: null
  }
};
export const mockedTransactionReportConfiguration = {
  data: {
    merchant_id: 106,
    type: 'account_report',
    configuration: {
      account_report: {
        transaction_report: {
          enabled: false,
          interval: 'weekly',
          email_recipients: ['<EMAIL>']
        }
      }
    },
    created_at: '2024-11-20T09:26:39.945Z',
    updated_at: '2024-11-20T09:26:39.945Z'
  }
};
export const mockedReferral = {
  data: {
    data: {
      link: 'https://merchant.koraapi.com/auth/signup?referral_code=TFkr8J6rD6',
      count: 0
    }
  }
};

export const mockedConversions = {
  data: {
    data: [
      {
        source_currency: 'GHS',
        destination_currency: 'KES',
        exchange_rate: '10.12',
        source_amount: '250.00',
        converted_amount: '24.70',
        status: 'success',
        reference: 'KPY-SWP-wjiyJxqEU1tn',
        channel: 'web',
        customer_name: 'willing dart',
        customer_email: '<EMAIL>',
        narration: null,
        transaction_date: '2025-06-16 23:40:30',
        account: {
          name: 'willing dart',
          email: '<EMAIL>'
        }
      }
    ],
    paging: {
      total_items: 1,
      page_size: 10,
      current: 1,
      count: 1,
      next: null
    }
  }
};
export const mockPoolAccountsDetails = {
  data: [
    {
      id: 1,
      pool_account_id: 2,
      currency: 'NGN',
      net_amount: '10000',
      fee: '100',
      amount_paid: 9900,
      transaction_reference: 'TXN-123456',
      transaction_date: '2025-05-26T09:55:07.000Z',
      status: 'SUCCESS',
      source_details: {
        bank: 'Kuda Bank',
        accountName: 'Jane Doe'
      },
      created_at: '2025-05-26T09:55:07.000Z',
      updated_at: '2025-05-26T09:55:07.000Z',
      pool_account_customer_name: 'Jane Doe',
      pool_account_reference: 'KPY-OY-JANE'
    }
  ]
};

export const mockPoolAccounts = {
  data: {
    data: [
      {
        id: 1,
        account_id: 2,
        reference: 'KPY-OY-JANE',
        customer_name: 'Jane Doe',
        customer_email: '<EMAIL>',
        created_at: '2025-05-26T09:55:07.000Z',
        updated_at: '2025-05-26T09:55:07.000Z'
      }
    ]
  }
};

export const mockedApprovalWorkflowUsers = {
  status: true,
  message: 'Approval workflow users retrieved successfully',
  data: [
    {
      id: 180,
      kora_id: 506,
      email: '<EMAIL>',
      firstname: 'MAYOWA',
      lastname: 'KAYODE',
      phoneNumber: '**********',
      role: {
        name: 'Owner',
        slug: 'owner'
      }
    },
    {
      id: 898,
      kora_id: 1244,
      email: '<EMAIL>',
      firstname: 'Willing',
      lastname: 'Son',
      phoneNumber: null,
      role: {
        name: 'Administrator',
        slug: 'admin'
      }
    }
  ]
};

export const mockedApprovalWorflows = {
  status: true,
  message: 'Approval workflows retrieved successfully',
  data: []
};

export const mockedRemoveApprovalWorkflow = {
  status: true,
  message: 'Approval workflow users removed successfully',
  data: null
};

export const mockedUpdateWorkflow = {
  status: true,
  message: 'Approval workflow created successfully',
  data: {
    data: {
      reference: 'KPY-APP-WF-aCTiXpxq3lChQfi',
      type: 'single_payout',
      approvers: [
        {
          userId: 300,
          userKoraId: 634
        }
      ],
      initiators: [
        {
          userId: 300,
          userKoraId: 634
        }
      ],
      enabled: true,
      createdAt: '2025-01-29T16:58:19.913Z',
      updatedAt: '2025-01-29T16:58:19.913Z'
    }
  }
};

export const mockInitiateApproveDeclinePayout = {
  status: true,
  message: 'auth required',
  data: {
    status: 'awaiting_approval',
    payment_reference: 'KPY-PO-202503210951KJ4o9J05017',
    authData: {
      type: 'otp',
      identifier: '1e8f61a1-006e-4187-b0e7-4bbb4f44ca8f'
    }
  }
};

export const mockApprovePayout = {
  status: true,
  message: 'Transfer successfully declined',
  data: {
    status: 'failed',
    message: 'Withdrawal declined',
    amount: '300.00',
    fee: '7.50',
    vat: '0.56',
    merchant_bears_cost: true,
    currency: 'NGN',
    reference: 'KPY-DD-a2eZDwr399MXQST',
    unique_reference: 'KPY-PO-202503210951KJ4o9J05017',
    createdAt: '2025-03-21T09:51:05.000Z',
    payment_destination_type: 'bank_account'
  }
};

export const mockDeclinePayout = {
  status: true,
  message: 'Transfer successfully declined',
  data: {
    status: 'failed',
    message: 'Withdrawal declined',
    amount: '300.00',
    fee: '7.50',
    vat: '0.56',
    merchant_bears_cost: true,
    currency: 'NGN',
    reference: 'KPY-DD-a2eZDwr399MXQST',
    unique_reference: 'KPY-PO-202503210951KJ4o9J05017',
    createdAt: '2025-03-21T09:51:05.000Z',
    payment_destination_type: 'bank_account'
  }
};

export const mockInitiateApproveDeclineBulkPayout = {
  status: true,
  message: 'auth required',
  data: {
    payment_reference: 'testing-bsbr64',
    authData: {
      type: 'otp',
      identifier: '6b6021e9-3091-4a03-8b19-72859e2c0f93'
    }
  }
};

export const mockDeclineBulkPayout = {
  status: true,
  message: 'Bulk transaction rejected successfully'
};

export const mockApproveBulkPayout = {
  status: true,
  message: 'Bulk transaction approved successfully'
};

export const mockSingleApproversTableData = {
  data: [
    {
      id: 728,
      kora_id: 1071,
      email: '<EMAIL>',
      firstname: 'Ugo',
      lastname: 'Dev',
      phoneNumber: null,
      dateAssigned: '06 Jun 2025',
      workflow_reference: 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ',
      workflow_type: 'single_payout'
    }
  ],
  paging: {
    total_items: 1,
    page_size: 5,
    current: 1,
    count: 1
  },
  links: [
    {
      href: 'https://api.koraapi.com/merchant/api/i/approval-workflow/KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ/users?user_type=approvers&limit=5&page=1',
      rel: 'current',
      method: 'GET'
    }
  ]
};

export const mockBulkApproversTableData = {
  data: [
    {
      id: 729,
      kora_id: 1072,
      email: '<EMAIL>',
      firstname: 'Lynda',
      lastname: 'Nwakor',
      phoneNumber: null,
      dateAssigned: '06 Jun 2025',
      workflow_reference: 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ',
      workflow_type: 'bulk_payout'
    }
  ],
  paging: {
    total_items: 1,
    page_size: 5,
    current: 1,
    count: 1
  },
  links: [
    {
      href: 'https://api.koraapi.com/merchant/api/i/approval-workflow/KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ/users?user_type=approvers&limit=5&page=1',
      rel: 'current',
      method: 'GET'
    }
  ]
};

export const mockSingleInitiatorsTableData = {
  data: [
    {
      id: 800,
      kora_id: 2001,
      email: '<EMAIL>',
      firstname: 'Ugo',
      lastname: 'Dev',
      phoneNumber: null,
      dateAssigned: '11 Jun 2025',
      workflow_reference: 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ',
      workflow_type: 'single_payout'
    }
  ],
  paging: {
    total_items: 1,
    page_size: 5,
    current: 1,
    count: 1
  },
  links: [
    {
      href: 'https://api.koraapi.com/merchant/api/i/initiator-workflow/KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ/users?user_type=initiators&limit=5&page=1',
      rel: 'current',
      method: 'GET'
    }
  ]
};

export const mockBulkInitiatorsTableData = {
  data: [
    {
      id: 801,
      kora_id: 2002,
      email: '<EMAIL>',
      firstname: 'Lynda',
      lastname: 'Nwakor',
      phoneNumber: null,
      dateAssigned: '11 Jun 2025',
      workflow_reference: 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ',
      workflow_type: 'bulk_payout'
    }
  ],
  paging: {
    total_items: 1,
    page_size: 5,
    current: 1,
    count: 1
  },
  links: [
    {
      href: 'https://api.koraapi.com/merchant/api/i/initiator-workflow/KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ/users?user_type=initiators&limit=5&page=1',
      rel: 'current',
      method: 'GET'
    }
  ]
};

export const mockApprovalRowTrxn = {
  id: 728,
  kora_id: 1071,
  email: '<EMAIL>',
  firstname: 'Ugo',
  lastname: 'Dev',
  phoneNumber: null,
  dateAssigned: '2025-06-11T19:37:39.972Z',
  workflow_reference: 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ',
  workflow_type: 'single_payout'
};

export const mockInitiateManageApproval = {
  data: {
    reference: 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ',
    auth: {
      identifier: '8a697e78-3948-4361-b6ae-e06b78749534',
      two_factor_type: 'otp'
    }
  },
  message: 'Two factor authorization initiated successfully'
};

export const mockAuthorizeManageApproval = {
  status: true,
  message: 'Approval workflow updated successfully',
  data: null
};

export const mockBusinessRequirementsData = {
  status: true,
  message: 'Business type requirements retrieved successfully',
  data: [
    {
      country: 'ng',
      business_types: {
        individual: {
          description:
            'Individuals can accept and manage online payments on Korapay. This type of business must provide valid identification and proof of address to verify their identity and location.',
          documents: ['Valid ID', 'Proof of Address']
        },
        ngo: {
          description:
            'Non-governmental organizations can accept and manage online payments on Korapay. This type of business must be duly registered with regulators of their country, and must be licensed to operate in their respective industries. As an NGO you will be required to provide;',
          documents: [
            'Certificate of Incorporation',
            'Proof of Address',
            'Approved Constitution',
            'SCUML Certificate (optional)',
            'Due Diligence Questionnaire'
          ]
        },
        registered_business_sme: {
          description:
            'Registered businesses can accept and manage online payments on Korapay. This type of business must be duly registered with regulators of their country, and must be licensed to operate in their respective industries. As a registered business you will be required to provide;',
          documents: ['Certificate of Incorporation', 'Proof of Address', 'MEMART', 'Operational License (optional)']
        },
        registered_business_non_sme: {
          description:
            'Registered businesses can accept and manage online payments on Korapay. This type of business must be duly registered with regulators of their country, and must be licensed to operate in their respective industries. As a registered business you will be required to provide;',
          documents: [
            'Certificate of Incorporation',
            'Proof of Address',
            'MEMART',
            'Operational License (optional)',
            'Due Diligence Questionnaire'
          ]
        }
      }
    }
  ]
};
