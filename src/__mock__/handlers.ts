/* eslint-disable import/prefer-default-export */
import { delay, http, HttpResponse, HttpResponseResolver } from 'msw';

import {
  mockApproveBulkPayout,
  mockApprovePayout,
  mockAuthorizeManageApproval,
  mockBulkApproversTableData,
  mockBulkInitiatorsTableData,
  mockBusinessRequirementsData,
  mockDeclineBulkPayout,
  mockDeclinePayout,
  mockedAllSplitAccountsSummary,
  mockedAllSplitPayments,
  mockedAnalyticsPayIn,
  mockedAnalyticsTransaction,
  mockedApprovalWorkflowUsers,
  mockedBankResolve,
  mockedBanks,
  mockedBvnValidation,
  mockedCardAccessStatus,
  mockedConversions,
  mockedCountries,
  mockedCreateSplitAccountSendOtpResponse,
  mockedCreateSplitAccountType,
  mockedCurrencies,
  mockedEmailConfigurations,
  mockedGHMobileMoneyNetwork,
  mockedIDValidation,
  mockedIndustries,
  mockedIssuedCardDetails,
  mockedKESPayout,
  mockedLienDetailsData,
  mocked<PERSON><PERSON>HistoryD<PERSON>,
  mocked<PERSON>ienSummaryData,
  mocked<PERSON>er<PERSON>tBalan<PERSON>,
  mockedMerchantBankAccount,
  mockedMerchantComplianceInfo,
  mockedMerchantDetails,
  mockedNGNBulkPayoutDetials,
  mockedNGNBulkPayouts,
  mockedNGNPayinDetails,
  mockedNGNPayout,
  mockedNGNPayoutDetails,
  mockedPayinDetailsRelatedTransactions,
  mockedPayinRefundQueryData,
  mockedPayIns,
  mockedPayoutSummary,
  mockedReferral,
  mockedRemoveApprovalWorkflow,
  mockedReservedCardholder,
  mockedRollingReserveHistory,
  mockedRVCBusinessMerchantDetails,
  mockedSettlementAccounts,
  mockedSettlementConversionData,
  mockedSplitAccountDetails,
  mockedSplitAccounts,
  mockedSplitPaymentsSettlementDetails,
  mockedSplitRules,
  mockedSplitSettings,
  mockedSuccessResponse,
  mockedTeamMembers,
  mockedUpdateWorkflow,
  mockedUSDPayout,
  mockedVerificationData,
  mockedVerificationStatusData,
  mockedVirtualAccountNumbers,
  mockedWebhooksData,
  mockInitiateApproveDeclineBulkPayout,
  mockInitiateApproveDeclinePayout,
  mockInitiateManageApproval,
  mockPoolAccounts,
  mockPoolAccountsDetails,
  mockSingleApproversTableData,
  mockSingleInitiatorsTableData
} from './mockData';

export function withDelay(resolver: HttpResponseResolver): HttpResponseResolver {
  return async (...args) => {
    await delay(100);
    return resolver(...args);
  };
}

export const handlers = [
  http.get(
    'http://localhost:3000/api/i/transactions/payins',
    withDelay(() => HttpResponse.json(mockedPayIns, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/misc/banks',
    withDelay(() => HttpResponse.json(mockedBanks, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/misc/banks/resolve',
    withDelay(() => HttpResponse.json(mockedBankResolve, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/profile/balances',
    withDelay(() => HttpResponse.json(mockedMerchantBalance, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/virtual-bank-account',
    withDelay(() => HttpResponse.json(mockedVirtualAccountNumbers, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/virtual-bank-account/undefined',
    withDelay(() => HttpResponse.json(mockedPayIns, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/virtual-bank-account/undefined/transactions',
    withDelay(() => HttpResponse.json(mockedPayIns, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/payouts/undefined',
    withDelay(() => HttpResponse.json(mockedPayIns, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/analytics/transactions/graph/ngn/payin',
    withDelay(() => HttpResponse.json(mockedAnalyticsPayIn, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/analytics/transactions/ngn/count',
    withDelay(() => HttpResponse.json(mockedAnalyticsTransaction, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/users/roles',
    withDelay(() => HttpResponse.json(mockedAnalyticsTransaction, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/users/teams',
    withDelay(() => HttpResponse.json(mockedTeamMembers, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/v1/merchants/profile/balances',
    withDelay(() => HttpResponse.json(mockedMerchantBalance, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/profile/bank-account',
    withDelay(() => HttpResponse.json(mockedMerchantBankAccount, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/balances/USD/transactions',
    withDelay(() => HttpResponse.json(mockedUSDPayout, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/balances/NGN/transactions',
    withDelay(() => HttpResponse.json(mockedNGNPayout, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/verifications/status',
    withDelay(() => HttpResponse.json(mockedVerificationStatusData, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/info',
    withDelay(() => HttpResponse.json(mockedMerchantComplianceInfo, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/get',
    withDelay(() => HttpResponse.json(mockedMerchantDetails, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/balances/KES/transactions',
    withDelay(() => HttpResponse.json(mockedKESPayout, { status: 200 }))
  ),
  http.post('http://localhost:3000/api/fees/calculate', async ({ request }) => {
    const req = (await request.json()) as { currency: string };
    if (req.currency === 'KES') {
      return HttpResponse.json(
        {
          data: {
            bank_account: {
              fee: 50,
              vat: 2.88
            }
          }
        },
        { status: 200 }
      );
    }
    return HttpResponse.json(
      {
        data: {
          bank_account: {
            fee: 25,
            vat: 1.88
          }
        }
      },
      { status: 200 }
    );
  }),
  http.get(
    'http://localhost:3000/api/kyc/meta-data',
    withDelay(() => HttpResponse.json(mockedIndustries, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/notification-configurations',
    withDelay(() => HttpResponse.json(mockedEmailConfigurations, { status: 200 }))
  ),
  http.put(
    'http://localhost:3000/api/notification-configurations/marketing',
    withDelay(() => HttpResponse.json(mockedSuccessResponse, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/users/password/reset/verify',
    withDelay(() => HttpResponse.json(mockedKESPayout, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/misc/countries',
    withDelay(() => HttpResponse.json(mockedCountries, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/misc/currencies',
    withDelay(() => HttpResponse.json(mockedCurrencies, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/settlement-accounts',
    withDelay(() => HttpResponse.json(mockedSettlementAccounts, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/rolling-reserves/USD/transactions',
    withDelay(() => HttpResponse.json(mockedRollingReserveHistory, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/rolling-reserves/KES/transactions',
    withDelay(() => HttpResponse.json(mockedRollingReserveHistory, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/rolling-reserves/NGN/transactions',
    withDelay(() => HttpResponse.json(mockedRollingReserveHistory, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/kyc/meta-data',
    withDelay(() => HttpResponse.json(mockedIndustries, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/bulk-transfers',
    withDelay(() => HttpResponse.json(mockedNGNBulkPayouts, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/bulk/${bulk_reference}/payouts',
    withDelay(() => HttpResponse.json({ ...mockedNGNBulkPayoutDetials, ...mockedPayoutSummary }, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/notification-configurations',
    withDelay(() => HttpResponse.json(mockedEmailConfigurations, { status: 200 }))
  ),
  http.put(
    'http://localhost:3000/api/notification-configurations/marketing',
    withDelay(() => HttpResponse.json(mockedSuccessResponse, { status: 200 }))
  ),

  http.get(
    'http://localhost:3000/api/refunds/KPY-PAY-SRbdnKaTHe7ffg/validate',
    withDelay(() =>
      HttpResponse.json(
        {
          data: {
            can_refund: true,
            transaction_amount: 235654,
            available_balance: 489751730.9,
            available_refundable_amount: 235654,
            minimum_refundable_amount: 100,
            maximum_refundable_amount: 235654,
            payment_source_reference: 'KPY-CM-sTzPFp1rncEg',
            refund_reason: 'Original amount for this transaction is NGN 235654'
          }
        },
        { status: 200 }
      )
    )
  ),
  http.get('http://localhost:3000/api/cards/aDemoRef-12345', ({ request }) => {
    let response;
    const url = new URL(request.url);
    const cardCategory = url.searchParams.get('cardCategory');
    if (cardCategory === 'reserved') response = mockedIssuedCardDetails;
    else
      response = {
        status: true,
        message: 'Card details retrieved successfully',
        data: {}
      };
    return HttpResponse.json(response, { status: 200 });
  }),
  http.get(
    'http://localhost:3000/api/wallets/card-issuance/balances',
    withDelay(() =>
      HttpResponse.json(
        {
          status: true,
          message: 'Card issuance wallet balances retrieved successfully',
          data: {
            USD: {
              available_balance: 3000
            }
          }
        },
        { status: 200 }
      )
    )
  ),
  http.patch(
    'http://localhost:3000/api/wallets/card-issuance/notifications/low-balance-limit',
    withDelay(() => HttpResponse.json({ status: true }, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/access/card-issuance/cards/virtual',
    withDelay(() => HttpResponse.json({}, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/merchants/business-details',
    withDelay(() => HttpResponse.json(mockedRVCBusinessMerchantDetails, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/kyc/id/verify',
    withDelay(() => HttpResponse.json(mockedIDValidation, { status: 200 }))
  ),
  http.put(
    'http://localhost:3000/api/kyc/bvn',
    withDelay(() => HttpResponse.json({ status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/identities/verifications',
    withDelay(() => HttpResponse.json(mockedVerificationData, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/i/access/card-issuance/virtual/access',
    withDelay(() => HttpResponse.json(mockedCardAccessStatus, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/cardholders/reserved',
    withDelay(() => HttpResponse.json(mockedReservedCardholder, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/identity/bvn/validate',
    withDelay(() => HttpResponse.json(mockedBvnValidation, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/webhooks/notifications/456',
    withDelay(() => HttpResponse.json(mockedWebhooksData, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/webhooks/notifications/payout-ref',
    withDelay(() => HttpResponse.json(mockedWebhooksData, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/webhooks/notifications/payment-ref',
    withDelay(() => HttpResponse.json(mockedWebhooksData, { status: 200 }))
  ),

  http.get(
    'http://localhost:3000/api/webhooks/notifications/undefined',
    withDelay(() => HttpResponse.json(mockedWebhooksData, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/kyc/id/verify',
    withDelay(() => HttpResponse.json(mockedIDValidation, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/files',
    withDelay(() => HttpResponse.json({}, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/misc/mobile-money',
    withDelay(() => HttpResponse.json(mockedGHMobileMoneyNetwork, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/settings/settlement/conversions',
    withDelay(() => HttpResponse.json(mockedSettlementConversionData, { status: 200 }))
  ),
  http.patch(
    'http://localhost:3000/api/settings/settlement/conversions',
    withDelay(() => HttpResponse.json({}, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/referral/generate-link',
    withDelay(() =>
      HttpResponse.json(
        {
          data: {
            data: {
              link: 'https://merchant.koraapi.com/auth/signup?referral_code=TFkr8J6rD6'
            }
          }
        },
        { status: 200 }
      )
    )
  ),
  http.get(
    'http://localhost:3000/api/referral/link',
    withDelay(() => HttpResponse.json(mockedReferral, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/split-payments/settings',
    withDelay(() => HttpResponse.json(mockedSplitSettings, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/split-payments',
    withDelay(() => HttpResponse.json(mockedSplitRules, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/split-payments/accounts',
    withDelay(() => HttpResponse.json(mockedCreateSplitAccountType, { status: 200 }))
  ),

  http.get(
    'http://localhost:3000/api/split-payments/accounts',
    withDelay(() => HttpResponse.json(mockedSplitAccounts, { status: 200 }))
  ),

  http.get(
    'http://localhost:3000/api/split-payments/accounts/summary',
    withDelay(() => HttpResponse.json(mockedAllSplitAccountsSummary, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/split-payments/accounts/test',
    withDelay(() => HttpResponse.json(mockedSplitAccountDetails, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/split-payments/settlements',
    withDelay(() => HttpResponse.json(mockedAllSplitPayments, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/split-payments/settlements/test',
    withDelay(() => HttpResponse.json(mockedSplitPaymentsSettlementDetails, { status: 200 }))
  ),

  http.post(
    'http://localhost:3000/api/split-payments/accounts/2fa/send-otp',
    withDelay(() => HttpResponse.json(mockedCreateSplitAccountSendOtpResponse, { status: 200 }))
  ),

  http.post(
    'http://localhost:3000/api/split-payments/accounts/2fa/verify-otp',
    withDelay(() => HttpResponse.json({}, { status: 200 }))
  ),

  http.get(
    'http://localhost:3000/api/liens/KPY-LIEN-chGzjolFFqsU0Bm',
    withDelay(() => HttpResponse.json(mockedLienDetailsData, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/liens',
    withDelay(() => HttpResponse.json(mockedLienHistoryData, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/transactions/payouts/count',
    withDelay(() =>
      HttpResponse.json(
        {
          data: {
            count: 1004
          }
        },
        { status: 200 }
      )
    )
  ),
  http.get(
    'http://localhost:3000/api/liens/summary',
    withDelay(() => HttpResponse.json(mockedLienSummaryData, { status: 200 }))
  ),
  http.get(
    '/api/transactions/payouts/test',
    withDelay(() => HttpResponse.json(mockedNGNPayoutDetails, { status: 200 }))
  ),
  http.get(
    '/api/transactions/payins/test',
    withDelay(() => HttpResponse.json(mockedNGNPayinDetails, { status: 200 }))
  ),
  http.get(
    '/api/refunds/payment-ref/validate',
    withDelay(() => HttpResponse.json(mockedPayinRefundQueryData, { status: 200 }))
  ),
  http.get(
    '/api/transactions/payments/payment-ref',
    withDelay(() => HttpResponse.json(mockedPayinDetailsRelatedTransactions, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/i/conversions',
    withDelay(() => HttpResponse.json(mockedConversions, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/pool-accounts/references',
    withDelay(() => HttpResponse.json(mockPoolAccounts, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/pool-accounts/transactions',
    withDelay(() => HttpResponse.json(mockPoolAccountsDetails, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/settings/approval-workflow',
    withDelay(() => HttpResponse.json(mockedApprovalWorkflowUsers, { status: 200 }))
  ),
  http.patch(
    'http://localhost:3000/api/settings/approval-workflow/KPY-APP-WF-WfxXSo6qiOE1Ruu/users/remove',
    withDelay(() => HttpResponse.json(mockedRemoveApprovalWorkflow, { status: 200 }))
  ),
  http.get('http://localhost:3000/api/settings/approval-workflow/KPY-APP-WF-rvSCqjHEnRKUcXK/users', ({ request }) => {
    const url = new URL(request.url);
    const userType = url.searchParams.get('user_type');
    const limit = url.searchParams.get('limit');
    const page = url.searchParams.get('page');
    const workflowReference = url.pathname.split('/').slice(-2, -1)[0];

    let response;
    if (userType === 'initiators') {
      response = {
        status: true,
        message: 'Approval workflow users retrieved successfully',
        data: mockedApprovalWorkflowUsers,
        meta: {
          limit: Number(limit),
          page: Number(page)
        }
      };
    } else if (workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ') {
      response = mockSingleApproversTableData;
    } else if (workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ') {
      response = mockBulkApproversTableData;
    } else {
      response = {
        status: false,
        message: 'Invalid user type',
        data: []
      };
    }
    return HttpResponse.json(response, { status: 200 });
  }),
  http.patch(
    'http://localhost:3000/api/settings/approval-workflow/authorize',
    withDelay(() => HttpResponse.json(mockedUpdateWorkflow, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/i/transfers/approval/initiate',
    withDelay(() => HttpResponse.json(mockInitiateApproveDeclinePayout, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/i/transfers/approval/decline',
    withDelay(() => HttpResponse.json(mockDeclinePayout, { status: 200 }))
  ),
  http.post(
    'http://localhost:3000/api/i/transfers/approval/approve',
    withDelay(() => HttpResponse.json(mockApprovePayout, { status: 200 }))
  ),
  http.patch(
    'http://localhost:3000/api/i/transactions/bulk/:ref/approval/initiate',
    withDelay(() => HttpResponse.json(mockInitiateApproveDeclineBulkPayout, { status: 200 }))
  ),
  http.patch(
    'http://localhost:3000/api/i/transactions/bulk/:ref/approval/approve',
    withDelay(() => HttpResponse.json(mockApproveBulkPayout, { status: 200 }))
  ),
  http.patch(
    'http://localhost:3000/api/i/transactions/bulk/:ref/approval/decline',
    withDelay(() => HttpResponse.json(mockDeclineBulkPayout, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/i/approval-workflow/${ref}/users?user_type=approvers&limit=5&page=1  ',
    withDelay(() => HttpResponse.json(mockSingleApproversTableData, { status: 200 }))
  ),
  http.get('/api/approval-workflow/:workflowReference/users', ({ request, params }) => {
    const url = new URL(request.url, 'http://localhost');
    const userType = url.searchParams.get('user_type');
    const { workflowReference } = params;
    const singleReference = workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ';
    const bulkReference = workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ';

    if (userType === 'approvers') {
      if (singleReference) {
        const { data, paging } = mockSingleApproversTableData;
        return HttpResponse.json({ data, paging }, { status: 200 });
      } else if (bulkReference) {
        const { data, paging } = mockBulkApproversTableData;
        return HttpResponse.json({ data, paging }, { status: 200 });
      } else {
        return HttpResponse.json({ data: [], paging: { total_items: 0 } }, { status: 200 });
      }
    }
    return HttpResponse.json({ data: [], paging: { total_items: 0 } }, { status: 200 });
  }),
  http.patch('http://localhost:3000/api/i/approval-workflow/${ref}/initiate', () => {
    withDelay(() => HttpResponse.json(mockInitiateManageApproval, { status: 200 }));
  }),
  http.patch('http://localhost:3000/api/i/approval-workflow/${ref}/authorize', () => {
    withDelay(() => HttpResponse.json(mockAuthorizeManageApproval, { status: 200 }));
  }),
  http.get('/api/initiator-workflow/:workflowReference/users', ({ request, params }) => {
    const url = new URL(request.url, 'http://localhost');
    const userType = url.searchParams.get('user_type');
    const { workflowReference } = params;
    const singleReference = workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ';
    const bulkReference = workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ';

    if (userType === 'initiators') {
      if (singleReference) {
        const { data, paging } = mockSingleInitiatorsTableData;
        return HttpResponse.json({ data, paging }, { status: 200 });
      } else if (bulkReference) {
        const { data, paging } = mockBulkInitiatorsTableData;
        return HttpResponse.json({ data, paging }, { status: 200 });
      } else {
        return HttpResponse.json({ data: [], paging: { total_items: 0 } }, { status: 200 });
      }
    }
    return HttpResponse.json({ data: [], paging: { total_items: 0 } }, { status: 200 });
  }),

  http.patch(
    '/api/transactions/bulk/:ref/approval/initiate',
    withDelay(() => HttpResponse.json(mockInitiateApproveDeclineBulkPayout, { status: 200 }))
  ),
  http.get(
    'http://localhost:3000/api/kyc/metadata/business-type-requirements',
    withDelay(() => HttpResponse.json(mockBusinessRequirementsData, { status: 200 }))
  )
];
