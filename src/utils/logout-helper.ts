import { QueryClient } from '@tanstack/react-query';

import { Storage } from '+services/storage-services';
import useStore from '+store';
import { history } from '+utils';

const store = useStore;

export const logout = (destination: null | string = null, queryClient?: QueryClient) => {
  const currentProfile = store.getState().profile;

  store.setState({
    authDetails: {},
    tempToken: '',
    profile: {},
    defaultMerchant: {},
    sessionActive: false,
    merchantEnv: '',
    merchantKYC: { bvn_required: false },
    walletBalance: { USD: { available_balance: 0, low_balance_threshold: null } }
  });
  store.destroy(); // destroy the listener
  store.persist.clearStorage(); // clear the store from storage
  Storage.removeItem('merchantID');

  if (currentProfile?.email) {
    sessionStorage.removeItem(`securityTipShown_${currentProfile.email}`);
    sessionStorage.removeItem(`twoFABannerShown_${currentProfile.email}`);
    sessionStorage.removeItem(`bulkPayoutTipShown_${currentProfile.email}`);
  }

  if (queryClient) {
    queryClient.clear();
    queryClient.setDefaultOptions({
      queries: {
        enabled: false,
        refetchOnWindowFocus: false
      }
    });
  }
  if (destination) history.push(destination);
};
