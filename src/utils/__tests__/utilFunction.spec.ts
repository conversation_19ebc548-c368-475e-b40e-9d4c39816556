import { customTabOrder, maskEmail } from '../utilFunctions';

describe('customTabOrder', () => {
  test('returns correct order for array input', () => {
    const input = ['USD', 'EUR', 'GBP', 'NGN'];
    const order = ['NGN', 'USD', 'EUR', 'GBP'];
    const result = customTabOrder(input, order);
    expect(result).toEqual(['NGN', 'USD', 'EUR', 'GBP']);
  });

  test('places defaultFirst at the beginning of the array)', () => {
    const input = ['USD', 'EUR', 'GBP', 'NGN'];
    const order = ['USD', 'NGN', 'EUR', 'GBP'];
    const result = customTabOrder(input, order, 'GBP');
    expect(result).toEqual(['GBP', 'USD', 'NGN', 'EUR']);
  });

  test('ignores defaultFirst if not present in the array', () => {
    const input = ['USD', 'EUR'];
    const order = ['USD', 'EUR', 'NGN'];
    const result = customTabOrder(input, order, 'GBP');
    expect(result).toEqual(['USD', 'EUR']);
  });

  test('returns correct order for object input', () => {
    const input = {
      USD: { rate: 1 },
      EUR: { rate: 2 },
      NGN: { rate: 1000 }
    };
    const order = ['NGN', 'USD', 'EUR', 'GBP'];
    const result = customTabOrder(input, order);
    expect(Object.keys(result)).toEqual(['NGN', 'USD', 'EUR']);
  });

  test('places defaultFirst at the beginning of the object', () => {
    const input = {
      USD: { rate: 1 },
      GBP: { rate: 3 },
      EUR: { rate: 2 }
    };
    const order = ['USD', 'GBP', 'EUR'];
    const result = customTabOrder(input, order, 'GBP');
    expect(Object.keys(result)).toEqual(['GBP', 'USD', 'EUR']);
  });

  test('ignores items in order not in input', () => {
    const input = ['USD', 'EUR'];
    const order = ['NGN', 'USD', 'BTC', 'EUR'];
    const result = customTabOrder(input, order);
    expect(result).toEqual(['USD', 'EUR']);
  });
});



describe('maskEmail', () => {

  it('should mask emails with username longer than 4 characters', () => {
    expect(maskEmail('<EMAIL>')).toBe('john***@example.com');
    expect(maskEmail('<EMAIL>')).toBe('alic***@company.co');
    expect(maskEmail('<EMAIL>')).toBe('very***@domain.com');
  });

  it('should mask emails with username of 2-4 characters', () => {
    expect(maskEmail('<EMAIL>')).toBe('j***@example.com');
    expect(maskEmail('<EMAIL>')).toBe('b**@mail.org');
    expect(maskEmail('<EMAIL>')).toBe('a***@test.com');
  });

  it('should handle single character usernames', () => {
    expect(maskEmail('<EMAIL>')).toBe('<EMAIL>');
  });

  it('should handle empty strings', () => {
    expect(maskEmail('')).toBe('');
  });

  it('should handle undefined input', () => {
    expect(maskEmail(undefined)).toBe('');
  });

  it('should return the input as-is for invalid email formats', () => {
    expect(maskEmail('not-an-email')).toBe('not-an-email');
    expect(maskEmail('missing-at-sign.com')).toBe('missing-at-sign.com');
    expect(maskEmail('@missing-username.com')).toBe('@missing-username.com');
  });

  it('should mask exactly 3 characters after the first 4 for very long usernames', () => {
    expect(maskEmail('<EMAIL>')).toBe('very***@example.com');
    expect(maskEmail('<EMAIL>')).toBe('extr***@example.com');
  });

  it('should handle emails with special characters', () => {
    expect(maskEmail('<EMAIL>')).toBe('user***@example.com');
    expect(maskEmail('<EMAIL>')).toBe('user***@example.com');
    expect(maskEmail('<EMAIL>')).toBe('user***@example.com');
  });
});
