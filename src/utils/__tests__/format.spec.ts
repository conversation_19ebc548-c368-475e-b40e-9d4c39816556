import { getFormattedConversionRateString } from '+utils/conversions-helper';
import { extractParams } from '+utils/formats';

describe('test extractParams', () => {
  it('extract Params returns accurate object', () => {
    const params = extractParams('?name=John&age=30');
    expect(params).toEqual({ name: '<PERSON>', age: '30' });
  });

  it('extract email params appropriately', () => {
    const params = extractParams('?email=<EMAIL>&code=74j5ct');
    expect(params).toEqual({ email: '<EMAIL>', code: '74j5ct' });
  });
});

describe('getFormattedConversionRateString', () => {
  it('conversion rate string for any currency pair', () => {
    const result = getFormattedConversionRateString('KES', 'GHS', 1500);
    expect(result).toBe('KES 1500 → GHS 1.00');
  });

  it('conversion rate string when from_currency_rate is zero', () => {
    const result = getFormattedConversionRateString('USD', 'NGN', 0.000667);
    expect(result).toBe('USD 0.000667 → NGN 1.00');
  });

  it('conversion rate string when rate is undefined', () => {
    const result = getFormattedConversionRateString('GHS', 'USD', undefined);
    expect(result).toBe('GHS 0 → USD 1.00');
  });

  it('conversion rate string with decimal values', () => {
    const result = getFormattedConversionRateString('NGN', 'GHS', 123.456);
    expect(result).toBe('NGN 123.456 → GHS 1.00');
  });
});
