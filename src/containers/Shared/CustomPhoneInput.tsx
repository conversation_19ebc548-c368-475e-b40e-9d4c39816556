import React from 'react';
import PhoneInput, { getCountryCallingCode } from 'react-phone-number-input';
import { ErrorMessage } from 'formik';

import 'react-phone-number-input/style.css';

import { CountryCode } from 'libphonenumber-js';

interface FieldProps {
  name: string;
  value: string;
  onChange?: () => void;
}

interface FormProps {
  touched: Record<string, boolean>;
  errors: Record<string, string>;
  setFieldValue: (field: string, value: unknown, shouldValidate?: boolean) => void;
  values: Record<string, string | number | null>;
}

interface ICustomInputProps {
  field: FieldProps;
  form: FormProps;
  label: string;
  defaultCountry?: CountryCode;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  [key: string]: unknown;
}

const CustomPhoneInput: React.FC<ICustomInputProps> = ({ field, form, label, defaultCountry = 'NG', ...props }) => {
  const { setFieldValue } = form;
  const [countryCode, setCountryCode] = React.useState<string | undefined>('NG');

  const handleCountryChange = (country: CountryCode | undefined) => {
    const dialCode = country ? getCountryCallingCode(country) : getCountryCallingCode(defaultCountry);
    setCountryCode(dialCode);
  };

  const handlePhoneChange = (number: string | undefined) => {
    setFieldValue('phone_number', number || '');
    if (!countryCode) {
      setCountryCode(getCountryCallingCode(defaultCountry));
    }
    props.onChange?.({
      target: {
        name: field.name,
        value: number || ''
      }
    } as React.ChangeEvent<HTMLInputElement>);
  };

  const formatPhoneNumber = (number: string) => {
    const cleaned = number.replace(/\D/g, '');
    return `+${cleaned}`;
  };

  return (
    <div className="form-group">
      <label htmlFor={field.name}>{label}</label>
      <PhoneInput
        {...field}
        id={field.name}
        international
        defaultCountry={defaultCountry}
        countryCallingCodeEditable={false}
        country={countryCode}
        onCountryChange={handleCountryChange}
        {...props}
        onChange={handlePhoneChange}
        value={formatPhoneNumber(field.value || '')}
      />
      <ErrorMessage name={field.name}>
        {msg => (
          <div className="input__errors">
            <p>{msg}</p>
          </div>
        )}
      </ErrorMessage>
    </div>
  );
};

export default CustomPhoneInput;
