import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Formik } from 'formik';
import { CountryCode } from 'libphonenumber-js';

import CustomPhoneInput from '../CustomPhoneInput';

describe('CustomPhoneInput Component', () => {
  const mockSetFieldValue = vi.fn();

  const defaultProps = {
    field: { name: 'phone_number', value: '' },
    form: {
      touched: {},
      errors: {},
      setFieldValue: mockSetFieldValue,
      values: { phone_number: '' }
    },
    label: 'Phone Number',
    defaultCountry: 'NG' as CountryCode
  };

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderWithFormik = (props = defaultProps) => {
    return render(
      <Formik initialValues={{ phone_number: '' }} onSubmit={() => {}}>
        <CustomPhoneInput {...props} />
      </Formik>
    );
  };

  it('renders the component with the correct label', () => {
    renderWithFormik();
    expect(screen.getByLabelText('Phone Number')).toBeInTheDocument();
  });

  it('sets the default country code on first load', () => {
    renderWithFormik();
    expect(screen.getByLabelText('Phone Number')).toBeInTheDocument();
    expect(mockSetFieldValue).not.toHaveBeenCalledWith('phone_country_code', '');
  });

  it('updates the country code when the country changes', async () => {
    renderWithFormik();

    const countrySelect = screen.getByLabelText('Phone number country');
    await userEvent.selectOptions(countrySelect, 'US'); // Change to United States

    expect(mockSetFieldValue).not.toHaveBeenCalledWith('phone_country_code', '1');
  });
});
