import { SVGAttributes } from 'react';

export const ThreeDotsIcon = ({
  title,
  id = 'plusIcon',
  fill = '#2376F3',
  ...props
}: SVGAttributes<SVGSVGElement> & { title: string; id?: string }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-testid={id} {...props}>
    <title id={id}>{title}</title>
    <path d="M6 12C6 13.6569 4.65692 15 3 15C1.34308 15 0 13.6569 0 12C0 10.3431 1.34308 9 3 9C4.65692 9 6 10.3431 6 12Z" fill={fill} />
    <path
      d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z"
      fill={fill}
    />
    <path
      d="M24 12C24 13.6569 22.6569 15 21 15C19.3431 15 18 13.6569 18 12C18 10.3431 19.3431 9 21 9C22.6569 9 24 10.3431 24 12Z"
      fill={fill}
    />
  </svg>
);
