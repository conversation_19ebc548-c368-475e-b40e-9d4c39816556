import { SVGAttributes } from 'react';

export const PlusIcon = ({
  title,
  id = 'plusIcon',
  stroke = 'white',
  strokeWidth = 2,
  ...props
}: SVGAttributes<SVGSVGElement> & { title: string; id?: string }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" data-testid={id} {...props}>
    <title id={id}>{title}</title>
    <path d="M10 4.16797V15.8346" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4.16602 10H15.8327" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);
