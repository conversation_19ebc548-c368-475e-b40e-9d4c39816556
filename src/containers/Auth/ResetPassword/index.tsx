import { lazy, Suspense } from 'react';
import Helmet from 'react-helmet';
import { Link } from 'react-router-dom';

import { SuspenseLoader as KpyLoader } from '+shared/LazyComponent';

import ImgKorapayLogoSvg from '+assets/img/logos/logo-kpy-ent.svg';

const ResetPasswordForm = lazy(() => import('./components/index'));

const ResetPasswordComponent = () => {
  return (
    <>
      <Helmet title="Reset Password">
        <link rel="preload" href={ImgKorapayLogoSvg} as="image" type="image/svg+xml" />
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="preconnect" href="//fonts.gstatic.com" crossOrigin="" />
      </Helmet>
      <main className="auth__main">
        <div className="auth__header">
          <div className="logo-container">
            <Link to="/">
              <img alt="Korapay Logo" src={ImgKorapayLogoSvg} width="120" height="40" loading="eager" decoding="async" />
            </Link>
          </div>
        </div>
        <div className="headings">
          <h2>Reset password</h2>
          <p>Enter your new password.</p>
        </div>
        <div className="auth__content">
          <Suspense fallback={<KpyLoader message={'Loading'} showImage={false} />}>
            <ResetPasswordForm />
          </Suspense>
        </div>
      </main>
    </>
  );
};

export default ResetPasswordComponent;
