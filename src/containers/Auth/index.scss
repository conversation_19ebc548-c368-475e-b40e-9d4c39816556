@import 'styles/base/variables';

@mixin auth-input($position: left) {
  box-shadow: none;
  border: 2px solid #eff2f7;
  border-radius: 8px;
  width: 100%;
  margin-bottom: 20px;
  padding: 1rem;
  text-align: $position;
  color: #3e4b5b;
}

@mixin auth-input-focus {
  border: 2px solid $kpyblue;
  box-shadow: none;
}

@mixin auth-full-btn($size: 100%) {
  margin-top: 20px;
  width: $size;
}

@mixin hide-seperator {
  display: none;
}

@mixin label($position: flex) {
  color: #292b2c;
  font-weight: 500;
  font-size: 1rem;
  display: $position;
  justify-content: flex-start;
  margin-bottom: 8px;
}

.css-1okebmr-indicatorSeparator {
  @include hide-seperator();
}

.css-tlfecz-indicatorContainer {
  position: absolute;
  top: 20%;
  right: 0;
}

.reg-phone-input-invalid {
  border-top: 2px solid red !important;
  border-bottom: 2px solid red !important;
  border-left: 2px solid red !important;
  border-right: none;
}

.border-red {
  border-color: red !important;
  border-top-color: red !important;
}

.auth__body {
  margin: 0;
  background-size: cover;
  min-height: 100vh;
  display: flex;
  width: 100%;

  @media (max-width: 1200px) {
    display: flex;
    flex-direction: column;
  }

  label {
    @include label();
  }

  a {
    text-decoration: none !important;
  }

  input {
    @include auth-input();
  }

  input:focus {
    @include auth-input-focus();
  }

  textarea {
    @include auth-input();
  }

  textarea:focus {
    @include auth-input-focus();
  }

  input[type='password'] {
    margin-bottom: 0;
  }
}

.logo__section {
  background: #f3f4f8;
  padding: 2.5rem 4.375rem;
  position: fixed;
  left: 0;
  height: 100vh;
  width: auto;

  @media (max-width: 1200px) {
    position: relative;
    height: unset;
    z-index: 2;
    width: 100vw;
    padding: 5.5rem 3.5rem;
    text-align: center;
  }

  img {
    width: 8.5rem;
    margin-bottom: 5.5rem;
  }

  h2 {
    margin-bottom: 1.25rem;
    color: #000a1a;
    font-size: 2.2rem;
  }

  p {
    color: #3e4b5b;
    opacity: 0.5;
    font-size: 1rem;
    font-weight: 400;
  }

  .progress-section {
    margin-top: 4.375rem;
  }

  @media (min-width: 1200px) {
    h2 {
      font-size: 1.7rem;
    }

    p {
      font-size: 0.8rem;
    }
  }
}

.link-text {
  color: $kpyblue;
  font-weight: 400;
  cursor: pointer;
}

.forgot-text {
  color: $kpyblue;
  cursor: pointer;
  margin-top: 8px;
}

.grey-text {
  font-weight: 400;
  color: #9fa6ae;
}

.--full-blue {
  @include auth-full-btn();
}

.--half-blue {
  height: 40px;
  width: 30%;
  margin: 3px 23px;
  padding: 10px 9px;
  font-size: 13px;
}

.forgot-password__form {
  width: 100%;
}

.form__section {
  background: white;
  box-shadow: 0 0 4px rgba(0, 5, 7, 0);
  display: flex;
  justify-content: flex-end;
  padding: 60px;
  position: absolute;
  right: 0;

  @media (min-width: $breakpoint-desktop-md) {
    width: 70%;
    justify-content: space-evenly;
  }

  @media (max-width: $breakpoint-desktop-sm) {
    display: flex;
    flex-direction: column;
    padding: 35px;
    text-align: center;
    align-items: center;
  }

  @media (max-width: 1200px) {
    margin-top: 14px;
    position: relative;
    margin-bottom: 20px;
  }

  .mid-section {
    @media (min-width: $breakpoint-desktop-md) {
      width: 50%;
    }
    width: 500px;

    @media (max-width: $breakpoint-tablet) {
      margin-top: 0;
      width: auto;
    }

    h3 {
      margin-top: 50px !important;
      font-size: 1.575rem;
      font-weight: 500;
      margin-bottom: 10px;
      color: #000a1a;
    }

    p {
      color: #607d8b;
      margin-bottom: 2.4rem;
    }

    .signup-back {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      left: 1rem;
      color: #2376f3;

      @media (max-width: $breakpoint-tablet) {
        margin-top: 0;
        position: static;
        left: 0;
      }
    }
  }

  react-tel-input {
    country-list {
      search-box {
        border: 1px solid #cacaca;
        /* border-radius: 3px; */
        font-size: 15px;
        /* line-height: 15px; */
        margin: 6px;
        padding: 3px 8px 5px;
        outline: none;
        width: 80%;
      }
    }
  }

  .phone-input {
    .PhoneInputInput {
      border-radius: 0 10px 10px 0;
      margin-bottom: 0 !important;
      padding: 1rem 0.5rem !important;
    }

    .PhoneInputCountry {
      border: 2px solid #eff2f7;
      border-radius: 10px 0 0 10px;
      border-right: none !important;
      top: 0;
      bottom: 0;
      padding: 12px;
      background-color: #f5f5f5;
      width: 54px;
      margin: 0 !important;
    }

    .PhoneInputCountryIcon {
      width: 16px;
      height: 11px;
      box-shadow: none !important;
    }

    &.error {
      input {
        border-color: #f32345;
      }
      .PhoneInputCountry {
        border-color: #f32345;
      }
    }
  }
}

h2 {
  margin-bottom: 20px;
}

p {
  font-size: 1rem;
  color: #9fa6ae;
}

.account-text {
  text-align: center;

  @media screen and (min-width: $breakpoint-tablet) {
    margin-left: 3rem;
  }
}

.input_group {
  display: flex;
  justify-content: space-between;

  .input__wrap {
    &:first-of-type {
      max-width: 48%;
      min-width: 48%;
    }

    &:last-of-type {
      margin-left: 1.25rem;
      max-width: 48%;
      min-width: 48%;
    }
  }

  @media (max-width: 1270px) {
    display: flex;
    flex-direction: column;

    .phone-class {
      margin: 0 0 20px 0;
    }

    .input__wrap {
      &:first-of-type {
        max-width: 100%;
        min-width: 100%;
      }

      &:last-of-type {
        margin-left: 0;
        max-width: 100%;
        min-width: 100%;
      }
    }
  }
}

.error-message {
  color: #c10b28;
  font-size: 1.12rem;
  font-weight: 500;
  background: #ffd2da;
  padding: 10px 20px;
  border-radius: 0.625rem;
  width: 100%;
  margin-bottom: 30px;
}

.css-yk16xz-control {
  box-shadow: none;
  border: 3px solid #eff2f7 !important;
  border-radius: 8px !important;
  width: 100%;
  height: 59px;
  margin-bottom: 20px;
}

.css-b8ldur-Input {
  position: relative;
  top: 10px;
}

.auth-check {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  input {
    width: 6%;
    margin-top: 0.3rem;
  }

  label {
    width: 95%;
    font-weight: 400;
    color: #9fa6ae;
  }

  .terms-condition {
    text-align: center;
    max-width: 430px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: $breakpoint-tablet) {
      margin-bottom: 40px;
    }
  }

  .check-label {
    color: #9fa6ae;
  }

  .btn--link {
    margin: 0;
    text-decoration: none;
    font-size: 0.9rem;
  }
}

.phone-input {
  display: flex;

  .css-yk16xz-control {
    box-shadow: none;
    border: 3px solid #eff2f7;
    border-right: none;
    width: 100%;
    border-radius: 8px 0px 0px 8px;
    height: 59px;
  }

  .css-26l3qy-menu {
    width: 210px;
  }
}

.code-select {
  width: 40%;

  .code-color {
    color: #9fa6ae;
  }

  .code-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

button {
  border: none;
  background: transparent;
}

.auth__center {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10rem auto;

  @media (max-width: $breakpoint-tablet) {
    padding: 4px;
  }

  input {
    @include auth-input();
  }

  input:focus {
    @include auth-input-focus();
  }

  textarea {
    @include auth-input();
  }

  textarea:focus {
    @include auth-input-focus();
  }

  input[type='password'] {
    margin-bottom: 0;
  }

  input[type='tel'] {
    border-radius: 0 8px 8px 0;
    width: 60%;
  }

  h2 {
    font-size: 1.7rem;
    line-height: 1.2;
    margin-bottom: 0.7rem;
    font-weight: 500;
    color: #102649;
    letter-spacing: -0.014em;
    text-align: center;

    @media (min-width: $breakpoint-desktop) {
      font-size: 1.7rem;
      line-height: 1.1;
      margin-bottom: 0.7rem;
      text-align: center;
    }
  }

  span {
    font-size: 1rem;
    font-weight: 400;
    color: #3e4b5b;
    opacity: 0.6;
    margin-bottom: 3.125rem;
    text-align: center;
  }

  .continue-btn {
    padding: 0.9375rem 6.2188rem;
    background: $kpyblue;
    color: white;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: block;
    margin: 1.5625rem auto;
  }

  img {
    width: 3rem;
    margin: 0 auto;
  }

  .sent-title {
    font-size: 2.1875rem;
    font-weight: 600;
    color: black;
    margin-bottom: 1.25rem;
    text-align: center;
  }

  .sent-subtitle {
    font-size: 1.1875rem;
    text-align: center;
    margin-bottom: 3.125rem;
    color: #3e4b5b;
    opacity: 0.6;
  }

  .input__wrap {
    margin-bottom: 1.25rem;
  }

  .receive-text {
    font-size: 1.25rem !important;
    text-align: center;
    margin-bottom: 30px;
    color: #414f5f;

    span {
      color: #000a1a;
    }

    p {
      display: inline;
      font-size: 1.25rem;
      font-style: italic;
      color: grey;
      opacity: 0.5;
    }
  }

  img {
    width: 5rem;
    height: 5rem;
    margin-bottom: 30px;
  }

  .loader-bg {
    background: #ccc;
    width: 216px;
    height: 3px;
    border-radius: 10px;
    position: relative;
    margin: 1.25rem auto;

    .blue-line {
      background: $kpyblue;
      border-radius: 10px;
      position: absolute;
      left: 0;
      z-index: 1;
      width: 60px;
      height: 3px;
      animation: line-bounce 1s infinite;
    }

    @keyframes line-bounce {
      0% {
        left: 150px;
      }

      50% {
        left: 0;
      }

      100% {
        left: 150px;
      }
    }
  }
}

.auth__center__body {
  margin: 0;
  background-size: cover;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 50px;
}

.continue-btn {
  padding: 0.9375rem 3.375rem;
  background: $kpyblue;
  color: white;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: block;
  margin: 0 auto;
}

.auth__main {
  min-height: 100vh;
  background: white;
  display: flex;
  flex-direction: column;

  .logo-container {
    width: 7rem;
    display: flex;
    justify-content: center;

    @media (min-width: $breakpoint-desktop) {
      width: 6.5rem;
    }
  }

  .auth__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: center;
    padding-top: 2rem;

    @media (min-width: $breakpoint-tablet) {
      padding-top: 3rem;
    }

    @media (min-width: $breakpoint-desktop) {
      padding-top: 4.5rem;
    }

    img {
      width: 7rem;
    }
  }

  .invalid__container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 3rem;

    > .verify__loader {
      display: flex;
      align-items: center;

      > p {
        margin-bottom: 0;
      }
    }
  }

  .headings {
    text-align: center;
    padding-top: 2rem;
    margin: 0 auto;

    img {
      width: 6.25rem;
      width: 6.25rem;
      margin-bottom: 1.8rem;
    }

    .back-link {
      > img {
        width: 1rem;
        margin-bottom: 0;
        margin-right: 0.3rem;
      }
    }
    .heading-text {
      margin-bottom: 20px;
    }

    .heading-button {
      margin: 0.5rem 0;
    }
    h2 {
      font-size: 1.7rem;
      line-height: 1.2;
      margin-bottom: 0.7rem;
      font-weight: 500;
      color: #000a1a;
      letter-spacing: -0.014em;

      @media (min-width: $breakpoint-desktop) {
        font-size: 1.7rem;
        line-height: 1.1;
        margin-bottom: 0.7rem;
      }

      @media (max-width: $breakpoint-tablet) {
        font-size: 1.1rem;
      }
    }

    p {
      font-size: 0.9rem;
      color: #607d8b;
      opacity: 0.5;
    }

    .auth-bvn-update {
      text-align: left;

      p {
        color: #292b2c;
      }

      .bvn-form {
        margin-bottom: 20px;
        input {
          padding: 1rem;
          margin-bottom: 0;
        }
      }

      .bvn-form-warning {
        padding: 10px 20px;
        display: flex;
        align-items: baseline;
        background-color: #fff8e1;
        border-radius: 10px;
        margin-bottom: 1.5rem;
        p {
          color: #915200;
          opacity: 1;
          margin-left: 12px;
          margin-bottom: 0;
        }
      }
    }

    .bvn-icon {
      background-color: #cde5fe;
      border-radius: 50px;
      width: 85px;
      height: 85px;
      padding-top: 1.75rem;
      margin: auto;
      margin-bottom: 30px;
    }

    &.max-width {
      max-width: 400px;
    }
  }

  .auth__content {
    padding: 2rem 1rem;
    width: 500px;
    margin: 0 auto;
    max-width: 100%;

    .locked-message {
      color: #c10b28;
      font-size: 0.8rem;
      text-align: center;

      button {
        color: $kpyblue;
        text-decoration: underline;
      }
    }

    @media (max-width: $breakpoint-tablet) {
      width: 390px;
    }

    input {
      @include auth-input();
    }

    input:focus {
      @include auth-input-focus();
    }

    textarea {
      @include auth-input();
    }

    textarea:focus {
      @include auth-input-focus();
    }

    input[type='password'] {
      margin-bottom: 0;
    }

    input[type='tel'] {
      border-radius: 0 8px 8px 0;
      width: 60%;
    }
  }

  .back {
    position: absolute;
    left: 100px;
    top: 60px;

    img {
      width: 24.6px;
      margin-bottom: 4.5px;
    }

    span {
      font-size: 20px;
      font-weight: 500;
    }
  }

  @media (max-width: $breakpoint-desktop) {
    .back {
      position: relative;
      left: 0;
      top: 0;
      margin-bottom: 20px;
    }
  }

  .auth__form {
    margin-top: 10rem;
    min-width: 28%;
  }
}

.email-notification {
  position: absolute;
  bottom: 0;
  left: 34rem;
  width: 50%;
  max-width: 255px;

  p {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0 !important;
  }

  @media (max-width: 854px) {
    max-width: 100%;
    position: relative !important;
    top: 0 !important;
    right: 0 !important;
    text-align: left !important;
  }

  @media (max-width: 1270px) {
    top: 565px;
  }
}

.country-notification {
  position: absolute;
  max-width: 240px;
  right: 6.5rem;
  bottom: 10rem;

  p {
    font-size: 14px;
    span {
      font-weight: 500;
    }
    margin-bottom: 0 !important;
  }
  ul {
    font-size: 0.9rem;
    color: #607d8b;

    li {
      margin-bottom: 0.2rem;
      margin-left: 1rem;
      list-style-type: disc;
    }
  }

  @media (max-width: 854px) {
    max-width: 100%;
    position: relative !important;
    top: 0 !important;
    right: 0 !important;
    text-align: left !important;
    margin-top: 1rem;
  }

  @media (max-width: 1270px) {
    margin-right: -3rem;
  }
}

.password__strength {
  position: absolute;
  bottom: 470px;
  right: 50px;

  p {
    font-size: 1rem;
    font-weight: 500;
    color: #607d8b;
    margin-bottom: 0 !important;
  }

  .active {
    color: #607d8b;
  }

  @media (max-width: 854px) {
    position: relative !important;
    bottom: 0 !important;
    right: 0 !important;
    text-align: left !important;
    margin-top: 10px !important;
  }
}

.mobile_bottom_space {
  margin-bottom: 1rem;
}

.cpassword__strength {
  position: absolute;
  bottom: 445px;
  right: 71px;

  @media (max-width: $breakpoint-desktop-sm) {
    position: relative !important;
    bottom: 0px !important;
    right: 5px !important;
    text-align: left;
    margin-top: 5px;
  }
}

.not-supported__body {
  background: white;
  box-shadow: 0 0 4px rgba(0, 5, 7, 0);
  margin-top: 8.125rem;
  width: 38rem;
  position: relative;
  left: 40%;
  right: 40%;

  @media (max-width: 1200px) {
    margin-top: 4rem;
    padding: 0 27px;
    width: 100vw;
    left: 0;
    right: 0;
  }

  h2 {
    font-size: 1.875rem;
    margin-bottom: 0.5rem;

    @media (max-width: $breakpoint-desktop) {
      font-size: 1.5rem;
    }
  }

  p {
    margin-bottom: 3rem;
  }

  .kyc-feedback {
    span {
      font-size: 0.9rem;
      font-weight: 500;
    }
  }

  .business-type {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
  }

  .type-container {
    @include auth-input(center);

    &:focus {
      @include auth-input-focus();
    }

    img {
      width: 16px;
      height: 14px;
      margin-right: 8px;
      margin-bottom: 5px;
    }
  }

  input {
    @include auth-input();

    &::placeholder {
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      color: #9fa6ae;
    }

    &.invalid {
      border-color: red;
    }
  }

  input:focus {
    @include auth-input-focus();
  }

  textarea {
    @include auth-input();

    &::placeholder {
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      color: #9fa6ae;
    }
  }

  .input-group {
    display: flex;
    margin-top: 10px;

    .input-group-prefix {
      width: 20%;
      border: 3px solid #eff2f7;
      border-radius: 8px 0px 0px 8px;
      padding: 14px 0;
      text-align: center;
      color: #9fa6ae;
      border-right: none;
      height: 58px;
    }

    input {
      width: 80%;
      border-radius: 0px 8px 8px 0px;
    }
  }

  .full-blue {
    @include auth-full-btn();
  }
}

.link-text-resend {
  display: inline-flex;
  justify-content: center;
  align-items: center;

  > span {
    color: #2376f3 !important;
    width: 0.8rem;
    height: 0.8rem;
    border-width: 1.5px;
  }
}

.input_group_div {
  flex-direction: column;

  .input__wrap:last-of-type {
    margin-left: 0;
    max-width: 100%;
    min-width: 100%;
  }

  .input__wrap:first-of-type {
    max-width: 100%;
    min-width: 100%;
  }
}

.signup_business-type {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .signup_business-type-btn {
    box-shadow: none;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 20px;
    padding: 13px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    border: 2px solid #eff2f7;

    &.selectedType {
      border: 2px solid $kpyblue;

      .signup_business-type-icon {
        p {
          color: $kpyblue;
        }
      }
    }
  }
}

.signup_business-type-icon {
  display: flex;
  align-items: center;
  gap: 1rem;

  p {
    margin-bottom: 0 !important;
  }
}

.signup_registered-options {
  width: 100%;
  background: #f1f6fa;
  border-radius: 0 0 10px 10px;
  padding: 0.3125rem;
  margin-bottom: 1.25rem;

  div {
    display: flex;
    justify-content: flex-end;
    flex-direction: row-reverse;
    align-items: center;
    min-height: 40px;
    width: 100%;

    input {
      width: 30px;
      margin-right: 5px;
      margin-left: 10px;
      margin-bottom: 0 !important;
    }

    label {
      width: calc(100% - 30px);
      font-size: 0.9rem;
      color: #3e4b5b;
      font-weight: 500;
      margin-bottom: 0 !important;
    }
  }
}
