/* eslint-disable @typescript-eslint/no-misused-promises */
/* eslint-disable @typescript-eslint/no-floating-promises */
import React, { useEffect, useRef, useState } from 'react';
import { useInterval } from 'react-use';
import { Field, FieldArray, FieldInputProps, Form, Formik, FormikErrors, FormikTouched } from 'formik';

import CustomReactSelect from '+containers/Shared/CustomReactSelect';
import { uploadFiles } from '+hooks/utils';
import { KYCServices } from '+services/kyc-services';
import Feedback from '+shared/Feedback';
import { DocumentFormValuesType, DocumentsPropsType, UploadResponseType } from '+types';
import { logError } from '+utils';

import { additionalFormsCount, getIdTypeErrorMessage, parseBackendRegex } from './constants';
import Headings from './Headings';
import KycFeedback from './KycFeedback';
import Tooltip from './Tooltip';

import CancelSvg from '+assets/img/auth/cancel.svg';
import CorrectSvg from '+assets/img/auth/correct.svg';
import DeleteSvg from '+assets/img/auth/delete.svg';
import DocumentSvg from '+assets/img/auth/document.svg';
import UploadSvg from '+assets/img/auth/upload.svg';
import Info from '+assets/img/dashboard/vnin-info.svg';
import WarningSVG from '+assets/img/dashboard/warning-outline.svg';

import './index.scss';

import { ErrorResponseType, IMerchantKycInfo } from '+types';

const Documents: React.FC<DocumentsPropsType> = ({
  updateStage,
  readKycFeedback,
  submittedDocuments,
  updateSubmittedKyc,
  formRef,
  setMobileActionBtnState,
  validIdOptions
}) => {
  const fileRef1 = useRef<HTMLInputElement | null>(null);
  const fileRef2 = useRef<HTMLInputElement | null>(null);

  const [idFile, setIdFile] = useState<string | undefined | null>(null);
  const [proofOfAddressFile, setProofOfAddressFile] = useState<string | undefined | null>(null);

  const [isFormValid, setIsFormValid] = useState(false);
  const [idInvalid, setIdInvalid] = useState(false);
  const [isAddDoc, setIsAddDoc] = useState(false);
  const [addFormState, setAddFormState] = useState({
    fileAdded: 0,
    fileUploaded: 0
  });

  const myRefs = useRef<Array<HTMLInputElement | null>>([]);
  const formikRef = formRef;

  const [state, setState] = useState({
    isLoading: false,
    uploadingDoc: false,
    isVninFieldError: false,
    feedback: {
      message: '',
      visible: false,
      type: 'danger'
    }
  });

  const [isAutoSave, setIsAutoSave] = useState(false);

  const idTypeError = (values: DocumentFormValuesType): React.ReactNode => {
    let message = '';
    const regexString = values.idType?.regex;
    if (regexString && values.idNumber) {
      const regex = parseBackendRegex(regexString);
      if (!regex.test(values.idNumber)) {
        setIdInvalid(true);
        message = getIdTypeErrorMessage(values.idType?.value || '');
        return message ? <Feedback type="danger" message={message} /> : null;
      }
    }
    setIdInvalid(false);
    return null;
  };

  const handleSubmit = () => {
    setState({ ...state, isLoading: true });
    const { values } = formikRef.current!;
    const data: {
      documents: Array<Record<string, unknown>>;
      additional_documents?: Array<unknown>;
    } = {
      documents: [
        values.idType?.value === 'vnin'
          ? {
              type: 'vnin',
              id: values.idNumber,
              text: {
                name: 'vnin',
                value: values.idNumber
              }
            }
          : {
              type: values.idType?.value,
              id: values.idNumber,
              file: {
                description: values.idType?.label,
                name: values.validIdName
              }
            },
        {
          type: 'proof_of_address',
          file: {
            description: values.proofOfAddressDescription,
            name: values.proofOfAddressName
          }
        }
      ]
    };

    if (addFormState.fileUploaded > 0) {
      data.additional_documents = values.additional_documents;
      businessDocMutation.mutate(data);
    } else {
      businessDocMutation.mutate(data);
    }
  };

  const handleAutosaveSubmit = () => {
    const { values } = formikRef.current!;
    const documents = [];
    const additionalDocuments = [];
    if (values.idType && values.idNumber) {
      if (values.idType.value === 'vnin') {
        documents.push({
          type: 'vnin',
          id: values.idNumber,
          text: {
            name: 'vnin',
            value: values.idNumber
          }
        });
      } else if (values.validIdName) {
        documents.push({
          type: values.idType.value,
          id: values.idNumber,
          file: {
            description: values.idType.label,
            name: values.validIdName
          }
        });
      }
    }
    if (values.proofOfAddressName && values.proofOfAddressDescription) {
      documents.push({
        type: 'proof_of_address',
        file: {
          description: values.proofOfAddressDescription,
          name: values.proofOfAddressName
        }
      });
    }

    if (values.additional_documents.length > 0 && addFormState.fileUploaded > 0) {
      additionalDocuments.push(...values.additional_documents);
    }
    if (documents.length > 0 || additionalDocuments.length > 0) {
      const data = {
        is_auto_save: true,
        ...(documents.length > 0 && { documents }),
        ...(additionalDocuments.length > 0 && { additional_documents: additionalDocuments })
      };
      setIsAutoSave(true);
      businessDocMutation.mutate(data);
    }
  };

  useInterval(() => {
    if (formikRef.current!.dirty) {
      handleAutosaveSubmit();
    }
  }, 30000);

  useEffect(() => {
    if (submittedDocuments?.length > 0) {
      const documents = submittedDocuments?.filter(item => item?.is_additional_document === false);
      documents.forEach(item => {
        if (item.type === 'proof_of_address') {
          void formikRef.current!.setFieldValue('proofOfAddressName', item?.file?.name);
          void formikRef.current!.setFieldValue('proofOfAddressDescription', item?.file?.description);
          setProofOfAddressFile(item?.file?.description);
        } else if (item.type === 'vnin') {
          void formikRef.current!.setFieldValue('idNumber', item?.id);
          void formikRef.current!.setFieldValue('idType', { value: 'vnin', label: 'VNIN' });
        } else {
          const validIdType = validIdOptions.find(id => id.value === item?.type);
          void formikRef.current!.setFieldValue('validIdName', item?.file?.name);
          void formikRef.current!.setFieldValue('idNumber', item?.id);
          void formikRef.current!.setFieldValue('idType', validIdType || null);
          void formikRef.current!.setFieldValue('validIdDescription', item?.file?.description);
          setIdFile(item?.file?.name);
        }
      });

      const additionalDocs = submittedDocuments?.filter(item => {
        return item?.is_additional_document === true;
      });

      if (additionalDocs.length > 0) {
        setIsAddDoc(true);
        setAddFormState({
          ...addFormState,
          fileAdded: additionalDocs.length,
          fileUploaded: additionalDocs.length
        });
        additionalDocs.forEach((item, index) => {
          void formikRef.current!.setFieldValue(`additional_documents[${index}].type`, item.type);
          void formikRef.current!.setFieldValue(`additional_documents[${index}].file.name`, item.file.name);
          void formikRef.current!.setFieldValue(`additional_documents[${index}].file.description`, item.file.description);
        });
      }
    }
  }, [submittedDocuments]);

  const resetAutoSave = () => {
    const { values, resetForm } = formikRef.current!;
    setIsAutoSave(false);
    resetForm({ values });
  };

  const handleApiError = (e: unknown) => {
    if (isAutoSave) return resetAutoSave();
    const error = (e as ErrorResponseType)?.response?.data;
    const errorMessage = () => {
      if (error?.message === 'KYC has already been verified and cannot be updated') {
        return 'KYC has already been verified, please refresh to continue';
      }
      if (error?.message === 'KYC is being reviewed and cannot be updated') {
        return 'KYC is being reviewed, please refresh to continue';
      }
      if (error?.message) {
        return error?.message;
      }
      return 'There seems to be an issue with your file upload';
    };
    setState({
      ...state,
      isLoading: false,
      feedback: { ...state.feedback, message: `${errorMessage()}`, visible: true, type: 'danger' }
    });
  };

  const businessDocMutation = KYCServices.useBusinessDoc({
    onSuccess: ({ data }) => {
      if (isAutoSave) return resetAutoSave();

      updateSubmittedKyc(data as IMerchantKycInfo);
      setState({ ...state, isLoading: false });
      return updateStage(3);
    },
    onError: handleApiError
  });

  const formatFileName = (docName: string, type: string) => {
    if (docName.length > 10) {
      const fileName = docName.substring(0, 10);
      return `${fileName} ...${type}`;
    }
    return `${docName}`;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    docType: string,
    setFieldValue: (field: string, value: unknown) => void
  ) => {
    const file = e.target.files![0];
    const fileName = e.target.files![0].name;
    const fileSize = e.target.files![0].size;

    const data = new FormData();
    data.append(`files`, file);
    const formatFileSize = Math.floor(fileSize / 1024 ** 2);

    if (formatFileSize > Number(process.env.REACT_APP_MAX_FILE_SIZE)) {
      if (docType === 'id') {
        setIdFile(null);
      } else if (docType === 'proof_of_address') {
        setProofOfAddressFile(null);
      } else if (docType === 'additionalDocuments0') {
        setFieldValue('additionalDocuments0', '');
      } else if (docType === 'additionalDocuments1') {
        setFieldValue('additionalDocuments1', '');
      } else if (docType === 'additionalDocuments2') {
        setFieldValue('additionalDocuments2', '');
      }
      return setState(prevState => ({
        ...prevState,
        isLoading: false,
        feedback: {
          ...state.feedback,
          message: `Please upload a file less than ${process.env.REACT_APP_MAX_FILE_SIZE || 20}MB in size`,
          visible: true,
          type: 'danger'
        }
      }));
    }
    return uploadFiles(data)
      .then((res: UploadResponseType) => {
        setState(prevState => ({
          ...prevState,
          feedback: {
            ...state.feedback,
            message: '',
            type: '',
            visible: false
          }
        }));
        if (docType === 'id') {
          setFieldValue('validIdName', res.data[0].path);
          setFieldValue('validIdDescription', res.data[0].original_name);
          setIdFile(fileName);
        } else if (docType === 'proof_of_address') {
          setFieldValue('proofOfAddressName', res.data[0].path);
          setFieldValue('proofOfAddressDescription', res.data[0].original_name);
          setProofOfAddressFile(fileName);
        } else if (docType === 'additional_documents0') {
          setFieldValue(`additional_documents[0].file.description`, res.data[0].original_name);
          setFieldValue(`additional_documents[0].file.name`, res.data[0].path);
        } else if (docType === 'additional_documents1') {
          setFieldValue(`additional_documents[1].file.description`, res.data[0].original_name);
          setFieldValue(`additional_documents[1].file.name`, res.data[0].path);
        } else if (docType === 'additional_documents2') {
          setFieldValue(`additional_documents[2].file.description`, res.data[0].original_name);
          setFieldValue(`additional_documents[2].file.name`, res.data[0].path);
        } else {
          setIdFile(null);
          setProofOfAddressFile(null);
          setFieldValue('additional_documents0', null);
          setFieldValue('additional_documents1', null);
          setFieldValue('additional_documents2', null);
        }
      })
      .catch(err => {
        if (docType === 'id') {
          setIdFile(null);
        } else if (docType === 'proof_of_address') {
          setProofOfAddressFile(null);
        } else if (docType === 'additional_documents[0].file.name') {
          setFieldValue('additional_documents[0].file.name', null);
        } else if (docType === 'additional_documents[1].file.name') {
          setFieldValue('additional_documents[1].file.name', null);
        } else if (docType === 'additional_documents[2].file.name') {
          setFieldValue('additional_documents[2].file.name', null);
        }
        setState({
          ...state,
          feedback: {
            ...state.feedback,
            message: `There's been an issue uploading your file. Please try again`,
            type: 'danger',
            visible: true
          }
        });
        logError(err);
      });
  };

  const showFeedback = () => {
    const { feedback } = state;
    return feedback?.visible ? <Feedback type={feedback.type} message={feedback.message} /> : '';
  };

  const processActionBtnState = () => {
    const disabled = !isFormValid || idInvalid || !(addFormState.fileUploaded === addFormState.fileAdded) || state.isLoading;
    useEffect(() => {
      setMobileActionBtnState();
    }, [disabled]);
    return disabled;
  };

  useEffect(() => {
    setMobileActionBtnState();
  }, [state.isLoading]);

  const configErrorMessage = (errors: FormikErrors<DocumentFormValuesType>, touched: FormikTouched<DocumentFormValuesType>) => {
    let msg = null;

    if (errors.validIdName && touched.validIdName) {
      msg = errors.validIdName;
    }
    if (errors.idNumber && touched.idNumber) {
      msg = errors.idNumber;
    }
    if (errors.proofOfAddressName && touched.proofOfAddressName) {
      msg = errors.proofOfAddressName;
    }
    if (errors.additional_documents && touched.additional_documents && typeof errors.additional_documents === 'string') {
      msg = errors.additional_documents;
    }

    return msg ? <Feedback type="danger" message={msg} /> : null;
  };

  const findValidIdOption = (value: string | undefined) => {
    const type = validIdOptions?.find(option => option.value === value) as { label?: string } | undefined;
    return type?.label;
  };
  return (
    <div>
      <div className="kyc__form">
        <Headings kycStage="documents" />
        {readKycFeedback && <KycFeedback feedback={readKycFeedback} />}
        <div style={{ maxWidth: '492px' }}>{showFeedback()}</div>
        <Formik
          innerRef={formikRef}
          initialValues={
            {
              idType: undefined,
              idNumber: '',
              vnin: '',
              validIdName: '',
              validIdDescription: '',
              proofOfAddressName: '',
              proofOfAddressDescription: '',
              additional_documents: []
            } as DocumentFormValuesType
          }
          onSubmit={() => {
            handleSubmit();
          }}
          validate={values => {
            const errors: Partial<Record<keyof DocumentFormValuesType, string>> = {};
            if (values.idType?.value !== 'vnin' && !values.validIdName) errors.validIdName = 'Id Image is required';
            if (!values.idNumber) errors.idNumber = 'Id number is required';
            if (!values.proofOfAddressName) errors.proofOfAddressName = 'Proof of Address is required';
            return errors;
          }}
        >
          {({ setFieldValue, errors, touched, values }) => {
            const safeValues: DocumentFormValuesType = {
              ...values,
              idType: typeof values.idType === 'object' ? values.idType : undefined
            };

            if (values.validIdName && values.idNumber && values.proofOfAddressName) {
              setIsFormValid(true);
            } else if (values.idType?.value === 'vnin' && values.idNumber?.length === 16 && values.proofOfAddressName) {
              setIsFormValid(true);
            } else if (
              values.idType?.value === 'vnin' &&
              values.idNumber?.length === 16 &&
              addFormState.fileUploaded === addFormState.fileAdded &&
              addFormState.fileUploaded > 0
            ) {
              setIsFormValid(true);
            } else {
              setIsFormValid(false);
            }
            return (
              <>
                {configErrorMessage(errors, touched)}
                {touched.idNumber && idTypeError(safeValues)}
                <Form>
                  <div className="input__wrap">
                    <label className="screen-reader-only document-label" htmlFor="valid_id">
                      <span>Valid Id</span>
                      <Tooltip title="Valid ID" />
                    </label>
                  </div>
                  <div className="kyc-input-group">
                    <div className="input__wrap">
                      <CustomReactSelect
                        id="valid_id"
                        data-testid="valid_id"
                        label="Select a document type"
                        placeholder={findValidIdOption(values?.idType?.value) || 'Document type'}
                        options={validIdOptions}
                        value={values?.idType?.value}
                        onChange={val => {
                          void setFieldValue('vnin', null);
                          setIdFile(null);
                          void setFieldValue('idType', val);
                          void setFieldValue('idNumber', '');
                          void setFieldValue('validIdName', '');
                          void setFieldValue('validIdDescription', '');
                        }}
                      />
                    </div>

                    <div className="input__wrap">
                      <Field name="idNumber">
                        {({ field }: { field: FieldInputProps<string> }) => (
                          <input
                            required
                            type="text"
                            {...field}
                            placeholder={`${values?.idType?.value === 'vnin' ? 'Enter your 16 digit VNIN code' : 'Enter ID registration number'}`}
                            autoComplete="off"
                          />
                        )}
                      </Field>
                    </div>
                  </div>

                  {values?.idType?.value === 'vnin' && (
                    <div className="vnin-hint">
                      <div className="header">
                        <img src={Info} alt="info-icon" />
                        <p>How to generate Virtual NIN</p>
                      </div>
                      <p>
                        Dial *346*3*Your NIN*471335# on a registered phone number to create your 16-digit VNIN. The generated VNIN code
                        should be submitted for verification within 48 hours; this service costs N20.0
                      </p>
                    </div>
                  )}
                  {values?.idType && values?.idType?.value !== 'vnin' && (
                    <div className="upload-button" style={{ background: idFile ? '#48CEB01A' : undefined, marginBottom: '20px' }}>
                      <div>
                        {idFile ? <img src={CorrectSvg} alt="document" /> : <img src={DocumentSvg} alt="document" />}
                        <p>{findValidIdOption(values?.idType?.value) || 'Valid Id'}</p>
                      </div>
                      {!idFile && (
                        <button onClick={() => fileRef1?.current?.click()} type="button">
                          <input
                            ref={fileRef1}
                            onChange={e => {
                              setIdFile('uploading');
                              handleChange(e, 'id', setFieldValue);
                            }}
                            multiple={false}
                            type="file"
                            accept="image/*, .docx, .pdf"
                            hidden
                            data-testid="valid_id_upload"
                          />
                          <p>Upload</p>
                          <img src={UploadSvg} alt="Upload file" />
                        </button>
                      )}
                      {idFile === 'uploading' && <p>Uploading...</p>}

                      {idFile?.includes('.') && (
                        <div className="file-present">
                          <p className="file-name">{formatFileName(idFile, idFile?.toString()?.split('.')[1])}</p>
                          <button
                            onClick={() => {
                              setIdFile(null);
                              void setFieldValue('validIdName', '');
                              void setFieldValue('validIdDescription', '');
                            }}
                            type="button"
                          >
                            <img src={CancelSvg} alt="Cancel file" />
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                  <div>
                    <div>
                      <label className="screen-reader-only document-label" htmlFor="proof_of_address">
                        <span>Proof of Address</span>
                        <Tooltip title="Proof of Address" />
                      </label>
                    </div>
                    <div className="upload-button" style={{ background: proofOfAddressFile ? '#48CEB01A' : undefined, marginTop: '10px' }}>
                      <div>
                        {proofOfAddressFile ? <img src={CorrectSvg} alt="document" /> : <img src={DocumentSvg} alt="document" />}
                        <p>Proof of Address</p>
                        <Tooltip title="Proof of Address" />
                      </div>
                      {!proofOfAddressFile && (
                        <button onClick={() => fileRef2?.current?.click()} type="button">
                          <input
                            data-testid="proof_of_address"
                            id="proof_of_address"
                            ref={fileRef2}
                            onChange={e => {
                              setProofOfAddressFile('uploading');
                              handleChange(e, 'proof_of_address', setFieldValue);
                            }}
                            multiple={false}
                            type="file"
                            accept="image/*, .docx, .pdf"
                            hidden
                          />
                          <p>Upload</p>
                          <img src={UploadSvg} alt="Upload file" />
                        </button>
                      )}
                      {proofOfAddressFile === 'uploading' && <p>Uploading...</p>}
                      {proofOfAddressFile?.includes('.') && (
                        <div className="file-present">
                          <p className="file-name">{formatFileName(proofOfAddressFile, proofOfAddressFile?.toString().split('.')[1])}</p>
                          <button
                            onClick={() => {
                              setProofOfAddressFile(null);
                              void setFieldValue('proofOfAddressName', '');
                              void setFieldValue('proofOfAddressDescription', '');
                            }}
                            type="button"
                          >
                            <img src={CancelSvg} alt="cancel-upload" />
                          </button>
                        </div>
                      )}
                    </div>
                    <div className="utility-bill-info">
                      <img src={WarningSVG} alt="delete-form" />
                      <p>
                        Utility bill presented must not exceed <span>3 months</span> prior to submission date.
                      </p>
                    </div>
                  </div>

                  <hr style={{ margin: '30px 0px' }} />

                  <div className="add-document">
                    {addFormState.fileAdded === 0 && !isAddDoc && (
                      <div style={{ display: 'flex' }}>
                        <button
                          onClick={() => {
                            setIsAddDoc(true);
                            setAddFormState({
                              ...addFormState,
                              fileAdded: addFormState.fileAdded + 1
                            });
                            values.additional_documents.push({
                              type: '',
                              file: {
                                description: '',
                                name: ''
                              }
                            });
                          }}
                          type="button"
                        >
                          + Add other documents
                        </button>
                        <Tooltip title="Add More Documents" />
                      </div>
                    )}
                    {(isAddDoc || addFormState.fileAdded > 0) && (
                      <FieldArray name="additional_documents">
                        {({
                          push,
                          remove
                        }: {
                          push: (value: { type: string; file: { description: string; name: string } }) => void;
                          remove: (index: number) => void;
                        }) => (
                          <div>
                            <div className="additional-documents">
                              {values.additional_documents.length > 0 && <span>Additional Documents</span>}
                              {values.additional_documents.length === additionalFormsCount.sinlgeFileUpload &&
                                addFormState.fileAdded === additionalFormsCount.sinlgeFileUpload &&
                                addFormState.fileUploaded === additionalFormsCount.minFileUploadCount && (
                                  <button
                                    type="button"
                                    onClick={() => {
                                      setAddFormState({
                                        ...addFormState,
                                        fileAdded: addFormState.fileAdded - 1
                                      });
                                      remove(0);
                                      setIsAddDoc(false);
                                    }}
                                  >
                                    Cancel <img src={DeleteSvg} alt="delete-form" />
                                  </button>
                                )}
                            </div>
                            {values.additional_documents?.map((item, index) => (
                              <div key={`additional_documents[${index}]`}>
                                {addFormState.fileAdded > addFormState.fileUploaded && !item.file?.name && index > 0 && (
                                  <label htmlFor="add-docs" className="screen-reader-only">
                                    <button
                                      type="button"
                                      onClick={() => {
                                        setAddFormState({
                                          ...addFormState,
                                          fileAdded: addFormState.fileAdded - 1
                                        });
                                        if (index > 0) {
                                          remove(index);
                                        }
                                        if (index === 0) {
                                          setIsAddDoc(false);
                                        }
                                      }}
                                    >
                                      Cancel <img src={DeleteSvg} alt="delete-form" />
                                    </button>
                                  </label>
                                )}

                                {!item.file?.name?.includes('.') && (
                                  <Field name={`additional_documents[${index}].type`}>
                                    {({ field }: { field: FieldInputProps<string> }) => (
                                      <div className="input__wrap">
                                        <input
                                          autoComplete="off"
                                          {...field}
                                          type="text"
                                          placeholder="Enter document title"
                                          onKeyUp={() => {
                                            if (values.additional_documents[index]?.type?.length > 1) {
                                              setState({
                                                ...state,
                                                feedback: {
                                                  ...state.feedback,
                                                  message: '',
                                                  visible: false
                                                }
                                              });
                                            }
                                          }}
                                          required
                                        />
                                      </div>
                                    )}
                                  </Field>
                                )}
                                <div className="upload-button" style={{ background: item.file?.name ? '#48CEB01A' : undefined }}>
                                  <div>
                                    {item.file?.name ? <img src={CorrectSvg} alt="document" /> : <img src={DocumentSvg} alt="document" />}
                                    <p>{!item.type ? 'Untitled Document' : item.type}</p>
                                  </div>
                                  {!item.file?.name && (
                                    <button
                                      onClick={() => {
                                        if (item.type.length > 1) {
                                          myRefs?.current[index]?.click();
                                        } else {
                                          setState({
                                            ...state,
                                            feedback: {
                                              ...state.feedback,
                                              message: 'Invalid document name',
                                              visible: true,
                                              type: 'danger'
                                            }
                                          });
                                        }
                                      }}
                                      type="button"
                                    >
                                      <input
                                        ref={el => {
                                          myRefs.current[index] = el;
                                        }}
                                        name={`additional_documents[${index}].type`}
                                        onChange={async event => {
                                          setFieldValue(`additional_documents[${index}].file.name`, 'uploading');
                                          await handleChange(event, `additional_documents${index}`, setFieldValue);
                                          setAddFormState({
                                            ...addFormState,
                                            fileUploaded: addFormState.fileUploaded + 1
                                          });
                                          setFieldValue(`additional_documents[${index}].type`, item.type);
                                        }}
                                        multiple={false}
                                        type="file"
                                        accept="image/*, .docx, .pdf"
                                        hidden
                                      />
                                      <p>Upload</p>
                                      <img src={UploadSvg} alt="upload" />
                                    </button>
                                  )}
                                  {item.file?.name === 'uploading' && <p>Uploading...</p>}
                                  {item.file?.name?.includes('.') && (
                                    <div className="file-present">
                                      <p className="file-name">
                                        {formatFileName(item.file.description, item.file.description?.toString().split('.')[1])}
                                      </p>
                                      <button
                                        onClick={() => {
                                          void setFieldValue(`additional_documents[${index}].file.name`, null);
                                          void setFieldValue(`additional_documents[${index}].file.description`, null);
                                          void setFieldValue(`additional_documents[${index}].type`, '');

                                          remove(index);

                                          if (index === 0 || values.additional_documents[0]) {
                                            setIsAddDoc(false);
                                          }

                                          setAddFormState({
                                            ...addFormState,
                                            fileAdded: addFormState.fileAdded - 1,
                                            fileUploaded: addFormState.fileUploaded - 1
                                          });
                                        }}
                                        type="button"
                                      >
                                        <img src={CancelSvg} alt="upload" />
                                      </button>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                            {addFormState.fileAdded === addFormState.fileUploaded &&
                              addFormState.fileAdded > additionalFormsCount.minFileAddCount &&
                              addFormState.fileUploaded < additionalFormsCount.maxFileUploadCount && (
                                <button
                                  type="button"
                                  onClick={() => {
                                    setAddFormState({
                                      ...addFormState,
                                      fileAdded: addFormState.fileAdded + 1
                                    });
                                    push({
                                      type: '',
                                      file: {
                                        description: '',
                                        name: ''
                                      }
                                    });
                                  }}
                                >
                                  + Add other documents
                                </button>
                              )}
                          </div>
                        )}
                      </FieldArray>
                    )}
                  </div>

                  <div className="btn-wrapper mt-5 sm-hide">
                    <button className="btn-kpy --full-blue --kyc-btn" type="submit" disabled={processActionBtnState()}>
                      {state.isLoading ? <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" /> : null}
                      {state.isLoading ? <span style={{ marginLeft: '0.5rem' }}>Submitting...</span> : 'Continue'}
                    </button>
                  </div>
                </Form>
              </>
            );
          }}
        </Formik>
      </div>
    </div>
  );
};

export default Documents;
