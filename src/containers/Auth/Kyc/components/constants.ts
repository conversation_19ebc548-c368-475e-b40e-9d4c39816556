export const validIds = [
  {
    value: 1,
    label: 'International Passport',
    code: 'international_passport'
  },
  {
    value: 2,
    label: 'Driver’s License',
    code: 'drivers_license'
  },
  {
    value: 3,
    label: 'Voter’s Card',
    code: 'voters_card'
  },
  {
    value: 4,
    label: 'VNIN',
    code: 'vnin'
  },
  {
    value: 5,
    label: 'NIN',
    code: 'nin'
  }
];

export const ngnCurrencies = [
  { value: 'NGN', label: 'NGN' }
  // { value: 'USD', label: 'USD' }
];

export const kesCurrencies = [{ value: 'KES', label: 'KES' }];

export const ghsCurrencies = [{ value: 'GHS', label: 'GHS' }];

export const zarCurrencies = [{ value: 'ZAR', label: 'ZAR' }];

export const otherCurrencies = [{ value: 'NGN', label: 'NGN' }];

export const bankCurrencies = [{ value: 'NGN', label: 'Nigerian Naira' }];

export const additionalFormsCount = {
  minFileAddCount: 0,
  maxFileAddCount: 3,
  minFileUploadCount: 0,
  maxFileUploadCount: 3,
  sinlgeFileUpload: 1
};

export function parseBackendRegex(regexString: string): RegExp {
  if (!regexString) return /.*/;
  const regexParts = regexString.match(/^\/(.*)\/(.*)?$/);
  return regexParts ? new RegExp(regexParts[1], regexParts[2]) : new RegExp(regexString);
}

export function getIdTypeErrorMessage(idTypeLabel: string): string {
  switch (idTypeLabel) {
    case 'international_passport':
      return 'International Passport must contain numbers and letters and must be 9 digits';
    case 'ke_passport':
      return 'International Passport can contain only numbers and letters and must be 8-9 digits';
    case 'gh_passport':
      return 'International Passport can contain only numbers and letters and must be 8 digits';
    case 'drivers_license':
      return 'Drivers license must contain numbers and letters and must be 12 digits';
    case 'gh_drivers_license':
      return 'Drivers license can contain numbers and letters and must be 6-20 digits';
    case 'voters_card':
      return 'Voters card must contain only numbers and letters and must be 19 digits';
    case 'gvc':
      return 'Voters card must contain only numbers and letters and must be 10 digits';
    case 'vnin':
      return 'VNIN must contain only numbers and letters and must be 16 digits';
    case 'nin':
      return 'NIN must contain only numbers and must be 11 digits';
    case 'ke_nin':
      return 'NIN must contain only numbers and must be 8-10 digits';
    case 'gh_ssnit':
      return 'SSNIT can contain only numbers and letters and must be 13 digits';
    case 'said':
      return 'ID number must contain only numbers and must be 13 digits';
    default:
      return 'Invalid ID format';
  }
}
