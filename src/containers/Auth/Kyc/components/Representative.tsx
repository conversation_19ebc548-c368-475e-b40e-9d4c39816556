/* eslint-disable camelcase */
import { useEffect, useRef, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import Select, { CSSObjectWithLabel, StylesConfig } from 'react-select';
import { useInterval } from 'react-use';

import { uploadFiles } from '+hooks/utils';
import { KYCServices } from '+services/kyc-services';
import Feedback from '+shared/Feedback';
import {
  DocumentsUploadType,
  ErrorResponseType,
  IKYCMetaData,
  IOption,
  IRepresentativeProps,
  ISendResponse,
  OptionType,
  RepresentativeResponseType,
  RepresentativeType,
  SubmitValuesType,
  UploadResponseType
} from '+types';
import { logError } from '+utils';

import { getIdTypeErrorMessage, parseBackendRegex } from './constants';
import Headings from './Headings';
import KycFeedback from './KycFeedback';
import Tooltip from './Tooltip';

import CancelSvg from '+assets/img/auth/cancel.svg';
import CorrectSvg from '+assets/img/auth/correct.svg';
import DocumentSvg from '+assets/img/auth/document.svg';
import UploadSvg from '+assets/img/auth/upload.svg';
import Info from '+assets/img/dashboard/vnin-info.svg';

import './Representative.scss';

const Representative = ({
  kycStage,
  updateStage,
  registeredType,
  readKycFeedback,
  submittedRepresentatives = [],
  updateSubmittedKyc,
  formRef,
  setMobileActionBtnState,
  validIdOptions
}: IRepresentativeProps) => {
  const [roles, setRoles] = useState<Array<Omit<IOption, 'id'>>>([]);
  const formDefaults: RepresentativeType[] =
    submittedRepresentatives?.length > 0
      ? (submittedRepresentatives as unknown as RepresentativeType[])
      : [
          {
            name: '',
            role: '',
            documents: [{ type: '', id: '', file: { description: '', name: '' } }]
          }
        ];

  const {
    control,
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    getValues,
    formState: { isValid, isDirty }
  } = useForm<{
    representatives: RepresentativeType[];
  }>({
    mode: 'all',
    defaultValues: { representatives: formDefaults }
  });
  const { fields, append, remove } = useFieldArray<{
    representatives: RepresentativeType[];
  }>({
    control,
    name: 'representatives'
  });
  const myRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [fileUploadCount, setFileUploadCount] = useState<{ required: number; uploaded: number }>({
    required: 1,
    uploaded: 0
  });

  const [state, setState] = useState<{
    isLoading: boolean;
    feedback: {
      message: string;
      visible: boolean;
      type: 'danger' | '';
    };
    firstRole?: string;
    validationError?: string;
  }>({
    isLoading: false,
    feedback: {
      message: '',
      visible: false,
      type: 'danger'
    }
  });

  const [isAutoSave, setIsAutoSave] = useState<boolean>(false);

  const [documentUploading, setDocumentUploading] = useState<{ [key: number]: boolean }>({});

  const isIdUploadEnabled = validIdOptions?.length > 0;
  const documentIsEmpty = (index: number): boolean => {
    const name = watch(`representatives.${index}.documents.0.file.name`);
    return name === '' || !name;
  };

  const resetAutoSave = (): void => {
    setIsAutoSave(false);
    reset(getValues());
  };

  let idInvalid = false;

  const idTypeError = (values: RepresentativeResponseType['documents'][0]) => {
    let message = '';
    let regexString = '';
    if (typeof values.type === 'object' && values.type !== null) {
      regexString = values.type.regex || '';
    }
    if (regexString && values.id) {
      const regex = parseBackendRegex(regexString);
      if (!regex.test(values.id)) {
        idInvalid = true;
        const value = typeof values.type === 'object' && values.type !== null ? values.type.value || '' : '';
        message = getIdTypeErrorMessage(value);
        return message ? <Feedback type="danger" message={message} /> : null;
      }
    }
    idInvalid = false;
    return null;
  };

  useEffect(() => {
    if (submittedRepresentatives?.length > 0) {
      const fileCount = submittedRepresentatives.reduce((acc: number, item: RepresentativeResponseType) => {
        return acc + (item.documents?.[0]?.type === 'vnin' ? 1 : item.documents?.[0]?.file?.name ? 1 : 0);
      }, 0);
      setFileUploadCount({
        uploaded: fileCount,
        required: submittedRepresentatives.length
      });
      submittedRepresentatives.forEach((item: RepresentativeResponseType, index: number) => {
        setValue(`representatives.${index}.name`, item.name);
        setValue(`representatives.${index}.role`, item.role?.id);
        if (item.shareholding_company_name !== undefined) {
          setValue(`representatives.${index}.shareholding_company_name`, item?.shareholding_company_name);
        } else {
          const validIdType = validIdOptions.find(id => id.value === item.documents[0]?.type) || item.documents[0]?.type;
          setValue(`representatives.${index}.documents.0.file.name`, item.documents[0]?.file?.name);
          setValue(`representatives.${index}.documents.0.file.description`, item.documents[0]?.file?.description);
          setValue(`representatives.${index}.documents.0.type`, validIdType);
        }
      });
    }

    void refetch();
  }, []);

  const { refetch } = KYCServices.useGetKYCMetaData<'representative_roles'>({
    bannerLevel: true,
    metaData: 'representative_roles',
    onSuccess: (data: ISendResponse<IKYCMetaData>) => {
      const options =
        data?.data?.representative_roles?.map(item => ({
          value: item.id,
          label: item.label
        })) || [];
      setRoles(options);
    }
  });

  const style: StylesConfig<OptionType, false> = {
    singleValue: (base: CSSObjectWithLabel) => ({
      ...base,
      position: 'absolute',
      top: 'unset',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
      width: 'calc(100% - 40px)'
    }),
    control: (base: CSSObjectWithLabel) => ({
      ...base,
      boxShadow: 'none',
      border: '2px solid #eff2f7 !important;',
      marginBottom: '20px',
      borderRadius: '8px',
      height: '58px'
    }),
    placeholder: (defaultStyles: CSSObjectWithLabel) => ({
      ...defaultStyles,
      position: 'absolute',
      top: 'unset',
      color: '#3e4b5b',
      fontSize: '15px'
    }),
    dropdownIndicator: (base: CSSObjectWithLabel) => ({
      ...base,
      position: 'absolute',
      top: '20%',
      right: '0%'
    }),
    indicatorSeparator: () => ({})
  };

  const handleOnSubmit = (values: SubmitValuesType) => {
    const { is_auto_save, ...val } = values;
    if (is_auto_save !== true) setState({ ...state, isLoading: true });

    const formattedData = val.representatives.map(item => {
      const { name, role, documents } = item;
      const cleanedDocuments = documents.map(doc => {
        const newDoc: DocumentsUploadType = { ...doc, type: typeof doc.type === 'object' && doc.type !== null ? doc.type.value : doc.type };

        if (!isIdUploadEnabled || (role === 'shareholding_company' && registeredType === 'registered_business_non_sme')) {
          delete newDoc.id;
        }
        delete newDoc.file?.url;
        if (typeof newDoc.type === 'string' && newDoc.type?.toLowerCase() !== 'vnin') delete newDoc.text;
        if (typeof newDoc.type === 'string' && newDoc.type?.toLowerCase() === 'vnin') {
          const text = {
            name: 'vnin',
            value: newDoc.id
          };
          delete newDoc.file;
          newDoc.text = text;
        }
        return newDoc;
      });

      return {
        name,
        role,
        documents: cleanedDocuments
      };
    });
    businessRepMutation.mutate({ representatives: formattedData, ...(is_auto_save === true && { is_auto_save }) });
  };

  const handleOnSubmitForSme = (values: SubmitValuesType) => {
    const { is_auto_save, ...val } = values;
    if (is_auto_save !== true) setState({ ...state, isLoading: true });
    const formattedData = val.representatives.map(obj => {
      const { name, role, shareholding_company_name, documents } = obj;

      const cleanedDocuments = documents.map(doc => {
        const newDoc: DocumentsUploadType = { ...doc, type: typeof doc.type === 'object' && doc.type !== null ? doc.type.value : doc.type };

        delete newDoc.file?.url;
        if (!isIdUploadEnabled) delete newDoc.id;
        if (typeof newDoc.type === 'string' && newDoc.type?.toLowerCase() !== 'vnin') delete newDoc.text;
        if (typeof newDoc.type === 'string' && newDoc.type?.toLowerCase() === 'vnin') {
          const text = {
            name: 'vnin',
            value: newDoc.id
          };
          delete newDoc.file;
          newDoc.text = text;
        }
        return newDoc;
      });

      const newObj = { name, role, shareholding_company_name, documents };
      if (shareholding_company_name) {
        newObj.shareholding_company_name = shareholding_company_name;
      }

      newObj.documents = cleanedDocuments;

      return newObj;
    });

    businessRepMutation.mutate({ representatives: formattedData, ...(is_auto_save === true && { is_auto_save }) });
  };

  const businessRepMutation = KYCServices.useBusinessRep({
    onSuccess: data => {
      if (isAutoSave) return resetAutoSave();
      updateSubmittedKyc(data.data);
      setState({ ...state, isLoading: false });
      return updateStage(4);
    },
    onError: e => {
      if (isAutoSave) return resetAutoSave();
      const error = (e as ErrorResponseType).response?.data;
      const errorMessage = () => {
        if (error?.message === 'KYC has already been verified and cannot be updated') {
          return 'KYC has already been verified, please refresh to continue';
        }
        if (error?.message === 'KYC is being reviewed and cannot be updated') {
          return 'KYC is being reviewed, please refresh to continue';
        }
        if (error?.message) {
          return error.message;
        }
        return 'There seems to be an issue with the submission';
      };
      setState({
        ...state,
        isLoading: false,
        feedback: { ...state.feedback, message: `${errorMessage()}`, visible: true, type: 'danger' }
      });
    }
  });

  const showFeedback = () => {
    const { feedback } = state;
    return feedback.visible ? <Feedback type={feedback.type} message={feedback.message} /> : '';
  };

  const formatFileName = (docName: string, type: string): string => {
    if (docName.length > 10) {
      const firstFileName = docName.substring(0, 10);
      return `${firstFileName} ...${type}`;
    }
    return `${docName}`;
  };

  const maxFileSize = Number(process.env.REACT_APP_MAX_FILE_SIZE) || 20;

  const disabled = !isValid || idInvalid || !(fileUploadCount.required === fileUploadCount.uploaded) || state.isLoading;

  useEffect(() => {
    setMobileActionBtnState({ disabled });
  }, [disabled, idInvalid]);

  useEffect(() => {
    setMobileActionBtnState({ isLoading: state.isLoading });
  }, [state.isLoading]);

  useInterval(() => {
    if (isDirty) {
      const data = { is_auto_save: true, representatives: watch('representatives') };
      setIsAutoSave(true);
      if (registeredType === 'registered_business_sme') {
        console.log('data', data);
        handleOnSubmitForSme(data);
      } else {
        handleOnSubmit(data);
      }
    }
  }, 30000);

  return (
    <div>
      <div className="kyc__form">
        <Headings kycStage={kycStage} />
        {readKycFeedback && <KycFeedback feedback={readKycFeedback} />}

        <div style={{ maxWidth: '492px' }}>{showFeedback()}</div>
        <form
          ref={formRef}
          onSubmit={e => {
            e.preventDefault();
            void handleSubmit(data => (registeredType === 'registered_business_sme' ? handleOnSubmitForSme(data) : handleOnSubmit(data)))(
              e
            );
          }}
        >
          {fields.map((item, index) => {
            return (
              <div key={item.id}>
                {watch(`representatives.${index}.documents.0`) && idTypeError(watch(`representatives.${index}.documents.0`))}
                <div className="kyc-input-group">
                  <div className="input__wrap">
                    <label htmlFor="name" className="screen-reader-only representative-label">
                      Name of Representative
                    </label>

                    <Controller
                      name={`representatives.${index}.role`}
                      defaultValue=""
                      control={control}
                      rules={{ required: true }}
                      render={({ field: { onChange, value } }) => (
                        <Select
                          isSearchable={false}
                          styles={{
                            ...style,
                            indicatorSeparator: () => ({}),
                            container: base => ({
                              ...base,
                              height: '100%'
                            }),
                            valueContainer: base => ({
                              ...base,
                              display: 'flex',
                              height: '100%'
                            })
                          }}
                          options={roles}
                          placeholder="Select Rep ..."
                          value={roles.find(c => c.value === value) as OptionType}
                          onChange={val => {
                            onChange(val?.value);
                            setState({
                              ...state,
                              firstRole:
                                typeof val?.value === 'string' ? val?.value : val?.value !== undefined ? String(val?.value) : undefined
                            });
                            if (`representatives.${index}.documents[0].file.name` !== null) {
                              setFileUploadCount({
                                ...fileUploadCount,
                                uploaded:
                                  fileUploadCount.uploaded === fileUploadCount.required
                                    ? fileUploadCount.uploaded - 1
                                    : fileUploadCount?.uploaded
                              });
                            }
                            setValue(`representatives.${index}.documents.0.file.name`, '');
                            setValue(`representatives.${index}.documents.0.file.description`, '');
                            setValue(`representatives.${index}.documents.0.id`, '');
                            let newType = '';

                            if (!isIdUploadEnabled) {
                              newType = 'valid_id';
                            } else if (
                              registeredType === 'registered_business_non_sme' &&
                              val &&
                              typeof val === 'object' &&
                              'value' in val &&
                              val.value === 'shareholding_company'
                            ) {
                              newType = 'memart';
                            }
                            setValue(`representatives.${index}.documents.0.type`, newType);
                          }}
                        />
                      )}
                    />
                  </div>
                  <div className="input__wrap">
                    {index !== 0 ? (
                      <button
                        className="remove-rep"
                        type="button"
                        onClick={() => {
                          if (fileUploadCount.required === 1) {
                            setFileUploadCount({
                              ...fileUploadCount,
                              required: fileUploadCount.required - 1
                            });
                          } else if (fileUploadCount.required > fileUploadCount.uploaded) {
                            setFileUploadCount({
                              ...fileUploadCount,
                              required: fileUploadCount.required - 1
                            });
                          } else {
                            setFileUploadCount({
                              ...fileUploadCount,
                              required: fileUploadCount.required - 1,
                              uploaded: fileUploadCount.uploaded - 1
                            });
                          }
                          remove(index);
                        }}
                      >
                        Remove x
                      </button>
                    ) : (
                      <label htmlFor="role" className="screen-reader-only role" style={{ visibility: 'hidden' }}>
                        Role
                      </label>
                    )}

                    <input
                      {...register(
                        watch(`representatives.${index}.role`) === 'shareholding_company' && registeredType === 'registered_business_sme'
                          ? `representatives.${index}.shareholding_company_name`
                          : `representatives.${index}.name`,
                        {
                          required: true,
                          minLength: 3
                        }
                      )}
                      type="text"
                      placeholder={
                        watch(`representatives.${index}.role`) === 'shareholding_company' && registeredType === 'registered_business_sme'
                          ? 'Enter Shareholding Company Name'
                          : 'Enter Representative Name'
                      }
                    />
                  </div>
                </div>

                {watch(`representatives.${index}.role`) === 'shareholding_company' && registeredType === 'registered_business_sme' && (
                  <>
                    <label htmlFor="name" className="screen-reader-only beneficiary-label">
                      <span>Ultimate Beneficiary</span>
                      <Tooltip title="Ultimate Beneficiaries" />
                    </label>

                    <input
                      {...register(`representatives.${index}.name`, {
                        required: true
                      })}
                      type="text"
                      placeholder="Enter Ultimate Beneficiary Name"
                    />
                  </>
                )}
                {isIdUploadEnabled &&
                  (registeredType === 'registered_business_non_sme' &&
                  watch(`representatives.${index}.role`) === 'shareholding_company' ? null : (
                    <div className="kyc-input-group">
                      <div className="input__wrap">
                        <label htmlFor="name" className="screen-reader-only representative-label">
                          Valid ID
                        </label>
                        <Controller
                          name={`representatives.${index}.documents.0.type`}
                          defaultValue=""
                          control={control}
                          rules={{ required: true }}
                          render={({ field: { onChange } }) => (
                            <Select
                              isSearchable={false}
                              styles={{
                                ...style,
                                indicatorSeparator: () => ({}),
                                container: base => ({
                                  ...base,
                                  height: '100%'
                                }),
                                valueContainer: base => ({
                                  ...base,
                                  display: 'flex',
                                  height: '100%'
                                })
                              }}
                              options={validIdOptions}
                              placeholder="Select document type"
                              value={watch(`representatives.${index}.documents.0.type`) as OptionType}
                              onChange={val => {
                                onChange(val);
                                if (`representatives.${index}.documents[0].file.name` !== null) {
                                  setFileUploadCount({
                                    ...fileUploadCount,
                                    uploaded:
                                      fileUploadCount.uploaded === fileUploadCount.required
                                        ? fileUploadCount.uploaded - 1
                                        : fileUploadCount?.uploaded
                                  });
                                }
                                if (val && typeof val === 'object' && 'value' in val && val.value === 'vnin') {
                                  setFileUploadCount({
                                    ...fileUploadCount,
                                    uploaded:
                                      fileUploadCount.required > fileUploadCount.uploaded
                                        ? fileUploadCount.uploaded + 1
                                        : fileUploadCount.uploaded
                                  });
                                }
                                setValue(`representatives.${index}.documents.0.file.name`, '');
                                setValue(`representatives.${index}.documents.0.id`, '');
                                setValue(`representatives.${index}.documents.0.file.description`, '');
                              }}
                            />
                          )}
                        />
                      </div>

                      <div className="input__wrap" style={{ marginTop: '32px' }}>
                        <input
                          {...register(`representatives.${index}.documents.0.id`, {
                            required: true
                          })}
                          type="text"
                          placeholder="Enter ID registration number "
                        />
                      </div>
                    </div>
                  ))}

                {watch(`representatives.${index}.documents.0.type.value`) === 'vnin' && (
                  <div className="vnin-hint">
                    <div className="header">
                      <img src={Info} alt="info-icon" />
                      <p>How to generate Virtual NIN</p>
                    </div>
                    <p>
                      Dial *346*3*Your NIN*471335# on a registered phone number to create your 16-digit VNIN. The generated VNIN code should
                      be submitted for verification within 48 hours; this service costs N20.0
                    </p>
                  </div>
                )}

                {(!isIdUploadEnabled || watch(`representatives.${index}.documents.0.type`)) &&
                  watch(`representatives.${index}.documents.0.type.value`) !== 'vnin' && (
                    <div
                      className="upload-button"
                      style={{
                        background: documentIsEmpty(index) ? '#F1F6FA' : '#EDFBF8'
                      }}
                    >
                      <div>
                        <img src={documentIsEmpty(index) ? DocumentSvg : CorrectSvg} alt="document" />
                        <p>
                          {registeredType === 'registered_business_non_sme' &&
                          watch(`representatives.${index}.role`) === 'shareholding_company'
                            ? 'MEMART'
                            : watch(`representatives.${index}.documents.0.type.label`) || 'Valid Id'}
                        </p>
                        {watch(`representatives.${index}.role`) === 'shareholding_company' ? (
                          <Tooltip title="MEMART" />
                        ) : (
                          <Tooltip title="Valid ID" />
                        )}
                      </div>
                      {documentIsEmpty(index) && !documentUploading?.[index] && (
                        <button onClick={() => myRefs?.current[index]?.click()} type="button">
                          <Controller
                            name={`representatives.${index}.documents.0.file.name`}
                            control={control}
                            render={({ field: { onChange } }) => (
                              <input
                                onChange={e => {
                                  const file = e.target.files?.[0];
                                  const fileName = e.currentTarget.files?.[0].name;
                                  const fileType = fileName?.substring(fileName?.indexOf('.') + 1) || '';
                                  let formatFileSize = 0;
                                  if (file) {
                                    formatFileSize = Math.floor(file.size / 1024 ** 2);
                                  }
                                  setDocumentUploading({
                                    ...documentUploading,
                                    [index]: true
                                  });
                                  const data = new FormData();
                                  data.append(`files`, e.target.files?.[0] as Blob);
                                  if (!file) setState(prevState => ({ ...prevState, validationError: 'Please upload a file' }));
                                  if (formatFileSize > maxFileSize) {
                                    setValue(`representatives.${index}.documents.0.file.name`, '');
                                    return setState({
                                      ...state,
                                      feedback: {
                                        ...state.feedback,
                                        message: `Please upload a file less than ${maxFileSize}MB in size`,
                                        visible: true,
                                        type: 'danger'
                                      }
                                    });
                                  }
                                  uploadFiles(data)
                                    .then((res: UploadResponseType) => {
                                      onChange(res?.data[0]?.path);
                                      setState({
                                        ...state,
                                        feedback: {
                                          ...state.feedback,
                                          message: '',
                                          type: '',
                                          visible: false
                                        }
                                      });
                                      setFileUploadCount({
                                        ...fileUploadCount,
                                        uploaded: fileUploadCount.uploaded + 1
                                      });
                                      setValue(
                                        `representatives.${index}.documents.0.file.description`,
                                        formatFileName(res?.data[0]?.original_name, fileType)
                                      );
                                    })
                                    .catch(err => {
                                      setValue(`representatives.${index}.documents.0.file.name`, '');
                                      logError(err);
                                      setState({
                                        ...state,
                                        isLoading: false,
                                        feedback: {
                                          ...state.feedback,
                                          message: `There's been an error uploading your file. Please try again`,
                                          visible: true
                                        }
                                      });
                                    })
                                    .finally(() => {
                                      setDocumentUploading({
                                        ...documentUploading,
                                        [index]: false
                                      });
                                    });
                                }}
                                multiple={false}
                                type="file"
                                accept="image/*, .docx, .pdf"
                                id="file"
                                ref={el => {
                                  myRefs.current[index] = el;
                                }}
                                hidden
                              />
                            )}
                          />
                          <p>Upload</p>
                          <img src={UploadSvg} alt="upload" />
                        </button>
                      )}
                      {documentUploading?.[index] === true && <p>Uploading...</p>}

                      {watch(`representatives.${index}.documents.0.file.name`)?.includes('.') && (
                        <div className="file-present">
                          <p className="file-name">{watch(`representatives.${index}.documents.0.file.description`)}</p>
                          <button
                            onClick={() => {
                              setFileUploadCount({
                                ...fileUploadCount,
                                uploaded: fileUploadCount.uploaded - 1
                              });
                              setValue(`representatives.${index}.documents.0.file.name`, '', { shouldDirty: true });
                              setValue(`representatives.${index}.documents.0.file.description`, '', { shouldDirty: true });
                            }}
                            type="button"
                          >
                            <img src={CancelSvg} alt="upload" />
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                <hr />
              </div>
            );
          })}
          <button
            type="button"
            onClick={() => {
              setFileUploadCount({
                ...fileUploadCount,
                required: fileUploadCount.required + 1
              });
              append({
                name: '',
                role: '',
                documents: [{ type: '', file: { description: '', name: '' } }]
              });
            }}
            className="add-rep"
          >
            +{'  '}Add another representative
          </button>

          <div className="btn-wrapper mt-5 sm-hide">
            <button
              className="btn-kpy --full-blue --kyc-btn"
              type="submit"
              disabled={!isValid || idInvalid || !(fileUploadCount.required === fileUploadCount.uploaded) || state.isLoading}
            >
              {state.isLoading ? <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" /> : null}
              {state.isLoading ? <span style={{ marginLeft: '0.5rem' }}>Submitting...</span> : 'Continue'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Representative;
