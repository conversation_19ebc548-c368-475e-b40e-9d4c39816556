import { useEffect, useRef, useState } from 'react';
import { useInterval } from 'react-use';
import { Field, Form, Formik, useFormikContext } from 'formik';
import { isEqual } from 'lodash';
import * as yup from 'yup';

import CustomReactSelect from '+containers/Shared/CustomReactSelect';
import Feedback from '+containers/Shared/Feedback';
import { useDisableNumberInputScroll } from '+dashboard/Shared/hooks/useDisableNumberInputScroll';
import { useBanks } from '+hooks';
import useIndustries from '+hooks/useIndustries';
import useMobileOperators from '+hooks/useMobileOperators';
import useSMECategoriesQuery from '+hooks/useSMECategory';
import useTrusteeCategoriesQuery from '+hooks/useTrusteeCategoriesQuery';
import { KYCServices } from '+services/kyc-services';
import { Storage } from '+services/storage-services';
import { UtilServices } from '+services/util-services';
import { IBanks, IKycForm, IKycResponse, IOperators, IOptions } from '+types/kycform-types';
import { merchantCountryCodes } from '+types/merchantCountryTypes';
import { backwardAmountInput, convertAmountStringToNumber, formatAmount, handleKeyPress, logError } from '+utils';

import AdditionalDocuments from './components/AdditionalDocuments';
import BusinessAddress from './components/BusinessAddress';
import { ghsCurrencies, kesCurrencies, ngnCurrencies, otherCurrencies, zarCurrencies } from './components/constants';
import Headings from './components/Headings';
import KycFeedback from './components/KycFeedback';
import UploadButton from './components/UploadButton';
import WorldwideSettlementAcount from './components/WorldwideSettlementAccount';
import { getFormattedKycData } from './FormatKycData';
import { calculateMaxDateOfBirth, handleApiCall, handleStageNumber } from './KycUtils';

import WebsiteSvg from '+assets/img/auth/website.svg';

const yupSchema = yup.object().shape({
  website: yup.string().url().required().max(250, 'Website URL must be less than 250 characters')
});

const SubmitButton = ({ kycStage, processActionBtnState, state }) => {
  const { isValid, errors } = useFormikContext();
  return (
    <button
      className="btn-kpy --full-blue --kyc-btn sm-hide"
      type="submit"
      disabled={processActionBtnState({ isValid, errors })}
      id={`${kycStage}-btn`}
    >
      {}
      {state.isLoading ? <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" /> : null}
      {state.isLoading ? (
        <span style={{ marginLeft: '0.5rem' }}>{state.buttonValue ? state.buttonValue : 'Submitting...'}</span>
      ) : kycStage === 'settlement_account' ? (
        'Submit'
      ) : (
        'Continue'
      )}
    </button>
  );
};

const KycForm = ({
  merchantCountry,
  kycStage,
  businessType,
  schema,
  setMobileActionBtnState,
  updateStage,
  submittedProfile,
  bvnInformation,
  readKycFeedback,
  formRef,
  dataClearFeedback,
  updateSubmittedKyc
}: IKycForm) => {
  const { industries, getIndustries } = useIndustries();
  const [existingAccount, setExistingAccount] = useState(null);
  const { trusteeCategories, getTrusteeCategories } = useTrusteeCategoriesQuery();
  const { smeCategories, getSMECategories } = useSMECategoriesQuery();
  const [accountNameDisabled, setAccountNameDisabled] = useState(true);
  const [isAutoSave, setIsAutoSave] = useState(false);

  const formikRef = formRef;

  const isSettlementAccountForm = kycStage === 'settlement_account' && merchantCountry !== merchantCountryCodes.Nigeria;

  useDisableNumberInputScroll();
  const { data: banks } = useBanks(
    merchantCountry === merchantCountryCodes.Kenya ? 'KES' : merchantCountry === merchantCountryCodes.SouthAfrica ? 'ZAR' : 'NGN'
  );

  const { data: operators } = useMobileOperators('GHS');

  const banksOptions: IOptions[] = banks?.data?.map((item: IBanks) => ({
    value: item.code,
    label: item.name
  }));

  const operatorOptions: IOptions[] | undefined = operators?.data?.map((item: IOperators) => ({
    value: item.code,
    label: item.name
  }));

  const [state, setState] = useState({
    isLoading: false,
    buttonValue: '',
    verificationError: false,
    maxDate: '2004-12-31',
    selectedIndustry: {},
    feedback: {
      message: '',
      visible: false,
      type: 'danger'
    }
  });

  const [isFirstMount, setIsFirstMount] = useState(true);
  const [initialValues, setInitialValues] = useState({});

  useEffect(() => {
    setIsFirstMount(false);
    Storage.setItem('surveyStarted', 'false');
  }, []);

  useEffect(() => {
    getIndustries();
    getTrusteeCategories();
    getSMECategories();
    const backDate = calculateMaxDateOfBirth();
    setState({ maxDate: backDate });
  }, [state.maxDate]);
  useEffect(() => {
    if (submittedProfile) {
      let defaultValues = {
        website: removeHttps(submittedProfile?.website),
        source_of_funds: submittedProfile?.source_of_funds,
        business_description: submittedProfile?.business_description,
        industry: submittedProfile?.industry,
        incorporated_trustee_category:
          submittedProfile?.incorporated_trustee_category?.id || submittedProfile?.incorporated_trustee_category,
        sme_type: submittedProfile.sme_type?.id || submittedProfile.sme_type,
        amount: submittedProfile?.expected_monthly_income?.amount,
        currency: submittedProfile?.expected_monthly_income?.currency,
        date_of_birth: submittedProfile?.date_of_birth ? new Date(submittedProfile?.date_of_birth).toISOString().split('T')[0] : undefined
      };
      if (kycStage === 'documents') {
        submittedProfile.forEach(item => {
          if (item?.is_additional_document === false) {
            defaultValues = {
              ...defaultValues,
              [item?.type]: {
                type: item?.type,
                ...(item?.id && { id: item?.id }),
                file: {
                  description: item?.file?.description,
                  name: item?.file?.name
                }
              }
            };
          }
          if (item?.type === 'certificate_of_incorporation' && merchantCountry === merchantCountryCodes?.Nigeria) {
            defaultValues['idNumber'] = item?.id;
          }

          if (item?.type === 'ke_tax_pin') {
            defaultValues['ke_tax_pin'] = item?.id;
          }
        });

        const additionalDocs = submittedProfile?.filter(item => {
          return item?.is_additional_document === true;
        });

        if (additionalDocs.length > 0) {
          const additional_documents = additionalDocs.map((item, index) => {
            return {
              type: item?.type,
              file: {
                name: item?.file?.name,
                description: item?.file?.description
              }
            };
          });

          defaultValues['additional_documents'] = additional_documents;
        }
      }

      if (kycStage === 'settlement_account') {
        const accountDetails = submittedProfile?.[Object.keys(submittedProfile || {})]?.[0];
        setExistingAccount(accountDetails);
        defaultValues = {
          ...defaultValues,
          account_number: accountDetails?.account_details?.account_number,
          mobile_number: accountDetails?.account_details?.mobile_number,
          account_name: accountDetails?.account_details?.account_name,
          currency: accountDetails?.currency,
          bank_code: accountDetails?.account_details?.bank_code,
          operator: accountDetails?.account_details?.mobile_money_operator_code,
          bvn: accountDetails && bvnInformation
        };

        if (accountDetails?.account_details?.account_name && merchantCountry === merchantCountryCodes.Kenya) {
          setAccountNameDisabled(false);
        }

        if (isSettlementAccountForm && Object?.keys(submittedProfile || {})?.length > 0) {
          defaultValues['isBankAccount'] = 'yes';
        }
      }

      if (kycStage === 'business_profile') {
        defaultValues = {
          ...defaultValues,
          LGA: submittedProfile?.business_address_details?.lga,
          state: submittedProfile?.business_address_details?.state,
          address_line: submittedProfile?.business_address_details?.address_line,
          city: submittedProfile?.business_address_details?.city,
          landmark: submittedProfile?.business_address_details?.landmark
        };
      }
      const cleanedValues = Object.entries(defaultValues).reduce((acc, [key, value]) => {
        if (value === undefined) return acc;
        return { ...acc, [key]: value };
      }, {});
      setInitialValues(cleanedValues);
      formikRef?.current?.resetForm({ values: cleanedValues });
    } else {
      formikRef?.current?.resetForm();
    }
  }, [submittedProfile, kycStage, merchantCountry, bvnInformation]);

  const onRequestSettled = () => {
    const { values, resetForm } = formikRef.current;
    if (!isAutoSave) resetForm({ values });
    setInitialValues(values);
  };

  const onRequestSuccess = (data: IKycResponse) => {
    onRequestSettled();
    const hasBVN = schema?.fields?.some(field => field.name === 'bvn');
    const hasValidBVN = hasBVN ? formikRef?.current?.values.bvn?.length === 11 : true;
    const hasCertificateOfIncorporation = schema?.fields?.some(
      field => field?.name === 'certificate_of_incorporation' && field?.requiresIdNumber
    );
    const hasValidIDNumber = hasCertificateOfIncorporation
      ? formikRef?.current?.values?.idNumber?.length >= 8 && formikRef?.current?.values?.idNumber?.length <= 12
      : true;
    setState({
      ...state,
      isLoading: false,
      verificationError: !hasValidBVN || !hasValidIDNumber || Boolean(state.feedback?.message && state.feedback?.type === 'danger')
    });
    if (isAutoSave) return setIsAutoSave(false);
    updateSubmittedKyc(data?.data);
    formikRef.current.resetForm({ values: {} });
    return updateStage(handleStageNumber(kycStage, businessType));
  };

  const onRequestError = e => {
    onRequestSettled();
    if (isAutoSave) return setIsAutoSave(false);
    const error = e.response?.data;
    window.scrollTo(0, 0);
    const errorMessage = () => {
      if (error?.data?.['individual.website']) {
        return 'Website or social media url is invalid';
      }
      if (error?.message === 'invalid request data') {
        return "There's an issue with one or more of the information you provided. Please update and try again";
      }
      if (error?.message === 'KYC has already been verified and cannot be updated') {
        return 'KYC has already been verified, please refresh to continue';
      }
      if (error?.message === 'KYC is being reviewed and cannot be updated') {
        return 'KYC is being reviewed, please refresh to continue';
      }
      if (error?.error === 'bad_request') {
        return Object.values(error?.data)[0].message;
      }
      if (error?.message) {
        const message = error?.message.charAt(0).toUpperCase() + error?.message.slice(1);
        return message;
      }
      return 'There seems to be an issue submitting your KYC details.';
    };
    setState({
      ...state,
      isLoading: false,
      feedback: { ...state.feedback, type: 'danger', message: `${errorMessage()}`, visible: true }
    });
  };

  const businessProfileMutation = KYCServices.useBusinessProfile({
    bannerLevel: true,
    onSuccess: onRequestSuccess,
    onError: onRequestError
  });

  const settlementAccountMutation = KYCServices.useSettlementAccount({
    bannerLevel: true,
    onSuccess: onRequestSuccess,
    onError: onRequestError
  });

  const skipComplianceCategoryMutation = KYCServices.useSkipComplianceCategory({
    bannerLevel: true,
    onSuccess: onRequestSuccess,
    onError: onRequestError
  });

  const businessDocMutation = KYCServices.useBusinessDoc({
    bannerLevel: true,
    onSuccess: onRequestSuccess,
    onError: onRequestError
  });

  const handleMutation = data => {
    handleApiCall({
      data,
      kycStage,
      businessProfile: businessProfileMutation.mutate,
      settlementAccount: settlementAccountMutation.mutate,
      skipComplianceCategory: skipComplianceCategoryMutation.mutate,
      businessDoc: businessDocMutation.mutate
    });
  };

  const handleSubmit = values => {
    const { is_auto_save, ...rest } = values;
    const isAutoSaveEnabled = is_auto_save === true;
    if (!isAutoSaveEnabled) {
      setState({
        ...state,
        isLoading: true
      });
    } else {
      setIsAutoSave(true);
    }

    const formattedData = getFormattedKycData(rest, kycStage, businessType, merchantCountry);
    handleMutation({
      ...formattedData,
      ...(isAutoSaveEnabled ? { is_auto_save: true } : {})
    });
  };
  const removeHttps = (url: string) => {
    let format = url;
    if (url?.startsWith('https://')) {
      format = url.replace('https://', '');
    }
    if (url?.startsWith('http://')) {
      format = url.replace('http://', '');
    }
    if (format === 'undefined') return undefined;
    return format;
  };

  useInterval(() => {
    if (formikRef.current?.dirty || !isEqual(formikRef.current?.values, initialValues)) {
      const errors = validate(formikRef.current.values);
      const values = Object.entries(formikRef.current.values).reduce((result, [key, value]) => {
        const field = schema.fields.find(item => item.name === key);
        const fieldWithIdNumber = schema.fields.find(item => item?.requiresIdNumber);
        if (
          errors?.[key] ||
          (field?.requiresIdNumber && !formikRef.current.values.idNumber) ||
          (key === 'idNumber' && fieldWithIdNumber && !formikRef.current.values?.[fieldWithIdNumber?.name])
        )
          return result;
        return { ...result, [key]: value };
      }, {});
      handleSubmit({ ...values, is_auto_save: true });
    }
  }, 30000);

  const setAdditionaldocumentLength = () => {
    return formikRef.current?.values?.additional_documents?.length || 0;
  };

  const setUploadError = message => {
    setState(prevState => ({
      ...prevState,
      isLoading: false,
      feedback: {
        ...state.feedback,
        message,
        visible: true,
        type: 'danger'
      }
    }));
  };

  const clearFeedback = () => {
    setState(prevState => ({
      ...prevState,
      feedback: {
        ...state.feedback,
        message: '',
        type: '',
        visible: false
      }
    }));
  };

  const setServerUploadError = () => {
    setState({
      ...state,
      feedback: {
        ...state.feedback,
        message: `There's been an issue uploading your file. Please try again`,
        type: 'danger',
        visible: true
      }
    });
  };

  const onClearData = (setFieldValue, values) => {
    if (existingAccount) setExistingAccount(null);
    if (values.currency) setFieldValue('currency', '');
    if (values.mobile_number) setFieldValue('mobile_number', '');
    if (values.operator) setFieldValue('operator', '');
    if (values.bank_code) setFieldValue('bank_code', '');
    if (values.bvn) setFieldValue('bvn', '');
    if (values.account_number) setFieldValue('account_number', '');
    if (values.account_name) setFieldValue('account_name', '');
  };
  const renderFormFields = (form, setFieldValue, values, setValues) => {
    const handleFieldChange = field => {
      if (values?.operator && values?.mobile_number?.length === 12) {
        setState({
          ...state,
          verificationError: false
        });
        resolveMobileAccount.mutate({
          mobileMoneyCode: values.operator,
          phoneNumber: values.mobile_number?.toString(),
          currency: values.currency
        });
      }
      if (field === 'account_number' && values?.account_number?.length < 10) {
        setFieldValue('account_name', '');
      } else if (field === 'account_number' && values?.bank_code && values?.account_number?.length >= 10) {
        resolveAccount.mutate({
          bank: values?.bank_code,
          account: values?.account_number?.toString(),
          currency: values?.currency
        });
      } else if (field === 'bvn') {
        if (values?.bvn?.length !== 11) {
          setState({
            ...state,
            verificationError: true
          });
        } else {
          setState({
            ...state,
            isLoading: true,
            buttonValue: 'Verifying....'
          });
          verifyDetails.mutate({
            type: 'bvn',
            country: merchantCountryCodes.Nigeria,
            number: values.bvn
          });
        }
      } else if (field === 'certificate_of_incorporation') {
        if (!values?.idNumber || values?.idNumber?.length < 8 || values?.idNumber?.length > 12) {
          setState({
            ...state,
            verificationError: true
          });
        } else {
          setState({
            ...state,
            verificationError: false
          });
        }
      }
    };

    let options;
    switch (merchantCountry) {
      case merchantCountryCodes.Kenya:
        options = kesCurrencies;
        break;
      case merchantCountryCodes.Nigeria:
        options = ngnCurrencies;
        break;
      case merchantCountryCodes.Ghana:
        options = ghsCurrencies;
        break;
      case merchantCountryCodes.SouthAfrica:
        options = zarCurrencies;
        break;
      default:
        options = otherCurrencies;
        break;
    }
    const getMaxLength = (fieldName: string) => {
      switch (fieldName) {
        case 'bvn':
          return 11;
        case 'mobile_number':
          return 12;
        case 'KES':
          return 20;
        case 'ZAR':
          return 12;
        default:
          return 10;
      }
    };

    return form.fields.map(field => {
      let inputField = null;
      const isDisabled = values?.isBankAccount === 'no' ? true : field?.name === 'account_name' ? accountNameDisabled : field?.readOnly;
      switch (field.field_type) {
        case 'text':
          inputField = (
            <Field
              type={field.field_type}
              name={field.name}
              id={field.name}
              {...field}
              disabled={isDisabled}
              readonly={isDisabled}
              required={field?.required}
            />
          );
          break;
        case 'text-website':
          inputField = (
            <Field name="website">
              {({ field }) => (
                <div className="input__wrap">
                  <div className="website-field">
                    <div>
                      <img src={WebsiteSvg} alt="website icon" />
                      <span> https://</span>
                    </div>
                    <input id={field.name} autoComplete="off" {...field} type="text" />
                  </div>
                </div>
              )}
            </Field>
          );
          break;
        case 'date':
          inputField = <Field type={field.field_type} name={field.name} id={field.name} {...field} max={state.maxDate} />;
          break;
        case 'select-industry':
          inputField = (
            <CustomReactSelect
              id={field.name}
              label="Select an option"
              options={industries}
              placeholder={submittedProfile?.industry?.label || ''}
              onChange={val => {
                setFieldValue(field.name, val.value);
              }}
              value={values?.[field.name]}
              isSearchable
            />
          );
          break;
        case 'select-currency':
          inputField = (
            <CustomReactSelect
              id={field.name}
              label={field.label}
              placeholder={existingAccount?.currency || ''}
              isDisabled={isDisabled}
              options={options}
              onChange={val => {
                setFieldValue(field.name, val.label);
              }}
              value={values?.[field.name]}
            />
          );
          break;
        case 'select-bank':
          inputField = (
            <CustomReactSelect
              id={field.name}
              label={field.label}
              options={banksOptions}
              placeholder={existingAccount?.bank?.name || ''}
              isSearchable
              isDisabled={isDisabled}
              onChange={val => {
                setFieldValue(field.name, val.value);
                resolveAccount.mutate({
                  bank: val.value,
                  account: values.account_number?.toString(),
                  currency: values.currency
                });
              }}
              value={values?.[field.name]}
            />
          );
          break;
        case 'select-operator':
          inputField = (
            <CustomReactSelect
              id={field.name}
              label={field.label}
              options={operatorOptions}
              placeholder={existingAccount?.account_details?.mobile_money_operator_name || ''}
              isSearchable
              isDisabled={isDisabled}
              onChange={val => {
                setFieldValue(field.name, val.value);
                if (values?.mobile_number) {
                  resolveMobileAccount.mutate({
                    mobileMoneyCode: val.value,
                    phoneNumber: values.mobile_number?.toString(),
                    currency: values.currency
                  });
                }
              }}
              value={values?.[field.name]}
            />
          );
          break;
        case 'select-trustee':
          inputField = (
            <CustomReactSelect
              id={field.name}
              label={field.label}
              options={trusteeCategories}
              placeholder={submittedProfile?.incorporated_trustee_category?.label || ''}
              onChange={val => {
                setFieldValue(field.name, val.value);
              }}
              value={values?.[field.name]}
            />
          );
          break;
        case 'select-sme':
          inputField = (
            <CustomReactSelect
              id={field.name}
              label={field.label}
              options={smeCategories}
              placeholder={submittedProfile?.sme_type?.label || ''}
              onChange={val => {
                setFieldValue(field.name, val.value);
              }}
              value={values?.[field.name]}
            />
          );
          break;
        case 'textarea':
          inputField = (
            <Field
              as="textarea"
              type={field.field_type}
              name={field.name}
              id={field.name}
              required={field?.required}
              {...field}
              maxLength="600"
            />
          );
          break;
        case 'upload':
          inputField = (
            <UploadButton
              values={values}
              label={field.label}
              required={field.required}
              name={field.name}
              setFieldValue={setFieldValue}
              setUploadError={setUploadError}
              clearFeedback={clearFeedback}
              setServerUploadError={setServerUploadError}
              setValues={setValues}
              handleFieldChange={handleFieldChange}
              merchantCountry={merchantCountry}
            />
          );
          break;
        case 'number':
          inputField = (
            <Field
              name={field.name}
              id={field.name}
              maxLength={field.name === 'bvn' || field.name === 'mobile_number' ? getMaxLength(field.name) : getMaxLength(values?.currency)}
              disabled={isDisabled}
              required={values?.isBankAccount === 'no' ? false : field?.required}
              onKeyUp={() => handleFieldChange(field.name)}
            />
          );
          break;
        case 'amount':
          inputField = (
            <Field name="amount">
              {({ field }) => (
                <div className="input__wrap">
                  <div className="input-with-select">
                    <CustomReactSelect
                      id="currency"
                      label=""
                      classNamePrefix="filter"
                      options={options}
                      placeholder={submittedProfile?.expected_monthly_income?.currency || ''}
                      onChange={val => {
                        setFieldValue('currency', val.label);
                      }}
                      value={values?.currency}
                    />
                    <input
                      id={field.name}
                      inputMode="numeric"
                      autoComplete="off"
                      {...field}
                      value={formatAmount(field.value)}
                      type="text"
                      className="input"
                      placeholder="Enter expected amount"
                      onKeyPress={handleKeyPress}
                      onChange={e => {
                        const rawValue = backwardAmountInput(e.target.value) as string;
                        const numericValue = convertAmountStringToNumber(rawValue);
                        setFieldValue('amount', numericValue);
                      }}
                      required
                    />
                  </div>
                </div>
              )}
            </Field>
          );
          break;
        default:
          inputField = null;
          break;
      }
      return (
        <div key={field.name} className="input__wrap">
          {field.field_type !== 'upload' && (
            <>
              <label htmlFor={field.name} className="screen-reader-only">
                {field.label}
              </label>
              {field.name === 'bvn' && (
                <p className="bvn-note">Please input a BVN that belongs to one of your representatives/stakeholders.</p>
              )}
            </>
          )}
          {inputField}
        </div>
      );
    });
  };
  const prevErrorsRef = useRef({});

  const processActionBtnState = ({ isValid, errors }) => {
    const disabled =
      !isValid ||
      state?.isLoading ||
      state?.verificationError ||
      (!formikRef.current?.dirty && Object?.values(formikRef.current?.values || {}).length === 0) ||
      Object.keys(errors).length > 0;

    setMobileActionBtnState({ disabled });
    if (!isEqual(errors, prevErrorsRef.current)) {
      if (errors && Object.keys(errors || {}).length > 0) {
        logError(errors);
      }
      prevErrorsRef.current = errors;
    }
    return disabled;
  };

  const validate = values => {
    let errors = {};
    schema.fields.forEach(field => {
      const fieldValue = values[field.name];

      if (field.name === 'website') {
        let website = fieldValue;
        const re = /^(http|https):\/\//i;
        if (!re.test(fieldValue)) {
          website = `https://${fieldValue}`;
        }
        try {
          yupSchema.validateSyncAt('website', { website });
        } catch (error) {
          errors[field.name] = error.message;
        }
      }

      if (!fieldValue && field.required) {
        errors[field.name] = `${field.label} is required.`;
      }
    });

    if (values.additional_documents?.length > 0) {
      const isAdditionalDocumentInvalid = values?.additional_documents?.some(obj => {
        return Object.values(obj).some(value => value?.name === '' || value?.name === 'uploading');
      });
      if (isAdditionalDocumentInvalid) {
        errors.add = 'required';
      }
    }

    if (kycStage === 'business_profile') {
      if (!values?.state) {
        errors.state = `State is required`;
      }

      if (merchantCountry === merchantCountryCodes.Nigeria) {
        if (!values?.LGA) {
          errors.LGA = `LGA is required`;
        }
      } else if (!values?.city) {
        errors.city = `City is required`;
      }

      if (!values?.address_line) {
        errors.address_line = `Address is required`;
      }
    }

    if (submittedProfile && isFirstMount) {
      errors = {};
    }

    if (values?.isBankAccount === 'no') {
      errors = {};
    }

    if (kycStage === 'documents' && merchantCountry === merchantCountryCodes.Nigeria) {
      if (!values?.idNumber) {
        errors.idNumber = `CAC registration number is required.`;
      }
    }

    if (values?.mobile_number && values?.mobile_number?.length < 12) {
      errors.mobile_number = `Mobile money number must be 12 digits`;
    }

    return errors;
  };

  const configErrorMessage = (errors, touched) => {
    let msg = null;

    schema.fields.forEach(field => {
      if (touched[field.name]) {
        msg = errors[field.name];
      }
    });

    return msg ? <Feedback type="danger" message={msg} /> : null;
  };

  const showFeedback = () => {
    const { feedback } = state;
    return feedback?.visible ? <Feedback type={feedback.type} message={feedback.message} /> : '';
  };

  const resolveAccount = UtilServices.useBankEnquiry({
    useEnvironmentHandler: 'live',
    onSuccess: ({ data }) => {
      const accountName = data?.account_name;
      setState({
        ...state,
        feedback: { ...state.feedback, message: '', visible: false }
      });
      if (accountName || ['ke'].includes(merchantCountry?.toLowerCase()) || ['za'].includes(merchantCountry?.toLowerCase())) {
        formikRef.current.setFieldValue('account_name', accountName || '');
        setAccountNameDisabled(Boolean(accountName));
      }
    },
    onError: e => {
      const error = e?.response?.data;
      const errorMessage = () => {
        if (error?.message === 'invalid request data') {
          const customMessage = error?.data?.account?.message;
          const message = customMessage.charAt(0).toUpperCase() + customMessage.slice(1);
          return message;
        }
        return e?.response?.data?.message || 'There seems to be an issue with resolving your bank account';
      };
      formikRef.current.setFieldValue('account_name', '');
      setState({
        ...state,
        isLoading: false,
        feedback: { ...state.feedback, message: `${errorMessage()}`, visible: true, type: 'danger' }
      });
    }
  });

  const resolveMobileAccount = UtilServices.useMobileNumberEnquiry({
    useEnvironmentHandler: 'live',
    onSuccess: () => {
      setState({
        ...state,
        feedback: { ...state.feedback, message: '', visible: false }
      });
    }
  });

  const verifyDetails = KYCServices.useDetailsVerification({
    onSuccess: res => {
      if (res.data.status === 'not_found') {
        setState({
          ...state,
          isLoading: false,
          buttonValue: '',
          verificationError: true,
          feedback: {
            ...state.feedback,
            message: `BVN number could not be verified. Please ensure you input the correct BVN and try again.`,
            visible: true,
            type: 'danger'
          }
        });
      } else {
        setState({
          ...state,
          verificationError: false,
          isLoading: false,
          buttonValue: '',
          feedback: { ...state.feedback, message: '', visible: false }
        });
      }
    },
    onError: e => {
      const error = e?.response?.data;
      const errorMessage = () => {
        if (error?.error === 'too_many_requests') return 'Too many attempts, Please contact support...';
        if (error?.message === 'invalid request data') {
          const customMessage = error?.data?.account?.message;
          const message = customMessage.charAt(0).toUpperCase() + customMessage.slice(1);
          return message;
        }
        return `There was an issue while trying to verify your BVN. Please ensure you input the correct BVN and try again.`;
      };
      setState({
        ...state,
        isLoading: false,
        buttonValue: '',
        verificationError: true,
        feedback: { ...state.feedback, message: `${errorMessage()}`, visible: true, type: 'danger' }
      });
    }
  });

  useEffect(() => {
    formikRef.current?.validateForm();
  }, [formikRef.current?.values]);

  return (
    <>
      <Headings kycStage={kycStage} />
      {dataClearFeedback && (
        <KycFeedback
          bgColor
          feedback="Kindly note that any update on your business profile will imply that all other information will be submitted again"
        />
      )}
      {readKycFeedback && <KycFeedback feedback={readKycFeedback} />}
      {showFeedback()}
      <Formik
        innerRef={formikRef}
        initialValues={Array.isArray(submittedProfile) || !submittedProfile ? {} : submittedProfile}
        onSubmit={handleSubmit}
        validate={validate}
        validateOnBlur
        validateOnChange
      >
        {({ setFieldValue, errors, touched, values, setValues }) => {
          return (
            <>
              {configErrorMessage(errors, touched)}
              <Form>
                {kycStage === 'settlement_account' && merchantCountry !== merchantCountryCodes.Nigeria && (
                  <WorldwideSettlementAcount setFieldValue={setFieldValue} onClearData={() => onClearData(setFieldValue, values)} />
                )}
                {renderFormFields(schema, setFieldValue, values, setValues)}
                {kycStage === 'documents' && (
                  <AdditionalDocuments
                    values={values}
                    setFieldValue={setFieldValue}
                    clearFeedback={clearFeedback}
                    setUploadError={setUploadError}
                    setServerUploadError={setServerUploadError}
                    additionalDocsLength={setAdditionaldocumentLength()}
                  />
                )}
                {kycStage === 'business_profile' && (
                  <BusinessAddress merchantCountry={merchantCountry} setFieldValue={setFieldValue} values={values} />
                )}
                <SubmitButton kycStage={kycStage} processActionBtnState={processActionBtnState} state={state} />
              </Form>
            </>
          );
        }}
      </Formik>
    </>
  );
};

export default KycForm;
