export const kycSchema = {
  ng: {
    individual: {
      business_profile: {
        fields: [
          {
            label: 'Date of Birth',
            field_type: 'date',
            required: true,
            name: 'date_of_birth'
          },

          {
            label: 'Business Description',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website / Social Media',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Valid ID',
            field_type: 'select-id',
            required: true,
            name: 'valid_id',
            requiresIdNumber: true
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'BVN',
            field_type: 'number',
            required: true,
            name: 'bvn'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    non_governmental_organization: {
      business_profile: {
        fields: [
          {
            label: 'Describe Your Business',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Incorporated Trustee Category',
            field_type: 'select-trustee',
            required: true,
            name: 'incorporated_trustee_category'
          },
          {
            label: 'Website',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Certificate of Incorporation',
            field_type: 'upload',
            required: true,
            name: 'certificate_of_incorporation',
            requiresIdNumber: true
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          },
          {
            label: 'Approved Constitution',
            field_type: 'upload',
            required: true,
            name: 'approved_constitution'
          },
          {
            label: 'SCUML Certificate (optional)',
            field_type: 'upload',
            required: false,
            name: 'scuml_certificate'
          },
          {
            label: 'Due Diligence Questionnaire',
            field_type: 'upload',
            required: true,
            name: 'due_diligence_questionnaire'
          }
        ]
      },

      representative: {
        fields: [
          {
            label: 'Name of Representative',
            field_type: 'select',
            required: true,
            name: 'representative'
          },
          {
            label: '',
            field_type: 'text',
            required: true,
            name: ''
          },
          {
            label: '',
            field_type: 'upload',
            required: true
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'BVN',
            field_type: 'number',
            required: true,
            name: 'bvn'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    registered_business: {
      registered_business_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'SME Category',
              field_type: 'select-sme',
              required: true,
              name: 'sme_type'
            },

            {
              required: true,
              name: 'currency'
            },
            {
              label: 'Expected Monthly Income',
              field_type: 'amount',
              required: true,
              name: 'amount'
            },
            {
              label: 'Source of Fund',
              field_type: 'text',
              required: true,
              name: 'source_of_funds'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },
        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation',
              requiresIdNumber: true
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },
            {
              label: 'MEMART',
              field_type: 'upload',
              required: true,
              name: 'memart'
            },
            {
              label: 'Operating License (optional)',
              field_type: 'upload',
              required: false,
              name: 'operating_license'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'BVN',
              field_type: 'number',
              required: true,
              name: 'bvn'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      },
      registered_business_non_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },

        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation',
              requiresIdNumber: true
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },
            {
              label: 'MEMART',
              field_type: 'upload',
              required: true,
              name: 'memart'
            },
            {
              label: 'Operating License (optional)',
              field_type: 'upload',
              required: false,
              name: 'operating_license'
            },
            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'BVN',
              field_type: 'number',
              required: true,
              name: 'bvn'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      }
    }
  },
  ke: {
    individual: {
      business_profile: {
        fields: [
          {
            label: 'Date of Birth',
            field_type: 'date',
            required: true,
            name: 'date_of_birth'
          },
          {
            label: 'Business Description',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website / Social Media (Compulsory)',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Valid ID',
            field_type: 'upload',
            required: true,
            name: 'valid_id'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    non_governmental_organization: {
      business_profile: {
        fields: [
          {
            label: 'Describe Your Business',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Incorporated Trustee Category',
            field_type: 'select-trustee',
            required: true,
            name: 'incorporated_trustee_category'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website / Social Media',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Certificate of Incorporation',
            field_type: 'upload',
            required: true,
            name: 'certificate_of_incorporation'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          },
          {
            label: 'Articles of Association (optional)',
            field_type: 'upload',
            required: false,
            name: 'article_of_association'
          },
          {
            label: 'Register of Directors (optional)',
            field_type: 'upload',
            required: false,
            name: 'register_of_directors'
          },
          {
            label: 'Shareholding Structure (optional)',
            field_type: 'upload',
            required: false,
            name: 'shareholding_structure'
          },
          {
            label: 'Evidence of Registration as NGO (optional)',
            field_type: 'upload',
            required: false,
            name: 'evidence_of_registration_as_ngo'
          },
          {
            label: 'Due Diligence Questionnaire',
            field_type: 'upload',
            required: true,
            name: 'due_diligence_questionnaire'
          }
        ]
      },

      representative: {
        fields: [
          {
            label: 'Name of Representative',
            field_type: 'select',
            required: true,
            name: 'representative'
          },
          {
            label: '',
            field_type: 'text',
            required: true,
            name: ''
          },
          {
            label: '',
            field_type: 'upload',
            required: true
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    registered_business: {
      registered_business_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'SME Category',
              field_type: 'select-sme',
              required: true,
              name: 'sme_type'
            },
            {
              required: true,
              name: 'currency'
            },
            {
              label: 'Expected Monthly Income',
              field_type: 'amount',
              required: true,
              name: 'amount'
            },
            {
              label: 'Source of Fund',
              field_type: 'text',
              required: true,
              name: 'source_of_funds'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },

        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Articles of Association (optional)',
              field_type: 'upload',
              required: false,
              name: 'article_of_association'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },
            {
              label: 'Valid ID of Shareholders & Directors ',
              field_type: 'upload',
              required: true,
              name: 'valid_id'
            },
            {
              label: 'Operational License (optional)',
              field_type: 'upload',
              required: false,
              name: 'operating_license'
            },
            {
              label: 'Register of Directors (optional)',
              field_type: 'upload',
              required: false,
              name: 'register_of_directors'
            },
            {
              label: 'Shareholding Structure (optional)',
              field_type: 'upload',
              required: false,
              name: 'shareholding_structure'
            },
            {
              label: 'Tax Pin (optional)',
              required: false,
              name: 'ke_tax_pin',
              field_type: 'text'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      },
      registered_business_non_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },

        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Articles of Association (optional)',
              field_type: 'upload',
              required: false,
              name: 'article_of_association'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },
            {
              label: 'Valid ID of Shareholders & Directors ',
              field_type: 'upload',
              required: true,
              name: 'valid_id'
            },
            {
              label: 'Register of Directors (optional)',
              field_type: 'upload',
              required: false,
              name: 'register_of_directors'
            },
            {
              label: 'Shareholding Structure (optional)',
              field_type: 'upload',
              required: false,
              name: 'shareholding_structure'
            },
            {
              label: 'Operational License (optional)',
              field_type: 'upload',
              required: false,
              name: 'operating_license'
            },
            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            },
            {
              label: 'Tax Pin (optional)',
              required: false,
              name: 'ke_tax_pin',
              field_type: 'text'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      }
    }
  },
  gh: {
    individual: {
      business_profile: {
        fields: [
          {
            label: 'Date of Birth',
            field_type: 'date',
            required: true,
            name: 'date_of_birth'
          },
          {
            label: 'Business Description',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website / Social Media (Compulsory)',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Valid ID',
            field_type: 'upload',
            required: true,
            name: 'valid_id'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Mobile Money Number',
            field_type: 'number',
            required: true,
            name: 'mobile_number'
          },
          {
            label: 'Select an Operator',
            field_type: 'select-operator',
            required: true,
            name: 'operator'
          }
        ]
      }
    },
    non_governmental_organization: {
      business_profile: {
        fields: [
          {
            label: 'Describe Your Business',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Incorporated Trustee Category',
            field_type: 'select-trustee',
            required: true,
            name: 'incorporated_trustee_category'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website / Social Media',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Certificate of Incorporation',
            field_type: 'upload',
            required: true,
            name: 'certificate_of_incorporation'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          },
          {
            label: 'Due Diligence Questionnaire',
            field_type: 'upload',
            required: true,
            name: 'due_diligence_questionnaire'
          }
        ]
      },

      representative: {
        fields: [
          {
            label: 'Name of Representative',
            field_type: 'select',
            required: true,
            name: 'representative'
          },
          {
            label: '',
            field_type: 'text',
            required: true,
            name: ''
          },
          {
            label: '',
            field_type: 'upload',
            required: true
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Mobile Money Number',
            field_type: 'number',
            required: true,
            name: 'mobile_number'
          },
          {
            label: 'Select an Operator',
            field_type: 'select-operator',
            required: true,
            name: 'operator'
          }
        ]
      }
    },
    registered_business: {
      registered_business_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'SME Category',
              field_type: 'select-sme',
              required: true,
              name: 'sme_type'
            },
            {
              required: true,
              name: 'currency'
            },
            {
              label: 'Expected Monthly Income',
              field_type: 'amount',
              required: true,
              name: 'amount'
            },
            {
              label: 'Source of Fund',
              field_type: 'text',
              required: true,
              name: 'source_of_funds'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },

        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },
            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Mobile Money Number',
              field_type: 'number',
              required: true,
              name: 'mobile_number'
            },
            {
              label: 'Select an Operator',
              field_type: 'select-operator',
              required: true,
              name: 'operator'
            }
          ]
        }
      },
      registered_business_non_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },
        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },

            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Mobile Money Number',
              field_type: 'number',
              required: true,
              name: 'mobile_number'
            },
            {
              label: 'Select an Operator',
              field_type: 'select-operator',
              required: true,
              name: 'operator'
            }
          ]
        }
      }
    }
  },
  za: {
    individual: {
      business_profile: {
        fields: [
          {
            label: 'Date of Birth',
            field_type: 'date',
            required: true,
            name: 'date_of_birth'
          },
          {
            label: 'Business Description',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website / Social Media (Compulsory)',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Valid ID',
            field_type: 'upload',
            required: true,
            name: 'valid_id'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    non_governmental_organization: {
      business_profile: {
        fields: [
          {
            label: 'Describe Your Business',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Incorporated Trustee Category',
            field_type: 'select-trustee',
            required: true,
            name: 'incorporated_trustee_category'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Certificate of Incorporation',
            field_type: 'upload',
            required: true,
            name: 'certificate_of_incorporation'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          },
          {
            label: 'Due Diligence Questionnaire',
            field_type: 'upload',
            required: true,
            name: 'due_diligence_questionnaire'
          }
        ]
      },

      representative: {
        fields: [
          {
            label: 'Name of Representative',
            field_type: 'select',
            required: true,
            name: 'representative'
          },
          {
            label: '',
            field_type: 'text',
            required: true,
            name: ''
          },
          {
            label: '',
            field_type: 'upload',
            required: true
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    registered_business: {
      registered_business_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'SME Category',
              field_type: 'select-sme',
              required: true,
              name: 'sme_type'
            },
            {
              required: true,
              name: 'currency'
            },
            {
              label: 'Expected Monthly Income',
              field_type: 'amount',
              required: true,
              name: 'amount'
            },
            {
              label: 'Source of Fund',
              field_type: 'text',
              required: true,
              name: 'source_of_funds'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },

        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },
            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      },
      registered_business_non_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },
        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },

            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      }
    }
  },
  others: {
    individual: {
      business_profile: {
        fields: [
          {
            label: 'Date of Birth',
            field_type: 'date',
            required: true,
            name: 'date_of_birth'
          },
          {
            label: 'Business Description',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website / Social Media (Compulsory)',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Valid ID',
            field_type: 'upload',
            required: true,
            name: 'valid_id'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'BVN',
            field_type: 'number',
            required: true,
            name: 'bvn'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    non_governmental_organization: {
      business_profile: {
        fields: [
          {
            label: 'Describe Your Business',
            field_type: 'textarea',
            required: true,
            name: 'business_description'
          },
          {
            label: 'Incorporated Trustee Category',
            field_type: 'select-trustee',
            required: true,
            name: 'incorporated_trustee_category'
          },
          {
            label: 'Industry',
            field_type: 'select-industry',
            required: true,
            name: 'industry'
          },
          {
            label: 'Website',
            field_type: 'text-website',
            required: true,
            name: 'website'
          }
        ]
      },

      documents: {
        fields: [
          {
            label: 'Certificate of Incorporation',
            field_type: 'upload',
            required: true,
            name: 'certificate_of_incorporation'
          },
          {
            label: 'Proof of Address',
            field_type: 'upload',
            required: true,
            name: 'proof_of_address'
          },
          {
            label: 'Due Diligence Questionnaire',
            field_type: 'upload',
            required: true,
            name: 'due_diligence_questionnaire'
          }
        ]
      },

      representative: {
        fields: [
          {
            label: 'Name of Representative',
            field_type: 'select',
            required: true,
            name: 'representative'
          },
          {
            label: '',
            field_type: 'text',
            required: true,
            name: ''
          },
          {
            label: '',
            field_type: 'upload',
            required: true
          }
        ]
      },

      settlement_account: {
        fields: [
          {
            label: 'Currency',
            field_type: 'select-currency',
            required: false,
            name: 'currency'
          },
          {
            label: 'Account Number',
            field_type: 'number',
            required: true,
            name: 'account_number'
          },
          {
            label: 'Select a Bank',
            field_type: 'select-bank',
            required: false,
            name: 'bank_code'
          },
          {
            label: 'BVN',
            field_type: 'number',
            required: true,
            name: 'bvn'
          },
          {
            label: 'Name on Account',
            field_type: 'text',
            required: true,
            readOnly: true,
            name: 'account_name'
          }
        ]
      }
    },
    registered_business: {
      registered_business_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'SME Category',
              field_type: 'select-sme',
              required: true,
              name: 'sme_type'
            },
            {
              required: true,
              name: 'currency'
            },
            {
              label: 'Expected Monthly Income',
              field_type: 'amount',
              required: true,
              name: 'amount'
            },
            {
              label: 'Source of Fund',
              field_type: 'text',
              required: true,
              name: 'source_of_funds'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },

        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },
            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'BVN',
              field_type: 'number',
              required: true,
              name: 'bvn'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      },
      registered_business_non_sme: {
        business_profile: {
          fields: [
            {
              label: 'Describe Your Business',
              field_type: 'textarea',
              required: true,
              name: 'business_description'
            },
            {
              label: 'Industry',
              field_type: 'select-industry',
              required: true,
              name: 'industry'
            },
            {
              label: 'Website',
              field_type: 'text-website',
              required: true,
              name: 'website'
            }
          ]
        },
        documents: {
          fields: [
            {
              label: 'Certificate of Incorporation',
              field_type: 'upload',
              required: true,
              name: 'certificate_of_incorporation'
            },
            {
              label: 'Proof of Address',
              field_type: 'upload',
              required: true,
              name: 'proof_of_address'
            },

            {
              label: 'Due Diligence Questionnaire',
              field_type: 'upload',
              required: true,
              name: 'due_diligence_questionnaire'
            }
          ]
        },

        representative: {
          fields: [
            {
              label: 'Name of Representative',
              field_type: 'select',
              required: true
            },
            {
              label: '',
              field_type: 'text',
              required: true
            },
            {
              label: 'Valid ID',
              field_type: 'upload',
              required: true
            }
          ]
        },

        settlement_account: {
          fields: [
            {
              label: 'Currency',
              field_type: 'select-currency',
              required: false,
              name: 'currency'
            },
            {
              label: 'Account Number',
              field_type: 'number',
              required: true,
              name: 'account_number'
            },
            {
              label: 'Select a Bank',
              field_type: 'select-bank',
              required: false,
              name: 'bank_code'
            },
            {
              label: 'BVN',
              field_type: 'number',
              required: true,
              name: 'bvn'
            },
            {
              label: 'Name on Account',
              field_type: 'text',
              required: true,
              readOnly: true,
              name: 'account_name'
            }
          ]
        }
      }
    }
  }
};
