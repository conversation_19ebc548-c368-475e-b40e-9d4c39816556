import React, { useEffect } from 'react';
import { useIntercom } from 'react-use-intercom';

import { Storage } from '+services/storage-services';
import { history } from '+utils';

import VerifiedSvg from '+assets/img/auth/verified.svg';

const SURVEY_ID = process.env.REACT_APP_SURVEY_ID || '';

// Preload critical image and inject critical CSS
const optimizeInitialLoad = () => {
  // Preload image
  const imageLink = document.createElement('link');
  imageLink.rel = 'preload';
  imageLink.as = 'image';
  imageLink.href = VerifiedSvg;
  imageLink.fetchPriority = 'high';
  document.head.appendChild(imageLink);

  // Inject critical CSS inline to prevent render blocking
  const criticalCSS = `
    .kyc__done {
      display: flex;
      flex-direction: column;
      position: relative;
      contain: layout style paint;
    }
    .kyc__done h2 {
      font-size: 1.7rem;
      line-height: 1.2;
      margin-bottom: 0.7rem;
      font-weight: 500;
      color: #102649;
      letter-spacing: -0.014em;
      text-align: center;
    }
    .kyc__done span {
      font-size: 1rem;
      font-weight: 400;
      color: #3e4b5b;
      opacity: 0.6;
      margin-bottom: 3.125rem;
      text-align: center;
    }
    .kyc__done .continue-btn {
      padding: 0.9375rem 6.2188rem;
      background: #2376f3;
      color: white;
      border-radius: 5px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      display: block;
      margin: 0 auto;
      border: none;
      transition: background-color 0.2s ease;
    }
    .kyc__done .continue-btn:hover {
      background: rgba(35, 118, 243, 0.8);
    }
    .kyc__done img {
      width: 3rem;
      margin: 0 auto;
      aspect-ratio: 1;
    }
  `;

  if (!document.querySelector('[data-critical-kyc]')) {
    const style = document.createElement('style');
    style.textContent = criticalCSS;
    style.setAttribute('data-critical-kyc', 'true');
    document.head.appendChild(style);
  }
};

const KycComplete: React.FC = () => {
  const { startSurvey } = useIntercom();

  // Optimize initial load immediately
  useEffect(() => {
    optimizeInitialLoad();
  }, []);

  // Defer Intercom survey to avoid blocking rendering
  useEffect(() => {
    const timer = setTimeout(() => {
      const hasSurveyBeenStarted = Storage.getItem('surveyStarted');

      if (!hasSurveyBeenStarted || hasSurveyBeenStarted === 'false') {
        startSurvey(+SURVEY_ID);
        Storage.setItem('surveyStarted', 'true');
      }
    }, 100); // Defer by 100ms to prioritize rendering

    return () => clearTimeout(timer);
  }, [startSurvey]);

  return (
    <div className="kyc__done">
      <img src={VerifiedSvg} alt="Onboarding Done" width="48" height="48" loading="eager" decoding="sync" fetchPriority="high" />
      <h2>All done!</h2>
      <span>
        Your business details have been submitted successfully, and is under review. You can continue to use your dashboard in Test Mode.
      </span>
      <button
        type="button"
        className="continue-btn"
        onClick={() => {
          history.push('/dashboard');
        }}
      >
        Go to Dashboard
      </button>
    </div>
  );
};

export default React.memo(KycComplete);
