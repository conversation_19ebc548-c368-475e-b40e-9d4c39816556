import { cleanInput } from 'src/utils';

import {
  AddressType,
  DocumentToSendType,
  IDocumentsValues,
  IHandleIDNumberValues,
  IIndividualBusinessProfileValues,
  INGOBusinessProfileValues,
  IRegisteredBusinessProfileValues,
  ISettlementAccountValues,
  merchantCountryCodes
} from '+types';

export type GetFormattedKycDataReturn =
  | IIndividualBusinessProfileValues
  | IDocumentsValues
  | ISettlementAccountValues
  | INGOBusinessProfileValues
  | IRegisteredBusinessProfileValues
  | undefined;

export const getFormattedKycData = (values: GetFormattedKycDataReturn, kycStage: string, businessType: string, merchantCountry: string) => {
  if (kycStage === 'business_profile' && businessType === 'individual') {
    return formatIndividualBusinessProfile(values as IIndividualBusinessProfileValues);
  }
  if (kycStage === 'documents' && businessType === 'individual') {
    return formatIndividualDocuments(values as IDocumentsValues);
  }
  if (kycStage === 'settlement_account') {
    return formatSettlementAccount(values as ISettlementAccountValues, merchantCountry);
  }
  if (kycStage === 'business_profile' && businessType === 'ngo') {
    return formatNGOBusinessProfile(values as INGOBusinessProfileValues);
  }
  if (kycStage === 'documents' && businessType === 'ngo') {
    return formatNGODocuments(values as IDocumentsValues);
  }
  if (kycStage === 'business_profile' && businessType === 'Registered') {
    return formatRegisteredBusinessProfile(values as IRegisteredBusinessProfileValues);
  }
  if (kycStage === 'documents' && businessType === 'Registered') {
    return formatRegisteredDocuments(values as IDocumentsValues);
  }
};

const getFormattedBusinessAddress = (values: AddressType) => {
  return {
    address_line: cleanInput(values.address_line ?? ''),
    city: cleanInput(values.city ?? ''),
    state: cleanInput(values.state ?? ''),
    landmark: cleanInput(values.landmark ?? ''),
    lga: values.LGA ?? ''
  };
};

const formatIndividualBusinessProfile = (values: IIndividualBusinessProfileValues) => {
  let url = '';

  const re = new RegExp('^(http|https)://', 'i');
  if (!re.test(values.website ?? '')) {
    url = `https://${values.website || ''}`;
  }
  const data = {
    individual: {
      date_of_birth: values.date_of_birth,
      website: url || values.website,
      business_description: cleanInput(values.business_description ?? ''),
      business_address_details: getFormattedBusinessAddress(values),
      industry: values.industry
    }
  };

  return data;
};

const formatIndividualDocuments = (values: IDocumentsValues) => {
  const documents = Object.values(values);
  if (values.additional_documents !== undefined) {
    const additionalDocs = values.additional_documents;
    const newValues = values;
    delete newValues.additional_documents;
    const updatedDocuments = Object.values(newValues);
    return {
      documents: updatedDocuments,
      additional_documents: additionalDocs
    };
  }
  return { documents };
};

const formatSettlementAccount = (values: ISettlementAccountValues, merchantCountry: string) => {
  const data =
    values?.isBankAccount === 'yes' || values?.isBankAccount === undefined
      ? {
          settlement_accounts: [
            {
              currency: values.currency,
              type: merchantCountry === merchantCountryCodes.Ghana ? 'mobile_money' : 'bank_account',
              ...((merchantCountry === merchantCountryCodes.Nigeria || merchantCountry === merchantCountryCodes.Others) && {
                bvn: cleanInput(values.bvn ?? '')
              }),
              ...(merchantCountry === merchantCountryCodes.Ghana
                ? {
                    mobile_money: {
                      operator: values.operator,
                      mobile_number: values.mobile_number
                    }
                  }
                : {
                    bank_account: {
                      bank_code: values.bank_code,
                      account_number: cleanInput(values.account_number),
                      ...(merchantCountry === 'ke' && { account_name: cleanInput(values.account_name) })
                    }
                  })
            }
          ]
        }
      : {
          compliance_category: 'settlement_accounts'
        };
  delete values.isBankAccount;
  return data;
};

const formatNGOBusinessProfile = (values: INGOBusinessProfileValues) => {
  let url = '';
  if (values?.website && values?.website.substring(0, 8) !== 'https://') {
    url = `https://${values.website || ''}`;
  } else {
    url = values?.website;
  }
  const data = {
    ngo: {
      incorporated_trustee_category: values.incorporated_trustee_category,
      business_description: cleanInput(values.business_description ?? ''),
      business_address_details: getFormattedBusinessAddress(values),
      website: values?.website ? url : undefined,
      industry: values.industry
    }
  };
  return data;
};

const handleIDNumber = (value: IHandleIDNumberValues) => {
  const { idNumber } = value;
  delete value.idNumber;
  value.certificate_of_incorporation.id = idNumber;
  return value;
};

const handleAdditionalDoc = (value: IDocumentsValues) => {
  const additionalDocs = value.additional_documents && value.additional_documents.length > 0 ? value.additional_documents : undefined;
  const updatedValues = value;
  delete updatedValues.additional_documents;
  const updatedDocuments: DocumentToSendType[] = Object.entries(updatedValues)
    .filter(([key]) => key !== 'ke_tax_pin' && key !== 'idNumber' && key !== 'additional_documents')
    .map(([, doc]) => doc as DocumentToSendType);

  if (value.ke_tax_pin) {
    updatedDocuments.push({
      type: 'ke_tax_pin',
      id: value.ke_tax_pin
    });
  }

  return {
    documents: updatedDocuments,
    additional_documents: additionalDocs
  };
};

const formatNGODocuments = (values: IDocumentsValues) => {
  const newValues = { ...values };
  if (values?.certificate_of_incorporation && values.idNumber) {
    handleIDNumber(newValues);
  }

  const documents = Object.values(newValues);
  if (newValues.additional_documents !== undefined) {
    return handleAdditionalDoc(newValues);
  }
  return { documents };
};

const formatRegisteredBusinessProfile = (values: IRegisteredBusinessProfileValues) => {
  let isSme = false;
  const obj = { ...values };
  if (obj.website !== null && obj?.website?.substring(0, 8) !== 'https://') {
    obj.website = `https://${obj.website || ''}`;
  }
  const data: Record<string, IRegisteredBusinessProfileValues> = {};
  if (obj.sme_type) {
    isSme = true;
  }
  if (obj.amount !== undefined) {
    obj.expected_monthly_income = {
      amount: obj.amount,
      currency: obj.currency
    };
    delete obj.amount;
    delete obj.currency;
  }
  obj.business_address_details = getFormattedBusinessAddress(values as AddressType);
  delete obj.address_line;
  delete obj.city;
  delete obj.state;
  delete obj.landmark;
  delete obj.LGA;
  delete obj.business_address;
  data[isSme ? 'registered_business_sme' : 'registered_business_non_sme'] = obj;
  return data;
};

const formatRegisteredDocuments = (values: IDocumentsValues) => {
  const newValues = { ...values };
  if (values?.certificate_of_incorporation && values.idNumber) {
    handleIDNumber(newValues);
  }

  const documents: DocumentToSendType[] = Object.entries(newValues)
    .filter(([key]) => key !== 'ke_tax_pin' && key !== 'idNumber')
    .map(([, doc]) => doc as DocumentToSendType);

  if (values.ke_tax_pin) {
    documents.push({
      type: 'ke_tax_pin',
      id: values.ke_tax_pin
    });
  }

  if (values?.additional_documents !== undefined) {
    return handleAdditionalDoc(values);
  }

  return { documents };
};
