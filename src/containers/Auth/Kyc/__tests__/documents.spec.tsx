import { useRef } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { http, HttpResponse } from 'msw';
import { expect, vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import Documents from '../components/Documents';

vi.mock('+hooks/utils', () => ({
  uploadFiles: vi.fn().mockImplementation(formData => {
    const file = (formData as FormData).get('files') as File;
    const fileName = file?.name || 'unknown.png';

    return Promise.resolve({
      data: [
        {
          id: Date.now(),
          identifier: 'test-id',
          category: 'documents',
          original_name: fileName,
          encoding: 'utf-8',
          path: `files/${fileName}`,
          mime: file?.type || 'image/png',
          updatedAt: new Date().toISOString(),
          createdAt: new Date().toISOString()
        }
      ]
    });
  })
}));

const MockedKycForm = () => {
  const formRef = useRef(null);

  const mockFunction = vi.fn();

  const validIdOptions = [
    {
      value: 'nin',
      label: 'NIN',
      regex: '/^[0-9]{11}$/',
      country: 'NG'
    },
    {
      value: 'vnin',
      label: 'VNIN',
      regex: '/^[a-zA-Z0-9]{16}$/',
      country: 'NG'
    },
    {
      value: 'voters_card',
      label: "Voter's Card",
      regex: '/^[a-zA-Z0-9]{19}$/',
      country: 'NG'
    },
    {
      value: 'drivers_license',
      label: "Driver's License",
      regex: '/^[a-zA-Z0-9]{12}$/',
      country: 'NG'
    },
    {
      value: 'international_passport',
      label: 'International Passport',
      regex: '/^[a-zA-Z0-9]{9}$/',
      country: 'NG'
    }
  ];
  return (
    <MockIndex>
      <Documents
        merchantCountry="ng"
        setMobileActionBtnState={mockFunction}
        updateStage={mockFunction}
        submittedDocuments={[]}
        readKycFeedback=""
        formRef={formRef}
        updateSubmittedKyc={mockFunction}
        validIdOptions={validIdOptions}
      />
    </MockIndex>
  );
};

describe('Documents', () => {
  test('The Documents form is rendered', () => {
    render(<MockedKycForm />);

    const title = screen.getByText('Upload verification documents');
    const subTitle = screen.getByText(
      "For verification, you'll need to upload the documents indicated below, based on your type of business. Only .pdf, .docx, .png, jpg and jpeg files are allowed (max size 20MB each)."
    );
    const proofOfAddress = screen.getByTestId('proof_of_address');

    expect(title).toBeInTheDocument();
    expect(subTitle).toBeInTheDocument();
    expect(subTitle).toBeInTheDocument();
    expect(proofOfAddress).toBeInTheDocument();
  });

  test('Documents form submits data to api', async () => {
    let response: unknown;

    server.use(
      http.put('http://localhost:3000/api/kyc/documents', async ({ request }) => {
        response = await request.json();
        return HttpResponse.json(
          {
            status: true,
            message: 'Successful',
            data: {}
          },
          {
            status: 200
          }
        );
      })
    );

    render(<MockedKycForm />);
    const submitButton = screen.getByRole('button', { name: 'Continue' });

    await waitFor(() => expect(submitButton).toBeDisabled());

    await userEvent.click(screen.getByLabelText('Select a document type'));

    const selectedDocument = screen.getByText('NIN');

    await userEvent.click(selectedDocument);
    await userEvent.type(screen.getByPlaceholderText('Enter ID registration number'), '11111111111');

    const idUploadInput = screen.getByTestId('valid_id_upload');
    const idFile = new File(['id file content'], 'nin.png', { type: 'image/png' });
    await userEvent.upload(idUploadInput, idFile);
    expect(screen.getByText('nin.png')).toBeInTheDocument();

    const proofOfAddressInput = screen.getByTestId('proof_of_address');
    const proofFile = new File(['proof'], 'proof.pdf', { type: 'application/pdf' });
    await userEvent.upload(proofOfAddressInput, proofFile);
    expect(screen.getByText('proof.pdf')).toBeInTheDocument();

    await waitFor(() => expect(submitButton).toBeEnabled());

    await userEvent.click(submitButton);

    await waitFor(() =>
      expect(response).toEqual({
        documents: [
          {
            type: 'nin',
            id: '11111111111',
            file: {
              description: 'NIN',
              name: 'files/nin.png'
            }
          },
          {
            type: 'proof_of_address',
            file: {
              description: 'proof.pdf',
              name: 'files/proof.pdf'
            }
          }
        ]
      })
    );
  }, 10000);

  test('Show throw an error if an invalid ID number is provided', async () => {
    render(<MockedKycForm />);
    const submitButton = screen.getByRole('button', { name: 'Continue' });

    await userEvent.click(screen.getByLabelText('Select a document type'));

    const selectedDocument = screen.getByText('NIN');

    await userEvent.click(selectedDocument);
    await userEvent.type(screen.getByPlaceholderText('Enter ID registration number'), '0000000000');

    const idUploadInput = screen.getByTestId('valid_id_upload');
    const idFile = new File(['id file content'], 'nin.png', { type: 'image/png' });
    await userEvent.upload(idUploadInput, idFile);
    expect(screen.getByText('nin.png')).toBeInTheDocument();

    expect(screen.getByText('NIN must contain only numbers and must be 11 digits')).toBeInTheDocument();

    await waitFor(() => expect(submitButton).toBeDisabled());
  });

  test('Should render different fields if VNIN is selected', async () => {
    render(<MockedKycForm />);

    await userEvent.click(screen.getByLabelText('Select a document type'));

    const selectedDocument = screen.getByText('VNIN');

    await userEvent.click(selectedDocument);
    await userEvent.type(screen.getByPlaceholderText('Enter your 16 digit VNIN code'), '0000000000');

    expect(screen.getByText('How to generate Virtual NIN')).toBeInTheDocument();

    expect(
      screen.getByText(
        'Dial *346*3*Your NIN*471335# on a registered phone number to create your 16-digit VNIN. The generated VNIN code should be submitted for verification within 48 hours; this service costs N20.0'
      )
    ).toBeInTheDocument();

    expect(screen.queryByTestId('valid_id_upload')).not.toBeInTheDocument();
  });
});
