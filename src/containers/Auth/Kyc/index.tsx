import { useCallback, useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { useIntercom } from 'react-use-intercom';
import { isEqual } from 'lodash';

import { KYCServices } from '+services/kyc-services';
import Feedback from '+shared/Feedback';
import { SuspenseLoader as KpyLoader } from '+shared/LazyComponent';
import Modal from '+shared/Modal';
import useStore from '+store';
import { IMerchantKycInfo } from '+types';
import { merchantCountryCodes } from '+types/merchantCountryTypes';
import { actions, history, logError } from '+utils';

import Documents from './components/Documents';
import Headings from './components/Headings';
import MobileProgressBar from './components/MobileProgressBar';
import PageTitle from './components/PageTitle';
import ProgressBar from './components/ProgressBar';
import Representative from './components/Representative';
import Tooltip from './components/Tooltip';
import KycComplete from './KycComplete';
import KycForm from './KycForm';
import { kycSchema } from './KycSchema';

import ImgKorapayLogo from '+assets/img/logos/logo-kpy-ent.png';
import IndividualSvg from '+assets/img/auth/individual.svg';
import ImgBackArrowSvg from '+assets/img/auth/left-arrow.svg';
import LaterSvg from '+assets/img/auth/logout.svg';
import NgoSvg from '+assets/img/auth/ngo.svg';
import RegisteredSvg from '+assets/img/auth/registered.svg';
import ArrowRightSvg from '+assets/img/dashboard/caret-right.svg';
import ImgKorapayLogoSvg from '+assets/img/logos/logo-kpy-ent.svg';

import './index.scss';

const KycComponent = () => {
  const [stage, setStage] = useState(1);
  const [selected, setSelected] = useState('');
  const [registeredBusinessType, setRegisteredBusinessType] = useState('');
  const ref1 = useRef(null);
  const ref2 = useRef(null);
  const ref3 = useRef(null);
  const [kycFeedback, setKycFeedback] = useState({});
  const [flaggedForm, setFlaggedForm] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [allowBackButton, setAllowBackBtn] = useState(false);
  const [showThisLaterModal, setShowThisLaterModal] = useState(false);
  const [submittedKyc, setSubmittedKyc] = useState<IMerchantKycInfo | null>(null);
  const [isFirstRequest, setIsFirstRequest] = useState(true);
  const [mobileActionBtnProps, setMobileActionBtnProps] = useState({
    isLoading: false,
    disabled: true
  });
  const { update: updateIntercom } = useIntercom();
  const {
    defaultMerchant,
    authDetails: { enhanced_kyc }
  } = useStore();

  const [merchantCountry, setMerchantCountry] = useState('');
  const selectedCountry =
    defaultMerchant?.country?.iso2?.toLowerCase() === merchantCountryCodes.Nigeria ||
    defaultMerchant?.country?.iso2?.toLowerCase() === merchantCountryCodes.Kenya ||
    defaultMerchant?.country?.iso2?.toLowerCase() === merchantCountryCodes.Ghana ||
    defaultMerchant?.country?.iso2?.toLowerCase() === merchantCountryCodes.SouthAfrica
      ? defaultMerchant?.country?.iso2?.toLowerCase()
      : merchantCountryCodes.Others;

  const formRef = useRef();

  const isEnhancedKycEnabled = enhanced_kyc?.is_enabled;

  const [state, setState] = useState({
    isLoading: false,
    feedback: {
      message: '',
      visible: false,
      type: 'danger'
    }
  });
  const [initialRender, setInitialRender] = useState(true);

  const handleStageChange = (value: number) => {
    setStage(value);
    refetchMerchantKYC();
  };

  const incrementStage = (num: number) => {
    handleStageChange(num + 1);
  };

  const clearFeedback = (step: string) => {
    setKycFeedback({ ...kycFeedback, [step]: null });
  };

  const isMobile = window?.innerWidth < 720;

  useEffect(() => {
    setMerchantCountry(selectedCountry);
    updateIntercom({ hideDefaultLauncher: true });
    return () => {
      updateIntercom({ hideDefaultLauncher: false });
    };
  }, []);

  const handleSubmit = () => {
    if (selected === 'individual') {
      ref1.current.focus();
    }
    if (selected === 'ngo') {
      ref2.current.focus();
    }
    if (selected === 'Registered') {
      ref3.current.focus();
    }
    setState({ ...state, isLoading: true });
    if (selected === 'Registered') {
      const data = {
        business_type: registeredBusinessType
      };
      businessTypeMutation.mutate(data);
    } else {
      const data = {
        business_type: selected
      };
      businessTypeMutation.mutate(data);
    }
  };

  const handleMobileSubmit = () => {
    if (stage === 1) {
      return handleSubmit();
    }

    if (formRef?.current?.handleSubmit) {
      formRef?.current.handleSubmit();
    } else if (formRef?.current?.requestSubmit) {
      formRef?.current?.requestSubmit();
    }
  };

  const setMobileActionBtn = useCallback(
    (newState = {}) => {
      const updatedState = {
        ...mobileActionBtnProps,
        ...newState
      };
      if (!isEqual(updatedState, mobileActionBtnProps)) {
        setMobileActionBtnProps(updatedState);
      }
    },
    [mobileActionBtnProps, setMobileActionBtnProps]
  );

  const updateStageFromAPI = (kyc: IMerchantKycInfo) => {
    if (!isFirstRequest) {
      return;
    }
    setIsFirstRequest(false);
    const complianceRequirements = kyc?.compliance?.requirements;
    const complianceFeedback = kyc?.compliance?.feedback || {};
    setFlaggedForm(Object.values(complianceRequirements || {}).some(e => e?.status === 'flagged'));
    if (complianceRequirements?.business_profile?.status === 'flagged') {
      setAllowBackBtn(true);
    } else {
      setAllowBackBtn(false);
    }

    if (complianceRequirements) {
      setKycFeedback({
        ...(complianceRequirements?.business_profile?.status === 'flagged' && {
          businessProfile: complianceFeedback?.business_profile?.[0]?.note
        }),
        ...(complianceRequirements?.documents?.status === 'flagged' && {
          documents: complianceFeedback?.documents?.[0]?.note
        }),
        ...(complianceRequirements?.representatives?.status === 'flagged' && {
          representatives: complianceFeedback?.representatives?.[0]?.note
        }),
        ...(complianceRequirements?.settlement_accounts?.status === 'flagged' && {
          settlementAccounts: complianceFeedback?.settlement_accounts?.[0]?.note
        })
      });
    }

    if (!complianceRequirements || complianceRequirements?.business_type?.status !== 'submitted') {
      setStage(1);
    } else if (complianceRequirements?.business_profile?.status !== 'submitted') {
      setStage(2);
    } else if (complianceRequirements?.documents?.status !== 'submitted') {
      setStage(3);
    } else if (complianceRequirements?.representatives && complianceRequirements.representatives?.status !== 'submitted') {
      setStage(4);
    } else if (complianceRequirements?.settlement_accounts?.status !== 'submitted') {
      if (complianceRequirements?.representatives) {
        setStage(5);
      } else {
        setStage(4);
      }
    } else {
      setStage(1);
    }
  };

  const { isLoading, refetch: refetchMerchantKYC } = KYCServices.useFetchMerchantKYC({
    queryKey: ['merchants/info', defaultMerchant?.email, stage],
    bannerLevel: true,
    onSuccess: ({ data }: { data: IMerchantKycInfo }) => {
      setSubmittedKyc(data);
      setInitialRender(false);
      if (['verified', 'ready'].includes(data?.compliance?.status) && !flaggedForm && initialRender) {
        return history.push('/dashboard/home');
      }
      if (flaggedForm && data?.compliance?.status === 'ready') {
        setStage(selected === 'individual' ? 5 : 6);
      }

      if (flaggedForm && data?.compliance?.status === 'responded') {
        setStage(selected === 'individual' ? 5 : 6);
      }

      if (data?.business_type === 'registered_business_sme' || data.business_type === 'registered_business_non_sme') {
        setSelected('Registered');
        setRegisteredBusinessType(data?.business_type);
        return updateStageFromAPI(data);
      }
      if (data?.business_type) {
        setSelected(data?.business_type);
        return updateStageFromAPI(data);
      }
      return null;
    }
  });

  const { data: kycSchemas, isLoading: schemaLoading } = KYCServices.useFetchKYCSchema({
    queryKey: ['KYCSchema', selectedCountry],
    bannerLevel: true,
    country: selectedCountry
  });

  const { data: countryIdsOptions } = KYCServices.useFetchCountryIds({
    queryKey: ['CountryId', selectedCountry],
    bannerLevel: true,
    country: selectedCountry,
    isIdentity: true,
    isIndividual: true
  });

  const schema = isEnhancedKycEnabled ? kycSchemas?.data || kycSchema[merchantCountry] : kycSchema[merchantCountry];
  const countryIds =
    countryIdsOptions?.data?.map(country => ({
      value: country.code,
      label: country.name,
      regex: country.regex,
      country: country.country
    })) || [];

  const resetKycFunction = KYCServices.useResetKyc({
    bannerLevel: true,
    onSuccess: () => {
      setSubmittedKyc(null);
      handleStageChange(1);
    }
  });

  const businessTypeMutation = KYCServices.useBusinessType({
    bannerLevel: true,
    onSuccess: () => {
      actions.track('Onboarding initiated');
      setSubmittedKyc(null);
      handleStageChange(stage + 1);
      return setState({ ...state, isLoading: false });
    },
    onError: e => {
      const error = (e as { response?: { data?: { message?: string } } })?.response?.data;
      logError(e);
      const errorMessage = () => {
        if (error?.message) {
          return error.message;
        }
        return 'There seems to be an issue registering your business type';
      };
      setState({
        ...state,
        isLoading: false,
        feedback: { ...state.feedback, message: `${errorMessage()}`, visible: true }
      });
    }
  });

  const calculateCompletion = () => {
    if (stage === 6 && selected === 'Registered') {
      return true;
    }
    if (stage === 6 && selected === 'ngo') {
      return true;
    }
    if (stage === 5 && selected === 'individual') {
      return true;
    }
    return false;
  };

  const isCompleted = calculateCompletion();

  const showFeedback = () => {
    const { feedback } = state;
    return feedback?.visible ? <Feedback type={feedback.type} message={feedback.message} /> : '';
  };

  const getButtonDisabledState = () => {
    const btnDisabled = !selected || state.isLoading || (selected === 'Registered' && !registeredBusinessType);
    if (stage === 1 && btnDisabled !== mobileActionBtnProps.disabled) {
      setMobileActionBtn({ disabled: btnDisabled });
    }
    return btnDisabled;
  };

  const showBackButton = () => {
    if (stage > 1 && stage < 5 && selected === 'individual' && (!flaggedForm || (flaggedForm && allowBackButton))) {
      return true;
    }
    if (stage > 1 && stage < 6 && (selected === 'ngo' || selected === 'Registered') && (!flaggedForm || (flaggedForm && allowBackButton))) {
      return true;
    }
    return false;
  };

  const getKycStage = (stage: number, selected: string) => {
    switch (stage) {
      case 2:
        return 'business_profile';
      case 3:
        return selected === 'individual' ? 'documents' : null;
      case 4:
        return selected === 'individual' ? 'settlement_account' : 'representatives';
      case 5:
        return 'settlement_account';
      default:
        return null;
    }
  };

  return (
    <div className="auth__body">
      <div className="kyc__progress__section">
        <Link to="/">
          <img alt="Korapay Logo" src={ImgKorapayLogo} srcSet={ImgKorapayLogoSvg} />
        </Link>
        <h2>
          Set up your
          <br />
          business account
        </h2>
        <p>
          Activate your business payments in
          <br />
          quick and simple steps.
        </p>

        <div className="progress-section">
          {selected === 'individual' && (
            <ProgressBar number={stage} stages={['Business Type', 'Business Profile', 'Documents', 'Bank Details']} />
          )}
          {selected === 'Registered' && (
            <ProgressBar number={stage} stages={['Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']} />
          )}

          {selected === 'ngo' && (
            <ProgressBar number={stage} stages={['Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']} />
          )}
        </div>

        <button className="later" type="button" onClick={() => setShowThisLaterModal(true)}>
          <img alt="Later" src={LaterSvg} />
          <p>I’ll do this later</p>
        </button>
      </div>

      <div>
        {selected === 'individual' && (
          <PageTitle number={stage} titles={['Select Business Type', 'Business Profile', 'Documents', 'Bank Details']} />
        )}
        {selected === 'Registered' && (
          <PageTitle number={stage} titles={['Select Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']} />
        )}

        {selected === 'ngo' && (
          <PageTitle number={stage} titles={['Select Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']} />
        )}
      </div>

      <div className={`kyc__progress__section-mobile ${stage > 1 && 'hide-top'}`}>
        <Link to="/">
          <img alt="Korapay Logo" src={ImgKorapayLogo} srcSet={ImgKorapayLogoSvg} />
        </Link>
        <h2>
          Set up your business <br />
          account
        </h2>
        <p>
          Activate your business payments in quick <br />
          and simple steps.
        </p>
      </div>

      {!isCompleted && (
        <div className="mobile-bottom-button">
          <button className="later-btn" type="button" onClick={() => setShowThisLaterModal(true)}>
            <img alt="Later" src={LaterSvg} />
            <span className="later-text">I’ll do this later</span>
          </button>

          <button
            onClick={handleMobileSubmit}
            className="btn-kpy mt-1 continue-btn"
            type="submit"
            disabled={mobileActionBtnProps.disabled}
            id={`${getKycStage(stage, selected)}-btn`}
          >
            {mobileActionBtnProps.isLoading ? <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" /> : null}
            {mobileActionBtnProps.isLoading ? (
              <span style={{ marginLeft: '0.5rem' }}>Submitting...</span>
            ) : (
              <span>
                <span>Continue</span>
                <img className="arrow-right-icon" alt="" src={ArrowRightSvg} />
              </span>
            )}
          </button>
        </div>
      )}

      {isLoading || schemaLoading ? (
        <KpyLoader message={state.isLoadingMessage} />
      ) : (
        <div className="kyc__section">
          {showBackButton() && (
            <button
              type="button"
              onClick={() => {
                if (flaggedForm && stage === 2) {
                  setShowModal(true);
                } else {
                  handleStageChange(stage - 1);
                }
              }}
              className="back-button"
            >
              <img src={ImgBackArrowSvg} alt="" /> <span>&nbsp;Back</span>
            </button>
          )}

          <div className="kyc-content">
            {stage > 1 && !isCompleted && (
              <div className="steps-mobile-position">
                {selected === 'individual' && (
                  <MobileProgressBar number={stage} stages={['Business Type', 'Business Profile', 'Documents', 'Bank Details']} />
                )}
                {selected === 'Registered' && (
                  <MobileProgressBar
                    number={stage}
                    stages={['Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']}
                  />
                )}

                {selected === 'ngo' && (
                  <MobileProgressBar
                    number={stage}
                    stages={['Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']}
                  />
                )}
              </div>
            )}

            {stage === 1 && (
              <div className={`kyc__form ${stage === 1 && 'kyc-form-position'}`}>
                <div>
                  {selected === 'individual' && (
                    <MobileProgressBar number={stage} stages={['Business Type', 'Business Profile', 'Documents', 'Bank Details']} />
                  )}
                  {selected === 'Registered' && (
                    <MobileProgressBar
                      number={stage}
                      stages={['Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']}
                    />
                  )}

                  {selected === 'ngo' && (
                    <MobileProgressBar
                      number={stage}
                      stages={['Business Type', 'Business Profile', 'Documents', 'Representative', 'Bank Details']}
                    />
                  )}
                </div>
                <Headings kycStage="index" />
                {showFeedback()}
                <button
                  ref={ref1}
                  onClick={() => {
                    setSelected('individual');
                    setStage(1);
                    setRegisteredBusinessType('');
                  }}
                  type="button"
                  className={`business-type-btn ${selected === 'individual' ? 'selectedType' : 'inactiveType'}`}
                >
                  <div className="business-type">
                    <img alt="Individual" src={IndividualSvg} />
                    <p>Individual</p>
                  </div>
                  <div>
                    <Tooltip title="For Individuals" />
                  </div>
                </button>

                <button
                  ref={ref2}
                  onClick={() => {
                    setSelected('ngo');
                    setStage(1);
                    setRegisteredBusinessType('');
                  }}
                  type="button"
                  className={`business-type-btn ${selected === 'ngo' ? 'selectedType' : 'inactiveType'}`}
                >
                  <div className="business-type">
                    <img alt="Individual" src={NgoSvg} />
                    <p>{isMobile ? 'NGO' : 'Non-Governmental Organisation (NGO)'}</p>
                  </div>
                  <div>
                    <Tooltip title="For NGOs" />
                  </div>
                </button>

                <button
                  ref={ref3}
                  onClick={() => {
                    setSelected('Registered');
                    setStage(1);
                  }}
                  type="button"
                  className={`business-type-btn mb-0 ${selected === 'Registered' ? 'selectedType' : 'inactiveType'}`}
                >
                  <div className="business-type">
                    <img alt="Individual" src={RegisteredSvg} />
                    <p>Registered Business</p>
                  </div>
                  <div>
                    <Tooltip title="For Registered Businesses" />
                  </div>
                </button>
                {selected === 'Registered' && (
                  <div className="registered-options">
                    <div>
                      <label
                        htmlFor="radio_reg_business_sme"
                        className={registeredBusinessType === 'registered_business_sme' ? 'selected' : ''}
                      >
                        Registered SME
                      </label>
                      <input
                        id="radio_reg_business_sme"
                        type="radio"
                        value="registered_business_sme"
                        checked={registeredBusinessType === 'registered_business_sme' && true}
                        name="registered-business"
                        onChange={e => {
                          setRegisteredBusinessType(e.target.value);
                        }}
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="radio_reg_business_non_sme"
                        className={registeredBusinessType === 'registered_business_non_sme' ? 'selected' : ''}
                      >
                        Registered Non-SME
                      </label>
                      <input
                        id="radio_reg_business_non_sme"
                        type="radio"
                        value="registered_business_non_sme"
                        checked={registeredBusinessType === 'registered_business_non_sme' && true}
                        name="registered-business"
                        onChange={e => {
                          setRegisteredBusinessType(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                )}

                <button
                  onClick={() => handleSubmit()}
                  className="btn-kpy --full-blue mt-3 sm-hide"
                  type="submit"
                  disabled={getButtonDisabledState()}
                  id={getKycStage(stage, selected)}
                >
                  {state.isLoading ? <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" /> : null}
                  {state.isLoading ? <span style={{ marginLeft: '0.5rem' }}>Submitting...</span> : 'Continue'}
                </button>
              </div>
            )}

            {showModal && (
              <Modal
                close={() => setShowModal(false)}
                visible={showModal}
                themeColor="red"
                size="sm"
                heading="Warning"
                description="Your previously submitted data will be cleared. Do you want to continue?"
                showButtons
                secondButtonAction={() => {
                  resetKycFunction.mutate({});
                  handleStageChange(1);
                  clearFeedback('businessProfile');
                }}
                secondButtonActionIsTerminal
              />
            )}

            {showThisLaterModal && (
              <Modal
                close={() => setShowThisLaterModal(false)}
                visible={showThisLaterModal}
                themeColor="red"
                size="sm"
                heading="Warning"
                description="If you leave this page, your unsubmitted data will be cleared and you would start again from where you left. Do you want to continue?"
                showButtons
                secondButtonAction={() => {
                  actions.track('Onboarding skipped');
                  history.push('/dashboard');
                }}
                secondButtonActionIsTerminal
              />
            )}

            {stage === 2 && selected === 'individual' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="business_profile"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('businessProfile');
                }}
                schema={schema?.individual?.business_profile}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.businessProfile}
                submittedProfile={submittedKyc?.details?.business_profile}
                dataClearFeedback={allowBackButton}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 3 && selected === 'individual' && countryIds?.length > 0 && (
              <Documents
                updateStage={num => {
                  incrementStage(num);
                  clearFeedback('documents');
                }}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.documents}
                submittedDocuments={submittedKyc?.details?.documents}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
                merchantCountry={merchantCountry}
                validIdOptions={countryIds}
              />
            )}

            {stage === 3 && selected === 'individual' && countryIds?.length === 0 && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="documents"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('documents');
                }}
                schema={schema.individual.documents}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.documents}
                submittedProfile={submittedKyc?.details?.documents}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 4 && selected === 'individual' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="settlement_account"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('settlement_account');
                }}
                schema={schema.individual.settlement_account}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback?.settlementAccounts}
                submittedProfile={submittedKyc?.details?.settlement_accounts}
                bvnInformation={submittedKyc?.details?.bvn?.number}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 2 && selected === 'ngo' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="business_profile"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('businessProfile');
                }}
                schema={schema.non_governmental_organization.business_profile}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.businessProfile}
                submittedProfile={submittedKyc?.details?.business_profile}
                dataClearFeedback={allowBackButton}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 3 && selected === 'ngo' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="documents"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('documents');
                }}
                schema={schema.non_governmental_organization.documents}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.documents}
                submittedProfile={submittedKyc?.details?.documents}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 4 && selected === 'ngo' && (
              <Representative
                merchantCountry={merchantCountry}
                kycStage="representatives"
                updateStage={num => {
                  incrementStage(num);
                  clearFeedback('representatives');
                }}
                registeredType={registeredBusinessType}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.representatives}
                submittedRepresentatives={submittedKyc?.details?.representatives}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
                validIdOptions={countryIds}
              />
            )}

            {stage === 5 && selected === 'ngo' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="settlement_account"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('settlement_account');
                }}
                schema={schema.non_governmental_organization.settlement_account}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback?.settlementAccounts}
                submittedProfile={submittedKyc?.details?.settlement_accounts}
                bvnInformation={submittedKyc?.details?.bvn?.number}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 2 && selected === 'Registered' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="business_profile"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('businessProfile');
                }}
                schema={schema.registered_business[registeredBusinessType].business_profile}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                dataClearFeedback={allowBackButton}
                readKycFeedback={kycFeedback.businessProfile}
                submittedProfile={submittedKyc?.details?.business_profile}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 3 && selected === 'Registered' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="documents"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('documents');
                }}
                schema={schema.registered_business[registeredBusinessType].documents}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.documents}
                submittedProfile={submittedKyc?.details?.documents}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {stage === 4 && selected === 'Registered' && (
              <Representative
                merchantCountry={merchantCountry}
                kycStage="representatives"
                updateStage={num => {
                  incrementStage(num);
                  clearFeedback('representatives');
                }}
                registeredType={registeredBusinessType}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.representatives}
                submittedRepresentatives={submittedKyc?.details?.representatives}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
                validIdOptions={countryIds}
              />
            )}

            {stage === 5 && selected === 'Registered' && (
              <KycForm
                merchantCountry={merchantCountry}
                kycStage="settlement_account"
                businessType={selected}
                updateStage={(num: number) => {
                  incrementStage(num);
                  clearFeedback('settlement_account');
                }}
                schema={schema.registered_business[registeredBusinessType].settlement_account}
                setMobileActionBtnState={setMobileActionBtn}
                formRef={formRef}
                readKycFeedback={kycFeedback.settlementAccounts}
                submittedProfile={submittedKyc?.details?.settlement_accounts}
                bvnInformation={submittedKyc?.details?.bvn?.number}
                updateSubmittedKyc={data => setSubmittedKyc(data)}
              />
            )}

            {isCompleted && (
              <div className="done-container">
                <KycComplete />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default KycComponent;
