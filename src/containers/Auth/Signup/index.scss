@import 'styles/base/variables';

.website-label {
  display: inline;
}

.bname-text-invalid {
  color: #f32345 !important;
  text-align: right !important;
  display: block;
  margin-top: 5px;
}

// Form validation styles
.input__wrap {
  position: relative;
  input.error-field,
  .phone-input.error-field {
    border-color: #f32345 !important;
  }
}

.error-message-display {
  position: absolute;
  bottom: -1px;
  color: #f32345;
  text-align: left;
  display: block;
  font-size: 12px;


  &.business-type-error {
    bottom: -1.2rem !important;
  }
}
.password-error {
  color: #f32345;
  text-align: left;
  display: block;
  font-size: 12px;
}

// Password validation styles
.password__strength {
  margin-top: 10px;

  p {
    margin-bottom: 5px;
  }
}

// Special handling for the phone input component
.phone-input.error {
  border-color: #f32345 !important;
}

// Password strength indicator colors
.password-criteria {
  &.valid {
    color: #24b314;
  }

  &.invalid {
    color: red;
  }

  &.neutral {
    color: #607d8b;
  }
}

// Loading indicator spacing
.loading-indicator-space {
  margin-left: 0.5rem;
}

// Text alignment utilities
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// Check/cross mark for password validation
.checkmark-icon {
  margin-right: 0.25rem;
}

// Confirm password validation
.confirm-password-validation {
  &.error {
    color: red;
  }

  &.neutral {
    color: #607d8b;
  }
}

// Password field wrapper
.password-field-wrap {
  position: relative;
}

.info__section {
  margin-bottom: 2rem;
  background-color: #f1f6fa;
  border: #0a51bd 1px solid;
  width: 100%;
  padding: 0.2rem 1rem;
  border-radius: 0.5rem;
  color: #414f5f;
  text-align: left;

  div {
    display: flex;
    align-items: center;
    justify-content: left;
  }

  h3 {
    color: #2376f3;
    margin-bottom: 0;
    font-size: 20px;

    @media (max-width: $breakpoint-desktop-sm) {
      font-size: 14px;
    }
  }

  p {
    font-size: 12px;
    margin-bottom: 0 !important;
    margin-left: 1.5rem;
    @media (max-width: $breakpoint-desktop-sm) {
      font-size: 11px;
    }
  }

  svg {
    margin-right: 0.5rem;
  }
}
