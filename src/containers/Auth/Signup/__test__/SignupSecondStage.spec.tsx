import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import type { CountryCode } from 'libphonenumber-js';

import MockIndex from '+mock/MockIndex';

import SignupSecondStageComponent from '../components/SignupSecondStage';

const mockFirstStageData = {
  btype: 'individual',
  industry: 'tech',
  country: '1',
  countryCode: 'NG' as CountryCode,
  registeredBusinessType: ''
};

const MockedSignupSecondStage = () => {
  return (
    <MockIndex>
      <SignupSecondStageComponent firstStageData={mockFirstStageData} />
    </MockIndex>
  );
};

describe('SignupSecondStageComponent', () => {
  test('it is accessible', async () => {
    const { container } = render(<MockedSignupSecondStage />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('renders the personal information fields', () => {
    render(<MockedSignupSecondStage />);

    expect(screen.getByLabelText('first_name')).toBeInTheDocument();
    expect(screen.getByLabelText('last_name')).toBeInTheDocument();
    expect(screen.getByLabelText('email')).toBeInTheDocument();
  });

  test('renders the business information fields', () => {
    render(<MockedSignupSecondStage />);

    expect(screen.getByLabelText('business name')).toBeInTheDocument();
  });

  test('renders the password fields', () => {
    render(<MockedSignupSecondStage />);

    expect(screen.getByLabelText('password')).toBeInTheDocument();
    expect(screen.getByLabelText('confirm_password')).toBeInTheDocument();
  });

  test('renders the terms and conditions checkbox', () => {
    render(<MockedSignupSecondStage />);

    expect(screen.getByLabelText('terms')).toBeInTheDocument();
  });

  test('renders the create account button', () => {
    render(<MockedSignupSecondStage />);

    expect(screen.getByRole('button', { name: 'Create Account' })).toBeInTheDocument();
  });

  test('password validation is shown when password field is focused', async () => {
    render(<MockedSignupSecondStage />);

    const passwordInput = screen.getByLabelText('password');
    await userEvent.click(passwordInput);

    expect(screen.getByText('Password must contain at least')).toBeInTheDocument();
    expect(screen.getByText('8 characters')).toBeInTheDocument();
  });

  test('can fill out the form', async () => {
    render(<MockedSignupSecondStage />);

    await userEvent.type(screen.getByLabelText('first_name'), 'John');
    await userEvent.type(screen.getByLabelText('last_name'), 'Doe');
    await userEvent.type(screen.getByLabelText('email'), '<EMAIL>');
    await userEvent.type(screen.getByLabelText('business name'), 'Acme Inc');
    await userEvent.type(screen.getByLabelText('password'), 'Password123!');
    await userEvent.type(screen.getByLabelText('confirm_password'), 'Password123!');
    await userEvent.click(screen.getByLabelText('terms'));

    expect(screen.getByLabelText('first_name')).toHaveValue('John');
    expect(screen.getByLabelText('last_name')).toHaveValue('Doe');
    expect(screen.getByLabelText('email')).toHaveValue('<EMAIL>');
    expect(screen.getByLabelText('business name')).toHaveValue('Acme Inc');
    expect(screen.getByLabelText('password')).toHaveValue('Password123!');
    expect(screen.getByLabelText('confirm_password')).toHaveValue('Password123!');
    expect(screen.getByLabelText('terms')).toBeChecked();
  });
});
