import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import SignupComponent from '../index';

const MockedSignUp = () => {
  return (
    <MockIndex>
      <SignupComponent />
    </MockIndex>
  );
};

describe('Signup Component', () => {
  describe('Accessibility', () => {
    test('Signup page is accessible', async () => {
      const { container } = render(<MockedSignUp />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('Checks for accessibility compliance with proper form labeling', () => {
      render(<MockedSignUp />);
      expect(screen.getByText('Country of Business Registration')).toBeInTheDocument();
      expect(screen.getByText('Industry')).toBeInTheDocument();
      expect(screen.getByText('Business Type')).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 3 })).toBeInTheDocument();
    });
  });

  describe('Initial Render', () => {
    test('Renders the signup page with correct header content', () => {
      render(<MockedSignUp />);
      expect(screen.getByAltText('Korapay Logo')).toBeInTheDocument();
      expect(screen.getByText(/Sign up your/)).toBeInTheDocument();
      expect(screen.getByText(/business on Kora/)).toBeInTheDocument();
      expect(screen.getByText('Unlock powerful tools for your business')).toBeInTheDocument();
    });

    test('Renders the first stage form initially', () => {
      render(<MockedSignUp />);

      expect(screen.getByText('Country of Business Registration')).toBeInTheDocument();
      expect(screen.getByText('Loading country selector...')).toBeInTheDocument();
      expect(screen.getByText('Loading industry selector...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
      expect(screen.queryByText('Back')).not.toBeInTheDocument();
    });

    test('Has login link for existing users', () => {
      render(<MockedSignUp />);
      const loginLink = screen.getByText('Log In');
      expect(loginLink).toBeInTheDocument();
      expect(loginLink).toHaveAttribute('href', '/auth/login');
    });

    test('Business type buttons are disabled initially', () => {
      render(<MockedSignUp />);

      const individualBtn = screen.getByRole('button', { name: /individual/i });
      const ngoBtn = screen.getByRole('button', { name: /non-governmental organisation/i });
      const registeredBusinessBtn = screen.getByRole('button', { name: /registered business/i });

      expect(individualBtn).toBeDisabled();
      expect(ngoBtn).toBeDisabled();
      expect(registeredBusinessBtn).toBeDisabled();
    });
  });

  describe('Stage Navigation', () => {
    test('Shows back button when navigating to second stage', () => {
      render(<MockedSignUp />);
      expect(screen.queryByText('Back')).not.toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
    });

    test('Maintains form state when navigating between stages', () => {
      render(<MockedSignUp />);
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
    });
  });

  describe('Business Type Selection', () => {
    test('Displays all business type options', () => {
      render(<MockedSignUp />);

      expect(screen.getByText('Individual')).toBeInTheDocument();
      expect(screen.getByText(/Non-Governmental Organisation/i)).toBeInTheDocument();
      expect(screen.getByText('Registered Business')).toBeInTheDocument();
    });

    test('Shows registered business sub-options when selected', () => {
      render(<MockedSignUp />);
      expect(screen.getByRole('button', { name: /registered business/i })).toBeInTheDocument();
    });

    test('Business type tooltips are present', () => {
      render(<MockedSignUp />);
      const businessTypeButtons = screen
        .getAllByRole('button')
        .filter(
          button =>
            button.textContent?.includes('Individual') ||
            button.textContent?.includes('NGO') ||
            button.textContent?.includes('Registered Business')
        );

      expect(businessTypeButtons.length).toBe(3);
    });
  });

  describe('Form Validation', () => {
    test('Displays validation errors for empty required fields', async () => {
      const user = userEvent.setup();
      render(<MockedSignUp />);

      const continueButton = screen.getByRole('button', { name: 'Continue' });
      await user.click(continueButton);
      expect(continueButton).toBeInTheDocument();
    });

    test('Validates registered business type selection', () => {
      render(<MockedSignUp />);
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
    });

    test('Shows validation errors inline for business type', () => {
      render(<MockedSignUp />);
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
      expect(screen.getByText('Country of Business Registration')).toBeInTheDocument();
    });
  });

  describe('External Links', () => {
    test('Korapay logo links to correct external URL', () => {
      render(<MockedSignUp />);

      const logoLink = screen.getByRole('link', { name: /korapay logo/i });
      expect(logoLink).toHaveAttribute('href', 'https://www.korahq.com');
      expect(logoLink).toHaveAttribute('target', '_blank');
      expect(logoLink).toHaveAttribute('rel', 'noopener noreferrer');
    });

    test('External links open in new tab for security', () => {
      render(<MockedSignUp />);

      const externalLinks = screen.getAllByRole('link');
      const korapayLink = externalLinks.find(link => link.getAttribute('href') === 'https://www.korahq.com');

      expect(korapayLink).toHaveAttribute('target', '_blank');
      expect(korapayLink).toHaveAttribute('rel', 'noopener noreferrer');
    });
  });

  describe('Responsive Design', () => {
    test('Renders correctly on mobile viewport', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500
      });

      render(<MockedSignUp />);
      expect(screen.getByText('NGO')).toBeInTheDocument();
    });

    test('Renders correctly on desktop viewport', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200
      });

      render(<MockedSignUp />);
      expect(screen.getByText(/Non-Governmental Organisation/i)).toBeInTheDocument();
    });
  });

  describe('Performance and Loading States', () => {
    test('Shows loading states for async components', () => {
      render(<MockedSignUp />);

      expect(screen.getByText('Loading country selector...')).toBeInTheDocument();
      expect(screen.getByText('Loading industry selector...')).toBeInTheDocument();
    });

    test('Handles component lazy loading correctly', async () => {
      render(<MockedSignUp />);

      await waitFor(() => {
        expect(screen.queryByText('Loading country selector...')).not.toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading industry selector...')).not.toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });
    });
  });

  describe('Error Boundaries', () => {
    test('Gracefully handles API failures', () => {
      render(<MockedSignUp />);
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
    });
  });

  describe('Data Persistence', () => {
    test('Form data structure supports persistence', async () => {
      render(<MockedSignUp />);
      await waitFor(() => {
        expect(screen.queryByText('Loading country selector...')).not.toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading industry selector...')).not.toBeInTheDocument();
      });
      await waitFor(() => {
        expect(screen.getByLabelText(/country/i)).toBeInTheDocument();
      });

      expect(screen.getByLabelText(/industry/i)).toBeInTheDocument();
    });

    test('Basic form data persists when component rerenders', async () => {
      const user = userEvent.setup();
      render(<MockedSignUp />);
      await waitFor(() => {
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });

      const countrySelect = screen.getByLabelText(/country/i);
      const industrySelect = screen.getByLabelText(/industry/i);

      await user.click(countrySelect);
      await user.click(industrySelect);

      const individualButton = screen.getByRole('button', { name: /individual/i });
      await user.click(individualButton);

      expect(countrySelect).toBeInTheDocument();
      expect(industrySelect).toBeInTheDocument();
    });

    test('State management functions exist for data persistence', async () => {
      const user = userEvent.setup();
      render(<MockedSignUp />);

      expect(screen.getByText(/Follow the prompt/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
      await waitFor(() => {
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });

      const countrySelect = screen.getByLabelText(/country/i);
      await user.click(countrySelect);
      expect(countrySelect).toBeInTheDocument();
    });

    test('Form validation maintains state across interactions', async () => {
      const user = userEvent.setup();
      render(<MockedSignUp />);
      await waitFor(() => {
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });

      const countrySelect = screen.getByLabelText(/country/i);
      const industrySelect = screen.getByLabelText(/industry/i);

      await user.click(countrySelect);
      await user.click(industrySelect);

      const individualButton = screen.getByRole('button', { name: /individual/i });
      await user.click(individualButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
      });

      expect(countrySelect).toBeInTheDocument();
      expect(industrySelect).toBeInTheDocument();
    });
  });

  describe('Security and Privacy', () => {
    test('External links have proper security attributes', () => {
      render(<MockedSignUp />);

      const externalLinks = screen.getAllByRole('link').filter(link => link.getAttribute('target') === '_blank');

      externalLinks.forEach(link => {
        expect(link).toHaveAttribute('rel', 'noopener noreferrer');
      });
    });

    test('Form inputs have proper autocomplete attributes', async () => {
      render(<MockedSignUp />);

      await waitFor(() => {
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });

      const countryInput = screen.getByLabelText(/country/i);
      const industryInput = screen.getByLabelText(/industry/i);

      expect(countryInput).toHaveAttribute('autocomplete', 'off');
      expect(industryInput).toHaveAttribute('autocomplete', 'off');
    });
  });

  describe('User Experience', () => {
    test('Continue button is prominently displayed', () => {
      render(<MockedSignUp />);

      const continueButton = screen.getByRole('button', { name: 'Continue' });
      expect(continueButton).toBeInTheDocument();
      expect(continueButton).toHaveClass('btn-kpy', '--full-blue');
    });

    test('Form has clear visual hierarchy', () => {
      render(<MockedSignUp />);
      expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 3 })).toBeInTheDocument();
      expect(screen.getByText('Create your business account effortlessly.')).toBeInTheDocument();
    });

    test('Help text and instructions are clear', () => {
      render(<MockedSignUp />);

      expect(screen.getByText(/Follow the prompt, fill out the form below/)).toBeInTheDocument();
      expect(screen.getByText(/Already have an account/)).toBeInTheDocument();
    });
  });

  describe('Form Validation and Data Submission - Advanced', () => {
    describe('Button State Management', () => {
      test('Business type buttons are disabled when no country is selected', () => {
        render(<MockedSignUp />);

        const individualBtn = screen.getByRole('button', { name: /individual/i });
        const ngoBtn = screen.getByRole('button', { name: /non-governmental organisation/i });
        const registeredBusinessBtn = screen.getByRole('button', { name: /registered business/i });

        expect(individualBtn).toBeDisabled();
        expect(ngoBtn).toBeDisabled();
        expect(registeredBusinessBtn).toBeDisabled();
      });

      test('Continue button is enabled but form validation prevents invalid submission', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);

        const continueButton = screen.getByRole('button', { name: 'Continue' });
        expect(continueButton).not.toBeDisabled();
        await user.click(continueButton);
        expect(screen.queryByText('Back')).not.toBeInTheDocument();
        expect(continueButton).toBeInTheDocument();
      });

      test('Business type buttons behavior with form interaction', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);
        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });
        const individualBtn = screen.getByRole('button', { name: /individual/i });
        const ngoBtn = screen.getByRole('button', { name: /non-governmental organisation/i });
        const registeredBusinessBtn = screen.getByRole('button', { name: /registered business/i });
        expect(individualBtn).toBeInTheDocument();
        expect(ngoBtn).toBeInTheDocument();
        expect(registeredBusinessBtn).toBeInTheDocument();
        const countrySelect = screen.getByLabelText(/country/i);
        await user.click(countrySelect);
        expect(individualBtn).toBeInTheDocument();
      });
    });

    describe('Form Validation Behavior', () => {
      test('Form prevents submission with incomplete data', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);

        const continueButton = screen.getByRole('button', { name: 'Continue' });
        await user.click(continueButton);
        expect(screen.queryByText('Back')).not.toBeInTheDocument();
        expect(screen.getByText('Country of Business Registration')).toBeInTheDocument();
        expect(screen.getByText('Industry')).toBeInTheDocument();
        expect(screen.getByText('Business Type')).toBeInTheDocument();
      });

      test('Form validation activates after attempting submission', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);
        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });

        const continueButton = screen.getByRole('button', { name: 'Continue' });
        await user.click(continueButton);
        expect(screen.getByLabelText(/country/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/industry/i)).toBeInTheDocument();
      });

      test('Registered business shows sub-type options', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);
        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });
        const registeredBusinessBtn = screen.getByRole('button', { name: /registered business/i });
        await user.click(registeredBusinessBtn);
        expect(registeredBusinessBtn).toBeInTheDocument();

        const continueButton = screen.getByRole('button', { name: 'Continue' });
        expect(continueButton).toBeInTheDocument();

        await user.click(continueButton);
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });
    });

    describe('Successful Form Progression', () => {
      test('Form interaction with individual selection', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);

        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });
        const countrySelect = screen.getByLabelText(/country/i);
        await user.click(countrySelect);

        const industrySelect = screen.getByLabelText(/industry/i);
        await user.click(industrySelect);

        const individualBtn = screen.getByRole('button', { name: /individual/i });
        await user.click(individualBtn);

        const continueButton = screen.getByRole('button', { name: 'Continue' });
        await user.click(continueButton);
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });

      test('Form interaction with registered business selection', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);
        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });

        const countrySelect = screen.getByLabelText(/country/i);
        await user.click(countrySelect);

        const industrySelect = screen.getByLabelText(/industry/i);
        await user.click(industrySelect);

        const registeredBusinessBtn = screen.getByRole('button', { name: /registered business/i });
        await user.click(registeredBusinessBtn);
        const continueButton = screen.getByRole('button', { name: 'Continue' });
        await user.click(continueButton);
        expect(registeredBusinessBtn).toBeInTheDocument();
      });
    });

    describe('Data Structure and Endpoint Validation', () => {
      test('Validates first stage data structure matches expected API format', () => {
        render(<MockedSignUp />);
        expect(screen.getByLabelText(/country/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/industry/i)).toBeInTheDocument();
        expect(screen.getByText('Individual')).toBeInTheDocument();
        expect(screen.getByText(/Non-Governmental Organisation/i)).toBeInTheDocument();
        expect(screen.getByText('Registered Business')).toBeInTheDocument();
      });

      test('Validates complete signup data structure for API endpoint', () => {
        render(<MockedSignUp />);
        const expectedDataFields = [
          'email',
          'first_name',
          'last_name',
          'bname',
          'btype',
          'industry',
          'phone_number',
          'country',
          'phone_country_code',
          'password',
          'confirm_password',
          'sra',
          'tandc'
        ];

        const firstStageFields = ['btype', 'industry', 'country'];

        firstStageFields.forEach(field => {
          expect(expectedDataFields).toContain(field);
        });

        expect(expectedDataFields.length).toBeGreaterThan(firstStageFields.length);
      });

      test('Form data preparation follows proper validation rules', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);
        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });
        const continueButton = screen.getByRole('button', { name: 'Continue' });
        await user.click(continueButton);
        expect(screen.queryByText('Back')).not.toBeInTheDocument();
        const countrySelect = screen.getByLabelText(/country/i);
        await user.click(countrySelect);
        await user.type(countrySelect, 'Nigeria');

        await user.click(continueButton);
        expect(screen.queryByText('Back')).not.toBeInTheDocument();
      });
    });

    describe('Form State Management', () => {
      test('Form maintains state consistency during validation cycles', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);

        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });

        const countrySelect = screen.getByLabelText(/country/i);
        await user.click(countrySelect);

        const continueButton = screen.getByRole('button', { name: 'Continue' });
        await user.click(continueButton);

        expect(countrySelect).toBeInTheDocument();
        const industrySelect = screen.getByLabelText(/industry/i);
        await user.click(industrySelect);

        const individualBtn = screen.getByRole('button', { name: /individual/i });
        await user.click(individualBtn);

        await user.click(continueButton);
        expect(screen.getByText('Select Country')).toBeInTheDocument();
      });

      test('Business type selection interaction test', async () => {
        const user = userEvent.setup();
        render(<MockedSignUp />);

        await waitFor(() => {
          expect(screen.getByText('Select Country')).toBeInTheDocument();
        });
        const individualBtn = screen.getByRole('button', { name: /individual/i });
        const registeredBusinessBtn = screen.getByRole('button', { name: /registered business/i });

        expect(individualBtn).toBeInTheDocument();
        expect(registeredBusinessBtn).toBeInTheDocument();

        await user.click(individualBtn);
        expect(individualBtn).toBeInTheDocument();

        await user.click(registeredBusinessBtn);
        expect(registeredBusinessBtn).toBeInTheDocument();
      });
    });
  });
});
