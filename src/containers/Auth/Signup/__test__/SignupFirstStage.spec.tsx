import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import SignupFirstStageComponent from '../components/SignupFirstStage';

const MockedSignupFirstStage = () => {
  return (
    <MockIndex>
      <SignupFirstStageComponent onSubmit={vi.fn()} />
    </MockIndex>
  );
};

describe('SignupFirstStageComponent', () => {
  test('it is accessible', async () => {
    const { container } = render(<MockedSignupFirstStage />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('renders the country selection field', () => {
    render(<MockedSignupFirstStage />);
    expect(screen.getByText('Country of Business Registration')).toBeInTheDocument();
  });

  test('renders the industry selection field', () => {
    render(<MockedSignupFirstStage />);
    expect(screen.getByText('Loading industry selector...')).toBeInTheDocument();
  });

  test('renders business type options', () => {
    render(<MockedSignupFirstStage />);
    expect(screen.getByText('Individual')).toBeInTheDocument();
    expect(screen.getByText(/Non-Governmental/)).toBeInTheDocument();
    expect(screen.getByText('Registered Business')).toBeInTheDocument();
  });

  test('business type options are disabled when no country is selected', () => {
    render(<MockedSignupFirstStage />);
    const individualButton = screen.getByRole('button', { name: 'Individual' });
    expect(individualButton).toBeDisabled();
  });

  test('continue button is present', () => {
    render(<MockedSignupFirstStage />);
    expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
  });
});
