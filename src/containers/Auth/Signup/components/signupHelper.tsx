import { FormikErrors } from 'formik';

import { ISignupData, SignupFirstStageDataType } from '+types';

export const businessTypes = {
  individual: 'Individual',
  ngo: 'Non-Governmental Organization (NGO)',
  registered_business_non_sme: 'Registered Business (Non-SME)',
  registered_business_sme: 'Registered Business (SME)'
};

export const validate = (values: ISignupData) => {
  const errors = {} as FormikErrors<ISignupData>;
  if (values.first_name.length < 2) errors.first_name = 'First Name is invalid';
  if (values.last_name.length < 2) errors.last_name = 'Last Name is invalid';
  if (values.bname.length < 2) errors.bname = 'Business Name is invalid';
  if (values.bname.length < 2) errors.bname = 'Business Name is invalid';
  if (!values.btype || values.btype.length < 2) errors.btype = 'Business type is invalid';
  if (values.email.length < 2 || !values.email) errors.email = 'Email address is invalid';
  if (!values.country) errors.country = 'Country is invalid';
  if (!values.phone_country_code) errors.phone_country_code = 'Country code is invalid';
  if (values.confirm_password !== values.password) errors.confirm_password = 'Must match your password';
  if (!values.password) errors.password = 'Password is invalid';
  if (!values.tandc) errors.tandc = 'Terms and Conditions are required';
  if (values.sra.length < 3) errors.sra = 'SRA is invalid';
  return errors;
};

export const validateFirstStage = (values: SignupFirstStageDataType, registeredBusinessType?: string) => {
  const errors = {} as FormikErrors<SignupFirstStageDataType>;
  if (!values.btype) errors.btype = 'Business type is invalid';
  if (!values.country) errors.country = 'Country is invalid';
  if (!values.industry) errors.industry = 'Industry is invalid';

  // Additional validation for registered business
  if (values.btype === 'registered_business' && (!registeredBusinessType || registeredBusinessType === '')) {
    errors.btype = 'Please select a registered business type (SME or Non-SME)';
  }

  return errors;
};
