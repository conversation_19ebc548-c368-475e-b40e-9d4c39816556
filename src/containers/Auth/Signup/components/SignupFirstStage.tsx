/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-props-no-spreading */
import React, { lazy, Suspense, useCallback, useEffect, useRef, useState } from 'react';
import { Field, Form, Formik, FormikErrors, FormikProps, FormikTouched } from 'formik';
import { CountryCode } from 'libphonenumber-js';

import Icon from '+containers/Dashboard/Shared/Icons';
import Feedback from '+containers/Shared/Feedback';
import useIndustries from '+hooks/useIndustries';
import { KYCServices } from '+services/kyc-services';
import { UtilServices } from '+services/util-services';
import { BusinessTypes, OptionType, SignupFirstStageDataType } from '+types';

import { validateFirstStage } from './signupHelper';

import 'react-phone-number-input/style.css';
import './index.scss';

const CustomReactSelect = lazy(() => import('+shared/CustomReactSelect'));

const SignupFirstStageComponent = ({
  onSubmit,
  initialData
}: {
  onSubmit: (values: SignupFirstStageDataType) => void;
  initialData?: SignupFirstStageDataType | null;
}) => {
  const formikRef = useRef<FormikProps<SignupFirstStageDataType> | null>(null);
  const [countries, setCountries] = useState<OptionType[]>([]);

  const [registeredBusinessType, setRegisteredBusinessType] = useState(initialData?.registeredBusinessType || '');
  const [businessTypeFocused, setBusinessTypeFocused] = useState(false);
  const [countryCode, setCountryCode] = useState<CountryCode | undefined>(initialData?.countryCode || undefined);
  const ref1 = useRef(null);
  const ref2 = useRef(null);
  const ref3 = useRef(null);

  const { industries } = useIndustries();

  const { refetch } = UtilServices.useFetchCountries({
    params: { forSignup: true },
    onSuccess: useCallback((data: { data: Array<{ id: number; name: string; iso2: string }> }) => {
      const options = data?.data?.map((item: { id: number; name: string; iso2: string }) => ({
        value: item.id,
        label: item.name,
        isoCode: item.iso2
      }));
      setCountries(options as OptionType[]);
    }, [])
  });

  const isMobile = window?.innerWidth < 720;

  useEffect(() => {
    void refetch();
  }, [refetch]);

  // Set initial country code from saved data
  useEffect(() => {
    if (initialData?.countryCode) {
      setCountryCode(initialData.countryCode);
    }
    if (initialData?.registeredBusinessType) {
      setRegisteredBusinessType(initialData.registeredBusinessType);
    }
  }, [initialData]);

  // When countries are loaded and we have initial country data, ensure country is properly set
  useEffect(() => {
    if (countries.length > 0 && initialData?.country && formikRef.current) {
      const matchingCountry = countries.find(country => country.value === initialData.country);
      if (matchingCountry && !formikRef.current.values.country) {
        void formikRef.current.setFieldValue('country', matchingCountry.value);
        setCountryCode(matchingCountry.isoCode as CountryCode);
      }
    }
  }, [countries, initialData]);

  const handleSubmit = useCallback(
    (values: SignupFirstStageDataType) => {
      const errors = validateFirstStage(values, registeredBusinessType);

      // Touch all fields to show validation errors
      if (Object.keys(errors).length > 0) {
        // Touch all fields to trigger validation styling
        const touchedFields: FormikTouched<SignupFirstStageDataType> = {};
        Object.keys(values).forEach(key => {
          touchedFields[key as keyof SignupFirstStageDataType] = true;
        });

        void formikRef.current?.setTouched(touchedFields);
        formikRef.current?.setErrors(errors);

        // Scroll to the first error
        if (Object.keys(errors).length > 0) {
          const firstErrorField = Object.keys(errors)[0] as keyof SignupFirstStageDataType;
          const element = document.querySelector(`[name="${firstErrorField}"]`);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          } else {
            window.scrollTo(0, 0);
          }
        }
        return;
      }

      onSubmit({ ...values, countryCode: countryCode as CountryCode, registeredBusinessType });
    },
    [countryCode, registeredBusinessType, onSubmit]
  );
  const configErrorMessage = useCallback(
    (
      errors: FormikErrors<SignupFirstStageDataType>,
      touched: FormikTouched<SignupFirstStageDataType>,
      values: SignupFirstStageDataType
    ) => {
      let msg = null;
      if (errors.industry && touched.industry && values.industry) {
        msg = errors.industry;
      }

      return msg === null || msg?.includes('invalid') ? null : <Feedback type="danger" message={msg} />;
    },
    []
  );

  const defaultValue = useCallback((options: OptionType[], val: number | string) => {
    return options ? options.find(option => option.value === val)?.value : '';
  }, []);

  const businessTypeRequirement = KYCServices.useFetchBusinessTypeRequirements({});

  const businessTypeRequirementsData = businessTypeRequirement?.data?.data || [];

  const getBusinessTypeNote = (key: string, country: string) => {
    const businessType = businessTypeRequirementsData.find(item => item.country === country?.toLowerCase())?.business_types[
      key as BusinessTypes
    ];

    const defaultBusinessType = businessTypeRequirementsData.find(item => item.country === 'default')?.business_types[key as BusinessTypes];
    return businessType || defaultBusinessType;
  };

  return (
    <Formik
      innerRef={ref => (formikRef.current = ref)}
      validate={values => validateFirstStage(values, registeredBusinessType)}
      initialValues={{
        btype: initialData?.btype || '',
        industry: initialData?.industry || '',
        country: initialData?.country || '',
        countryCode: (initialData?.countryCode || '') as CountryCode,
        registeredBusinessType: initialData?.registeredBusinessType || ''
      }}
      onSubmit={handleSubmit}
    >
      {({ errors, values, setFieldValue, touched }) => {
        const selected = values.btype;
        const note = getBusinessTypeNote(selected === 'registered_business' ? registeredBusinessType : selected, countryCode || 'default');

        return (
          <>
            {configErrorMessage(errors, touched, values)}
            <Form autoComplete="off">
              <div className="input_group input_group_div">
                {/* Country */}
                <div className="input__wrap">
                  <label htmlFor="Select country" className="screen-reader-only">
                    Country of Business Registration
                  </label>
                  <Suspense fallback={<div>Loading country selector...</div>}>
                    <CustomReactSelect
                      id="country"
                      label="Country of Business Registration"
                      options={countries}
                      value={defaultValue(countries, values.country)}
                      aria-label="country"
                      placeholder="Select Country"
                      onChange={(value: { value: number; isoCode: CountryCode }) => {
                        void setFieldValue('country', value.value);
                        setCountryCode(value.isoCode);
                      }}
                      isSearchable
                      required
                      styles={{
                        control: baseStyle => ({
                          ...baseStyle,
                          boxShadow: 'none',
                          borderWidth: '2px',
                          marginBottom: '20px',
                          borderRadius: '8px',
                          height: '59px',
                          borderColor: errors.country && touched.country ? '#f32345' : '#eff2f7'
                        })
                      }}
                    />
                  </Suspense>
                </div>

                {/* Industry field */}
                <Field name="industry">
                  {() => (
                    <div className="input__wrap">
                      <label htmlFor="industry" className="screen-reader-only">
                        Industry
                      </label>

                      <Suspense fallback={<div>Loading industry selector...</div>}>
                        <CustomReactSelect
                          id="industry"
                          label="Select an option"
                          options={industries}
                          placeholder="Select an industry"
                          onChange={(value: { label: string; value: string } | null) => {
                            if (value) {
                              void setFieldValue('industry', value.value);
                            } else {
                              void setFieldValue('industry', '');
                            }
                          }}
                          value={defaultValue(industries, values.industry)?.toString()}
                          isSearchable
                          aria-label="industry"
                          required
                          styles={{
                            control: baseStyle => ({
                              ...baseStyle,
                              boxShadow: 'none',
                              borderWidth: '2px',
                              marginBottom: '20px',
                              borderRadius: '8px',
                              height: '59px',
                              borderColor: errors.industry && touched.industry ? '#f32345' : '#eff2f7'
                            })
                          }}
                        />
                      </Suspense>
                      {errors.industry && touched.industry && (
                        <div className="error-message-display">
                          <span>{errors.industry}</span>
                        </div>
                      )}
                    </div>
                  )}
                </Field>

                <Field name="btype">
                  {() => (
                    <div className="">
                      <div className="input__wrap">
                        <label htmlFor="btype" className="screen-reader-only">
                          Business Type
                        </label>

                        <div className={`signup_business-type ${errors.btype && touched.btype ? 'has-error' : ''}`}>
                          <button
                            ref={ref1}
                            disabled={!values.country}
                            onClick={() => {
                              void setFieldValue('btype', 'individual');
                              setRegisteredBusinessType('');
                            }}
                            type="button"
                            onFocus={() => setBusinessTypeFocused(true)}
                            className={`signup_business-type-btn ${selected === 'individual' ? 'selectedType' : ''} ${
                              errors.btype && touched.btype ? 'error-border' : ''
                            }`}
                          >
                            <div className="signup_business-type-icon">
                              <Icon name="user" width={14} height={20} fill={selected === 'individual' ? '#0062F6' : undefined} />
                              <p>Individual</p>
                            </div>
                          </button>

                          <button
                            ref={ref2}
                            disabled={!values.country}
                            onClick={() => {
                              void setFieldValue('btype', 'ngo');
                              setRegisteredBusinessType('');
                            }}
                            onFocus={() => setBusinessTypeFocused(true)}
                            type="button"
                            className={`signup_business-type-btn ${selected === 'ngo' ? 'selectedType' : ''} ${
                              errors.btype && touched.btype ? 'error-border' : ''
                            }`}
                          >
                            <div className="signup_business-type-icon">
                              <Icon name="heart" width={14} height={20} fill={selected === 'ngo' ? '#0062F6' : undefined} />
                              <p>{isMobile ? 'NGO' : 'Non-Governmental Organisation (NGO)'}</p>
                            </div>
                          </button>

                          <button
                            ref={ref3}
                            disabled={!values.country}
                            onClick={() => {
                              void setFieldValue('btype', 'registered_business');
                            }}
                            type="button"
                            className={`signup_business-type-btn mb-0 ${selected === 'registered_business' ? 'selectedType' : ''} ${
                              errors.btype && touched.btype ? 'error-border' : ''
                            }`}
                          >
                            <div className="signup_business-type-icon">
                              <Icon
                                name="briefcasePlain"
                                width={14}
                                height={20}
                                fill={selected === 'registered_business' ? '#0062F6' : undefined}
                              />
                              <p>Registered Business</p>
                            </div>
                          </button>
                          {selected === 'registered_business' && (
                            <div className="signup_registered-options">
                              <div>
                                <label
                                  htmlFor="radio_reg_business_sme"
                                  className={registeredBusinessType === 'registered_business_sme' ? 'selected' : ''}
                                >
                                  Registered SME
                                </label>
                                <input
                                  id="radio_reg_business_sme"
                                  onFocus={() => setBusinessTypeFocused(true)}
                                  type="radio"
                                  value="registered_business_sme"
                                  checked={registeredBusinessType === 'registered_business_sme' && true}
                                  name="registered-business"
                                  onChange={e => {
                                    setRegisteredBusinessType(e.target.value);
                                  }}
                                />
                              </div>
                              <div>
                                <label
                                  htmlFor="radio_reg_business_non_sme"
                                  className={registeredBusinessType === 'registered_business_non_sme' ? 'selected' : ''}
                                >
                                  Registered Non-SME
                                </label>
                                <input
                                  id="radio_reg_business_non_sme"
                                  onFocus={() => setBusinessTypeFocused(true)}
                                  type="radio"
                                  value="registered_business_non_sme"
                                  checked={registeredBusinessType === 'registered_business_non_sme' && true}
                                  name="registered-business"
                                  onChange={e => {
                                    setRegisteredBusinessType(e.target.value);
                                  }}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                        {errors.btype && touched.btype && (
                          <div className="error-message-display business-type-error">
                            <span>{errors.btype}</span>
                          </div>
                        )}
                      </div>

                      {businessTypeFocused && Boolean(note) && (
                        <div className="country-notification mobile_bottom_space">
                          <p>
                            <span>Note:</span> {note?.description}
                          </p>
                          <ul className="mobile_bottom_space">{note?.documents.map((doc, index) => <li key={index}>{doc}</li>)}</ul>
                        </div>
                      )}
                    </div>
                  )}
                </Field>
                <div className="btn-wrapper mt-3">
                  <button className="btn-kpy --full-blue" type="submit">
                    Continue
                  </button>
                </div>
              </div>
            </Form>
          </>
        );
      }}
    </Formik>
  );
};

export default React.memo(SignupFirstStageComponent);
