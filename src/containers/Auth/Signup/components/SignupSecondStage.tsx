/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-props-no-spreading */
import React, { lazy, Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getCountryCallingCode } from 'react-phone-number-input';
import { Field, FieldProps, Form, Formik, FormikErrors, FormikProps, FormikTouched } from 'formik';
import { CountryCode } from 'libphonenumber-js';

import { IReCaptchaRef } from '+containers/Shared/CustomReCaptcha';
import Feedback from '+containers/Shared/Feedback';
import { AuthServices } from '+services/auth-services';
import { Storage } from '+services/storage-services';
import { ErrorResponse, ErrorResponseType, ISignupData, SignupFirstStageDataType } from '+types';
import { EmailValidation, history, logBreadCrumb, PhoneNumberValidation } from '+utils';
import { breadCrumbEvents } from '+utils/bugsnag-events';

import { usePasswordValidation, useSignupQueryParams } from '../hooks';
import { useValidateBusinessName } from '../hooks/useValidateBusinessName';
import { validate } from './signupHelper';

import 'react-phone-number-input/style.css';
import './index.scss';

const PhoneInput = lazy(() =>
  import('react-phone-number-input').then(module => ({
    default: module.default
  }))
);
const CustomReCaptcha = lazy(() => import('+containers/Shared/CustomReCaptcha'));

const SignupSecondStageComponent = ({
  firstStageData,
  initialData,
  onDataChange
}: {
  firstStageData?: SignupFirstStageDataType | null;
  initialData?: Partial<ISignupData> | null;
  onDataChange?: (data: Partial<ISignupData>) => void;
}) => {
  const { countryCode } = firstStageData || {};
  const { referralCode, mbsy } = useSignupQueryParams();
  const { passwordState, handlePasswordChange, validatePassword } = usePasswordValidation();

  const recaptchaRef = useRef<IReCaptchaRef>(null);
  const formRef = useRef<ISignupData | object>({});
  const formikRef = useRef<FormikProps<ISignupData> | null>(null);
  const [userEmail, setUserEmail] = useState('');

  const [focused, setFocused] = useState(false);
  const [emailFocused, setEmailFocused] = useState(false);
  const [cPassword, setCPassword] = useState(false);

  const [state, setState] = useState({
    isLoading: false,
    feedback: { message: '', visible: false, type: 'danger' }
  });

  const { businessNameValidityStatus, setBusinessNameValidityStatus } = useValidateBusinessName();

  // Initialize password state with persisted data if available
  useEffect(() => {
    if (initialData?.password && initialData.password !== passwordState.password) {
      handlePasswordChange(initialData.password);
    }
  }, [initialData?.password, handlePasswordChange, passwordState.password]);

  const onFocus = useCallback(() => setFocused(true), []);
  const onBlur = useCallback(() => setFocused(false), []);
  const onFocusCP = useCallback(() => setCPassword(true), []);

  const signupMutation = AuthServices.useSignUp({
    onSuccess: useCallback(() => {
      Storage.setItem('USER_EMAIL', userEmail);
      setState(prev => ({
        ...prev,
        isLoading: false
      }));
      history.push('/auth/email-sent');
    }, [userEmail]),

    onError: useCallback((e: unknown) => {
      const error = (e as ErrorResponseType).response?.data;
      window.scrollTo(0, 0);

      const errorMessage = () => {
        if (error?.error === 'bad_request' && Object.values(error?.data || {})[0]) {
          return Object.values(error?.data as Record<string, ErrorResponse>)[0].message;
        }
        if (error?.message) {
          return error.message;
        }
        return 'There seems to be an issue signing you up. <NAME_EMAIL> for more info.';
      };

      const errorMessageValue = errorMessage();

      if (errorMessageValue?.startsWith('Business name already taken')) {
        formikRef?.current?.setErrors({ bname: errorMessageValue });
        setState(prev => ({
          ...prev,
          isLoading: false
        }));
        setBusinessNameValidityStatus('invalid');
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          feedback: { ...prev.feedback, message: errorMessage(), visible: true }
        }));
      }
    }, [])
  });

  useEffect(() => {
    validatePassword();
  }, [passwordState.password, validatePassword]);

  const handleSubmit = useCallback(
    (values: ISignupData) => {
      if (businessNameValidityStatus === 'invalid') return;
      const errors = validate(values);

      // Touch all fields to show validation errors
      if (Object.keys(errors).length > 0) {
        // Touch all fields to trigger validation styling
        const touchedFields: FormikTouched<ISignupData> = {};
        Object.keys(values).forEach(key => {
          touchedFields[key as keyof ISignupData] = true;
        });

        void formikRef.current?.setTouched(touchedFields);
        formikRef.current?.setErrors(errors);

        // Scroll to the first error
        if (Object.keys(errors).length > 0) {
          const firstErrorField = Object.keys(errors)[0] as keyof ISignupData;
          const element = document.querySelector(`[name="${firstErrorField}"]`);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          } else {
            window.scrollTo(0, 0);
          }
        }
        return;
      }

      logBreadCrumb({ event: breadCrumbEvents.signup.signUpButtonClicked });

      if (recaptchaRef.current?.executeRecaptcha) {
        setUserEmail(values.email);
        setState(prev => ({ ...prev, isLoading: true }));

        if (values && process.env.REACT_APP_DARKHORSE_INIT === 'true') {
          const user = {
            id: values.phone_number,
            name: `${values.first_name} ${values.last_name}`,
            email: values.email
          };

          setTimeout(() => {
            void import('@datadog/browser-rum').then(({ datadogRum }) => {
              datadogRum.setUser(user);
            });
          }, 0);
        }

        const referral: Record<string, string> = {};
        if (mbsy) referral['provider_referral_code'] = mbsy;
        if (referralCode) referral['referral_code'] = referralCode;

        const userData = {
          email: values.email,
          first_name: values.first_name,
          last_name: values.last_name,
          password: passwordState.password,
          confirm_password: values.confirm_password,
          business_name: values.bname,
          business_type: values.btype,
          industry: values.industry,
          phone_number: values.phone_number,
          country: values.country,
          phone_country_code: values.phone_country_code,
          sra: values.sra?.length > 0 ? values.sra : undefined,
          ...(Object.keys(referral).length > 0 && { referral })
        };

        if (!values.sra) delete userData.sra;

        formRef.current = userData;
        recaptchaRef.current.executeRecaptcha();
      } else {
        setState(prev => ({
          ...prev,
          feedback: {
            message: 'Please wait while recatpcha loads and try again',
            visible: true,
            type: 'warning'
          }
        }));
      }
    },
    [mbsy, passwordState.password, referralCode, businessNameValidityStatus]
  );

  const signupAct = useCallback(() => {
    setState(prev => ({
      ...prev,
      feedback: { ...prev.feedback, visible: false, message: '' },
      isLoading: true
    }));
    signupMutation.mutate(formRef.current as ISignupData);
  }, [signupMutation]);

  const recaptchaError = useCallback((errorType: string) => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      feedback: {
        message: `Recaptcha verification ${errorType}. Please refresh this page and retry.`,
        visible: true,
        type: 'warning'
      }
    }));
  }, []);

  const recaptchNotReady = useCallback(() => {
    setState(prev => ({
      ...prev,
      feedback: {
        message: 'Please wait while recaptcha loads and try again',
        visible: true,
        type: 'warning'
      }
    }));
  }, []);

  const showFeedback = useMemo(() => {
    const { feedback } = state;
    return feedback.visible ? <Feedback type={feedback.type} message={feedback.message} /> : null;
  }, [state.feedback]);

  const getSpanColor = useCallback((minChar: boolean, valPassword: boolean) => {
    if (minChar) return 'valid';
    if (valPassword) return 'invalid';
    return 'neutral';
  }, []);

  const configErrorMessage = useCallback((errors: FormikErrors<ISignupData>, touched: FormikTouched<ISignupData>, values: ISignupData) => {
    let msg = null;

    if (errors.first_name && touched.first_name && values.first_name) {
      msg = errors.first_name;
    }
    if (errors.last_name && touched.last_name && values.last_name) {
      msg = errors.last_name;
    }
    if (errors.bname && touched.bname && values.bname && !errors.bname.startsWith('Business name already taken')) {
      msg = errors.bname;
    }
    if (errors.industry && touched.industry && values.industry) {
      msg = errors.industry;
    }
    if (errors.btype && touched.btype && values.btype) {
      msg = errors.btype;
    }
    if (errors.email && touched.email && values.email) {
      msg = errors.email;
    }
    if (errors.phone_number && touched.phone_number && values.phone_number) {
      msg = errors.phone_number;
    }
    if (errors.phone_country_code && touched.phone_country_code && values.phone_country_code) {
      msg = errors.phone_country_code;
    }

    return msg === null || msg?.includes('invalid') ? null : <Feedback type="danger" message={msg} />;
  }, []);

  const renderPasswordStrength = useMemo(() => {
    const { minChars, containUC, containLC, containSC, containNum, validPassword } = passwordState;

    const checkMark = String.fromCodePoint(0x02713);

    if (!focused) return null;

    return (
      <div className="password__strength mobile_bottom_space">
        <p>Password must contain at least</p>
        <div>
          <span className={`checkmark-icon password-criteria ${getSpanColor(minChars, validPassword)}`}>{checkMark}</span>
          <span className={`password-criteria ${getSpanColor(minChars, validPassword)}`}> 8 characters </span>
        </div>
        <div>
          <span className={`checkmark-icon password-criteria ${getSpanColor(containUC, validPassword)}`}>{checkMark}</span>
          <span className={`password-criteria ${getSpanColor(containUC, validPassword)}`}> 1 upper case letter (A-Z)</span>
        </div>
        <div>
          <span className={`checkmark-icon password-criteria ${getSpanColor(containLC, validPassword)}`}>{checkMark}</span>
          <span className={`password-criteria ${getSpanColor(containLC, validPassword)}`}> 1 lower case letter (a-z)</span>
        </div>
        <div>
          <span className={`checkmark-icon password-criteria ${getSpanColor(containSC, validPassword)}`}>{checkMark}</span>
          <span className={`password-criteria ${getSpanColor(containSC, validPassword)}`}> 1 special character (*@#-!...)</span>
        </div>
        <div>
          <span className={`checkmark-icon password-criteria ${getSpanColor(containNum, validPassword)}`}>{checkMark}</span>
          <span className={`password-criteria ${getSpanColor(containNum, validPassword)}`}> Must contain number</span>
        </div>
      </div>
    );
  }, [focused, getSpanColor, passwordState]);

  return (
    <Formik
      innerRef={ref => (formikRef.current = ref)}
      validate={validate}
      initialValues={{
        first_name: initialData?.first_name || '',
        last_name: initialData?.last_name || '',
        bname: initialData?.bname || '',
        btype: firstStageData?.btype || '',
        industry: firstStageData?.industry || '',
        email: initialData?.email || '',
        country: firstStageData?.country || '',
        phone_number: initialData?.phone_number || '',
        phone_country_code: countryCode || initialData?.phone_country_code || '',
        confirm_password: initialData?.confirm_password || '',
        password: initialData?.password || '',
        sra: initialData?.sra || '',
        tandc: initialData?.tandc || ''
      }}
      onSubmit={handleSubmit}
    >
      {({ errors, values, setFieldValue, touched, handleChange }) => {
        // Save form data to parent state whenever values change
        useEffect(() => {
          if (onDataChange) {
            onDataChange(values);
          }
        }, [values]);

        const allowOnlyText = (e: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
          void setFieldValue(fieldName, e.target.value.replace(/[^a-zA-Z]/gi, ''));
        };

        const allowTextandSpace = (e: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
          void setFieldValue(fieldName, e.target.value.replace(/[^A-Za-z ]/gi, ''));
        };

        const formDisabled = state.isLoading;

        return (
          <>
            {showFeedback || configErrorMessage(errors, touched, values)}
            <Form autoComplete="off">
              {/* Name fields section */}
              <div className="input_group">
                <Field name="first_name">
                  {({ field, meta: { touched: touch, error } }: FieldProps) => (
                    <div className="input__wrap">
                      <label htmlFor={field.name} className="screen-reader-only">
                        First Name
                      </label>
                      <input
                        className={touch && error ? 'error-field' : ''}
                        autoComplete="off"
                        maxLength={100}
                        {...field}
                        onChange={e => allowOnlyText(e, field.name)}
                        type="text"
                        required
                        aria-label="first_name"
                      />
                      {touch && error && (
                        <div className="error-message-display">
                          <span>{error}</span>
                        </div>
                      )}
                    </div>
                  )}
                </Field>

                <Field name="last_name">
                  {({ field, meta: { touched: touch, error } }: FieldProps) => (
                    <div className="input__wrap">
                      <label htmlFor={field.name} className="screen-reader-only">
                        Last Name
                      </label>
                      <input
                        className={touch && error ? 'error-field' : ''}
                        autoComplete="off"
                        maxLength={100}
                        {...field}
                        onChange={e => allowOnlyText(e, field.name)}
                        type="text"
                        required
                        aria-label="last_name"
                      />
                      {touch && error && (
                        <div className="error-message-display">
                          <span>{error}</span>
                        </div>
                      )}
                    </div>
                  )}
                </Field>
              </div>
              {/* Business name field */}
              <Field name="bname">
                {({ field, meta: { touched: touch, error } }: FieldProps) => (
                  <div className="input__wrap">
                    <label htmlFor="bname" className="screen-reader-only">
                      Business Name
                    </label>
                    <input
                      className={touch && error ? 'error-field' : ''}
                      {...field}
                      autoComplete="off"
                      type="text"
                      aria-label="business name"
                      required
                    />
                    {touch && error && (
                      <div className="error-message-display">
                        <span>{error}</span>
                      </div>
                    )}
                  </div>
                )}
              </Field>

              {/* Email and Phone section */}
              <div className="input_group">
                <Field name="email" validate={EmailValidation}>
                  {({ field, meta: { touched: touch, error } }: FieldProps) => (
                    <div className="input__wrap">
                      <label htmlFor="email" className="screen-reader-only">
                        Business Email
                      </label>
                      <input
                        className={touch && error ? 'error-field' : ''}
                        maxLength={100}
                        {...field}
                        autoComplete="off"
                        type="email"
                        aria-label="email"
                        required
                        onFocus={() => setEmailFocused(true)}
                        onBlur={() => setEmailFocused(false)}
                      />
                      {touch && error && (
                        <div className="error-message-display">
                          <span>{error}</span>
                        </div>
                      )}
                      {emailFocused && (
                        <div className="email-notification mobile_bottom_space">
                          <p>Please use emails with your company&apos;s domain extensions or emails listed on your website.</p>
                        </div>
                      )}
                    </div>
                  )}
                </Field>
                <Field name="phone_number" validate={PhoneNumberValidation}>
                  {({ field, meta: { touched: touch, error } }: FieldProps) => (
                    <div className="input__wrap phone-class">
                      <label htmlFor="Select country" className="screen-reader-only">
                        Phone Number
                      </label>
                      <Suspense fallback={<div>Loading phone input...</div>}>
                        <PhoneInput
                          {...field}
                          id="Select country"
                          className={`phone-input ${touch && error ? 'error' : ''}`}
                          international
                          defaultCountry={countryCode || 'NG'}
                          required
                          countryCallingCodeEditable={false}
                          country={values.phone_country_code}
                          onCountryChange={(country: CountryCode) => {
                            const dialCode = country ? getCountryCallingCode(country) : '234';
                            void setFieldValue('phone_country_code', dialCode);
                          }}
                          value={values.phone_number || ''}
                          onChange={(number: string) => {
                            const code = values.phone_country_code ? values.phone_country_code : '234';
                            void setFieldValue('phone_number', number);
                            if (!values.phone_country_code) {
                              void setFieldValue('phone_country_code', code);
                            }
                          }}
                        />
                        {touch && error && (
                          <div className="error-message-display">
                            <span>{error}</span>
                          </div>
                        )}
                      </Suspense>
                    </div>
                  )}
                </Field>
              </div>
              {/* Password field */}
              <Field name="password">
                {({ field }: FieldProps) => (
                  <div className="">
                    <label htmlFor="password" className="screen-reader-only">
                      Password
                    </label>
                    <input
                      className={focused === true ? 'error-field' : ''}
                      {...field}
                      value={passwordState.password}
                      name="password"
                      maxLength={100}
                      autoComplete="off"
                      onChange={e => {
                        handlePasswordChange(e.target.value);
                        void setFieldValue('password', e.target.value);
                      }}
                      type="password"
                      onFocus={onFocus}
                      onBlur={onBlur}
                      required
                      aria-label="password"
                      data-testid="password-input"
                    />
                    {errors.password && touched.password && (
                      <div className="error-message-display">
                        <span>{errors.password}</span>
                      </div>
                    )}
                    {renderPasswordStrength}
                  </div>
                )}
              </Field>
              {/* Confirm password field */}
              <Field name="confirm_password">
                {({ field }: FieldProps) => (
                  <div className="input__wrap password-field-wrap mb-3 mt-3">
                    <label htmlFor="confirm_password" className="screen-reader-only">
                      Confirm Password
                    </label>
                    <input
                      className={cPassword === true && values.confirm_password !== values.password ? 'error-field' : ''}
                      {...field}
                      autoComplete="off"
                      type="password"
                      required
                      onChange={handleChange}
                      onFocus={onFocusCP}
                      aria-label="confirm_password"
                    />
                    {cPassword === true && values.confirm_password !== values.password ? (
                      <div className="password-error">
                        <span>{errors.confirm_password}</span>
                      </div>
                    ) : null}
                  </div>
                )}
              </Field>
              {/* How did you hear about us field */}
              <Field name="sra">
                {({ field, meta: { touched: touch, error } }: FieldProps) => (
                  <div className="input__wrap">
                    <label htmlFor="sra" className="screen-reader-only">
                      How did you hear about us?
                    </label>
                    <input
                      maxLength={50}
                      className={touch && error ? 'error-field' : ''}
                      {...field}
                      autoComplete="off"
                      type="text"
                      required
                      aria-label="sra"
                      placeholder="Help us learn!"
                      onChange={e => allowTextandSpace(e, field.name)}
                      minLength={3}
                    />
                    {touch && error && (
                      <div className="error-message-display">
                        <span>{error}</span>
                      </div>
                    )}
                  </div>
                )}
              </Field>
              {/* Terms checkbox */}
              <div className="auth-check mt-3">
                <input onChange={handleChange} value={values.tandc} name="tandc" type="checkbox" aria-label="terms" required />
                <div className="check-label ml-2 text-left">
                  I agree to Kora&apos;s{' '}
                  <span className="link-text">
                    <a className="btn--link" href="https://www.korahq.com/merchant-service" target="_blank" rel="noopener noreferrer">
                      Merchant Service Agreement
                    </a>
                  </span>
                  , and the collection and processing of my personal data in accordance with Kora&apos;s{' '}
                  <span className="link-text">
                    <a className="btn--link" href="https://www.korahq.com/privacy-policy" target="_blank" rel="noopener noreferrer">
                      Privacy Notice
                    </a>
                  </span>
                  .
                </div>
              </div>
              {/* Submit button */}
              <div className="btn-wrapper mt-3">
                <button className="btn-kpy --full-blue" type="submit" disabled={formDisabled}>
                  {state.isLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" />
                      <span className="loading-indicator-space">Submitting...</span>
                    </>
                  ) : (
                    'Create Account'
                  )}
                </button>

                <Suspense fallback={<div>Loading captcha...</div>}>
                  <CustomReCaptcha ref={recaptchaRef} onChange={signupAct} onError={recaptchaError} onNotReady={recaptchNotReady} />
                </Suspense>
              </div>
              {/* Terms agreement */}
              <div className="auth-check mt-4">
                <div className="check-label terms-condition">
                  By creating an account you automatically agree to Kora&apos;s{' '}
                  <span className="link-text">
                    <a className="btn--link" href="https://www.korahq.com/terms-of-use" target="_blank" rel="noopener noreferrer">
                      Terms and Conditions
                    </a>
                  </span>
                  .
                </div>
              </div>
            </Form>
          </>
        );
      }}
    </Formik>
  );
};

export default React.memo(SignupSecondStageComponent);
