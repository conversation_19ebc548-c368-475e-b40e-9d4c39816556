/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useCallback, useState } from 'react';
import { Link } from 'react-router-dom';

import Icon from '+containers/Dashboard/Shared/Icons';
import { ISignupData, SignupFirstStageDataType } from '+types';

import SignupFirstStageComponent from './components/SignupFirstStage';
import SignupSecondStageComponent from './components/SignupSecondStage';

import ImgKorapayLogo from '+assets/img/logos/logo-kpy-ent.png';
import ImgKorapayLogoSvg from '+assets/img/logos/logo-kpy-ent.svg';

import 'react-phone-number-input/style.css';
import './index.scss';

const SignupComponent = () => {
  const [stage, setStage] = useState<'first' | 'second'>('first');
  const [firstStageData, setFirstStageData] = useState<null | SignupFirstStageDataType>(null);
  const [secondStageData, setSecondStageData] = useState<null | Partial<ISignupData>>(null);

  const handleLinkClick = useCallback((e: { preventDefault: () => void }) => {
    e.preventDefault();
    const url = new URL('https://www.korahq.com');
    window.open(url.origin + url.pathname, '_blank', 'noopener,noreferrer');
  }, []);

  const onFirstStageSubmit = (data: SignupFirstStageDataType) => {
    setFirstStageData(data);
    setStage('second');
  };

  const onSecondStageDataChange = useCallback((data: Partial<ISignupData>) => {
    setSecondStageData(data);
  }, []);

  return (
    <div className="auth__body">
      <div className="logo__section">
        <a href="https://www.korahq.com" target="_blank" rel="noopener noreferrer" onClick={handleLinkClick}>
          <img alt="Korapay Logo" src={ImgKorapayLogo} srcSet={ImgKorapayLogoSvg} loading="eager" />
        </a>
        <h2>
          Sign up your
          <br />
          business on Kora
        </h2>
        <p>Create your business account effortlessly.</p>
      </div>

      <div className="form__section">
        <div className="mid-section">
          {stage === 'second' && (
            <button type="button" onClick={() => setStage('first')} className="signup-back">
              <Icon name="arrowLeft" width={20} height={20} fill="#2376F3" />
              <span>&nbsp; Back</span>
            </button>
          )}
          <h3>Unlock powerful tools for your business</h3>
          <p>
            Follow the prompt, fill out the form below with details of your <br /> business.
          </p>
          {stage === 'first' && <SignupFirstStageComponent onSubmit={onFirstStageSubmit} initialData={firstStageData} />}
          {stage === 'second' && (
            <SignupSecondStageComponent
              firstStageData={firstStageData}
              initialData={secondStageData}
              onDataChange={onSecondStageDataChange}
            />
          )}
        </div>

        <div className="right-section">
          <p className="account-text">
            Already have an account?{' '}
            <Link className="link-text" to="/auth/login">
              Log In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default React.memo(SignupComponent);
