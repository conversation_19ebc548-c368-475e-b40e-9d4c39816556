import { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import { AuthServices } from '+services/auth-services';
import { Storage } from '+services/storage-services';
import Feedback from '+shared/Feedback';
import { ErrorResponseType } from '+types';
import { history } from '+utils';

import InvalidSvg from '+assets/img/auth/invalid.svg';
import VerifiedSvg from '+assets/img/auth/verified.svg';
import ImgKorapayLogoSvg from '+assets/img/logos/logo-kpy-ent.svg';

type userDataType = {
  email: string;
  merchant_name: string;
  country_name: string;
  is_supported_country: boolean;
  status: string;
};

const preloadImages = () => {
  const images = [ImgKorapayLogoSvg, InvalidSvg, VerifiedSvg];
  images.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
};

const VerificationSkeleton = () => (
  <div className="auth__center">
    <div
      style={{
        width: '80px',
        height: '80px',
        backgroundColor: '#f0f0f0',
        borderRadius: '50%',
        marginBottom: '30px',
        animation: 'pulse 1.5s ease-in-out infinite alternate'
      }}
    />
    <div
      style={{
        width: '200px',
        height: '24px',
        backgroundColor: '#f0f0f0',
        borderRadius: '4px',
        marginBottom: '10px',
        animation: 'pulse 1.5s ease-in-out infinite alternate'
      }}
    />
    <div
      style={{
        width: '150px',
        height: '16px',
        backgroundColor: '#f0f0f0',
        borderRadius: '4px',
        animation: 'pulse 1.5s ease-in-out infinite alternate'
      }}
    />
    <style>{`
      @keyframes pulse {
        0% { opacity: 1; }
        100% { opacity: 0.4; }
      }
    `}</style>
  </div>
);

const VerifyUser = () => {
  const { search } = useLocation();
  const params = new URLSearchParams(search);
  const token = params.get('verification_token');
  const email = params.get('email');
  const [tokenStatus, setTokenStatus] = useState('');
  const [state, setState] = useState({
    feedback: {
      message: '',
      visible: false,
      type: 'danger'
    }
  });

  // Preload images on component mount for better LCP
  useEffect(() => {
    preloadImages();
  }, []);

  const tokenMutate = AuthServices.useVerifyToken({
    onSuccess: res => {
      if (res.data?.is_supported_country === false) {
        Storage.setItem('USER_COUNTRY', res.data?.country_name);
        Storage.setItem('BUSINESS_NAME', res.data?.merchant_name);
        Storage.setItem('USER_EMAIL', res.data?.email);
        history.push('/auth/not-supported');
      } else {
        setTokenStatus('is_supported');
        // Reduce redirect delay for better UX
        setTimeout(() => {
          history.push('/auth/login');
        }, 2000);
      }
    },
    onError: e => {
      const error = (e as ErrorResponseType).response?.data;
      if ((error?.data as userDataType)?.status === 'expired') {
        setTokenStatus((error?.data as userDataType)?.status);
        Storage.setItem('USER_EMAIL', (error?.data as userDataType)?.email);
      } else {
        setTokenStatus('invalid');
      }
    }
  });

  const resendMutate = AuthServices.useResend({
    onSuccess: () => {
      return history.push('/auth/email-sent');
    },
    onError: e => {
      const error = (e as ErrorResponseType).response?.data;
      window.scrollTo(0, 0);
      const errorMessage = () => {
        if (error?.error === 'bad_request' && Object.values(error?.data || {})[0]) {
          return Object.values(error?.data as Record<string, { message: string }>)[0]?.message;
        }
        if (error?.message) {
          return error?.message;
        }
        return 'There seems to be an error sending you an email';
      };
      setState({
        ...state,
        feedback: { ...state.feedback, message: `${errorMessage()}`, visible: true }
      });
    }
  });

  const showFeedback = () => {
    const { feedback } = state;
    return feedback.visible ? <Feedback type={feedback.type} message={feedback.message} /> : '';
  };

  useEffect(() => {
    if (token && email) {
      const data = {
        verification_token: token,
        email: email.replace(' ', '+')
      };
      tokenMutate.mutate(data);
    } else if (!token || !email) {
      setTokenStatus('invalid');
    }
  }, [token, email]);

  return (
    <>
      <div className="auth__main">
        <div className="auth__header">
          <Link to="/" className="">
            <img alt="Korapay Logo" className="logo__container" src={ImgKorapayLogoSvg} loading="eager" decoding="sync" />
          </Link>
        </div>

        {tokenStatus === '' && <VerificationSkeleton />}

        {showFeedback()}

        {tokenStatus === 'is_supported' && (
          <div className="auth__center">
            <img src={VerifiedSvg} alt="Verified" loading="eager" decoding="sync" width="80" height="80" />
            <h2>
              Awesome! Your email has
              <br />
              been verified
            </h2>
            <button type="button" className="continue-btn" onClick={() => history.push('/auth/login')}>
              Login
            </button>
          </div>
        )}

        {tokenStatus === 'invalid' && (
          <div className="auth__center">
            <img src={InvalidSvg} alt="Invalid" loading="eager" decoding="sync" width="80" height="80" />
            <h2>This link is no longer valid.</h2>
            <span>This link has either been used or is no longer valid.</span>
            <button type="button" className="continue-btn" onClick={() => history.push('/')}>
              Go Home
            </button>
          </div>
        )}

        {tokenStatus === 'expired' && (
          <div className=" auth__center">
            <img src={InvalidSvg} alt="Invalid" loading="eager" decoding="sync" width="80" height="80" />
            <h2>Oops! This link has expired.</h2>
            <span>
              It seems like the verification link sent to your email has expired,
              <br />
              Click on the button below to request a new link.
            </span>
            <button type="button" className="continue-btn" onClick={() => resendMutate.mutate({ email })}>
              Resend Link
            </button>
          </div>
        )}
      </div>
    </>
  );
};

export default VerifyUser;
