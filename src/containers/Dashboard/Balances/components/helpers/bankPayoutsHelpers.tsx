import { BankAccount, BuildBankAccountParams, FormValuesType, TransferData, WithdrawalDetails } from '+types';

const currencyToCountryMap: Record<string, string> = {
  USD: 'US',
  GBP: 'GB',
  EURO: 'EU'
};

const getAccountName = (formValues: FormValuesType): string => {
  if (!formValues) return '';
  if (formValues.beneficiary_type === 'individual') {
    const first = formValues.first_name ?? '';
    const last = formValues.last_name ?? '';
    return `${first} ${last}`.trim();
  }
  return formValues.business_name ?? '';
};

const getAccountNumber = (
  isForeignTrx: boolean,
  foreignFormType: 'local' | 'international',
  foreignFormValues: FormValuesType,
  withdrawalDetails: WithdrawalDetails
): string | undefined => {
  if (isForeignTrx) {
    if (foreignFormType === 'local') {
      return foreignFormValues?.account_number;
    }
    if (foreignFormValues?.account_number_type === 'account_number') {
      return foreignFormValues?.account_number;
    }
    return foreignFormValues?.iban;
  }
  return withdrawalDetails?.bankNumber as string;
};

const buildBankAccount = ({
  withdrawalDetails,
  currency,
  isForeignTrx,
  foreignFormValues,
  foreignFormType
}: BuildBankAccountParams): BankAccount => {
  const baseBankAccount: BankAccount = {
    account: getAccountNumber(isForeignTrx, foreignFormType, foreignFormValues, withdrawalDetails)
  };

  if (!isForeignTrx) {
    return {
      ...baseBankAccount,
      bank: withdrawalDetails?.bankName as string
    };
  }

  const foreignBankAccount: BankAccount = {
    ...baseBankAccount,
    account_name: getAccountName(foreignFormValues),
    bank_name: foreignFormValues?.bank_name ?? '',
    beneficiary_type: foreignFormValues?.beneficiary_type ?? '',
    ...(foreignFormValues?.beneficiary_type === 'individual' && {
      first_name: foreignFormValues.first_name ?? '',
      last_name: foreignFormValues.last_name ?? ''
    }),
    ...(foreignFormValues?.beneficiary_type === 'corporate' && {
      business_name: foreignFormValues.business_name ?? ''
    }),
    account_type: foreignFormValues?.account_type ?? '',
    account_number_type: 'account_number',
    address_information: {
      country: currencyToCountryMap[currency] ?? 'Unknown',
      state: foreignFormValues?.state ?? '',
      city: foreignFormValues?.city ?? '',
      zip_code: foreignFormValues?.zip_code ?? '',
      street: foreignFormValues?.recipient_address ?? '',
      full_address: foreignFormValues?.recipient_address ?? ''
    }
  };

  if (foreignFormType === 'local') {
    return {
      ...foreignBankAccount,
      payment_method: foreignFormValues?.payment_method ?? '',
      ...(foreignFormValues?.payment_method === 'AbaRouting' && { routing_number: foreignFormValues?.routing_number ?? '' }),
      ...(foreignFormValues?.payment_method === 'BicSwift' && { swift_bic: foreignFormValues?.swift_bic ?? '' }),
      ...(foreignFormValues?.payment_method === 'FPS' && { sort_code: foreignFormValues?.sort_code ?? '' })
    };
  }

  if (foreignFormType === 'international') {
    return {
      ...foreignBankAccount,
      payment_method: 'BicSwift',
      routing_number: foreignFormValues?.swift_bic ?? '',
      intermediary_routing_number: foreignFormValues?.intermediary_routing_number ?? ''
    };
  }

  return {
    ...foreignBankAccount,
    ...(foreignFormValues?.account_number_type === 'account_number' && { sort_code: foreignFormValues?.sort_code ?? '' }),
    swift_bic: foreignFormValues?.swift_bic ?? ''
  };
};

export function getTransferData({
  twoFactorType,
  withdrawalDetails,
  currency,
  isForeignTrx,
  foreignFormValues,
  foreignFormType
}: {
  twoFactorType: string;
  withdrawalDetails: WithdrawalDetails;
  currency: string;
  isForeignTrx: boolean;
  foreignFormValues: FormValuesType;
  foreignFormType: 'local' | 'international';
}): TransferData {
  return {
    two_factor_type: twoFactorType,
    reference: '',
    destination: {
      type: 'bank_account',
      amount: withdrawalDetails?.amount ?? 0,
      narration: withdrawalDetails?.description || 'Withdrawal from Merchant Balance',
      currency,
      bank_account: buildBankAccount({
        withdrawalDetails,
        currency,
        isForeignTrx,
        foreignFormValues,
        foreignFormType
      }),
      customer: {
        name: isForeignTrx ? getAccountName(foreignFormValues) : (withdrawalDetails?.username ?? '')
      }
    }
  };
}
