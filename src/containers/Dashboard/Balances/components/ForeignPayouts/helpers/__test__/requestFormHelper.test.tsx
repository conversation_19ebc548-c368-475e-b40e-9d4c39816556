import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';

import {
  accountTypes,
  ErrorMsg,
  foreignCurrencies,
  formData,
  getFormProps,
  getInitialFormValues,
  getPaymentMethodFields
} from '../requestFormHelper';

describe('ErrorMsg', () => {
  it('renders nothing if not touched or no error', () => {
    const { container } = render(<ErrorMsg touched={false} error={undefined} id="test" />);
    expect(container).toBeEmptyDOMElement();
  });

  it('renders error message when touched and error present', () => {
    render(<ErrorMsg touched={true} error="Required field" id="test" testId="error-test" />);
    expect(screen.getByTestId('error-test')).toBeInTheDocument();
    expect(screen.getByText('Required field')).toBeInTheDocument();
  });
});

describe('getInitialFormValues', () => {
  it('returns object with all required form fields as empty strings', () => {
    const result = getInitialFormValues();

    expect(result.first_name).toBe('');
    expect(result.account_number_type).toBe('account_number');
    expect(Object.keys(result)).toHaveLength(22);
  });
});

describe('getFormProps', () => {
  const mockForm = {
    first_name: 'John',
    last_name: 'Doe',
    sender_name: '',
    sender_email: '',
    business_name: '',
    payment_method: '',
    account_number: '',
    swift_bic: '',
    routing_number: '',
    bank_name: '',
    country: '',
    recipient_address: '',
    state: '',
    city: '',
    zip_code: '',
    account_number_type: 'account_number',
    sort_code: '',
    iban: '',
    beneficiary_type: '',
    fps: '',
    account_type: '',
    intermediary_routing_number: ''
  };
  const mockSetForm = vi.fn();
  const mockTouched = { first_name: false };
  const mockSetTouched = vi.fn();
  const mockErrors = {};
  const mockSetErrors = vi.fn();
  const mockValidate = vi.fn().mockReturnValue({});

  it('returns correct form props structure', () => {
    const result = getFormProps(mockForm, mockSetForm, mockTouched, mockSetTouched, mockErrors, mockSetErrors, mockValidate);

    expect(result.values).toBe(mockForm);
    expect(result.isValid).toBe(true);
    expect(typeof result.getFieldProps).toBe('function');
    expect(typeof result.setFieldValue).toBe('function');
  });

  it('calculates isValid as false when errors exist', () => {
    const result = getFormProps(
      mockForm,
      mockSetForm,
      mockTouched,
      mockSetTouched,
      { first_name: 'Required' },
      mockSetErrors,
      mockValidate
    );

    expect(result.isValid).toBe(false);
  });
});

describe('getPaymentMethodFields', () => {
  it('shows routing number for USD AbaRouting', () => {
    const result = getPaymentMethodFields('USD', 'AbaRouting');
    expect(result.showRoutingNumber).toBe(true);
    expect(result.showSwiftBic).toBe(false);
    expect(result.showFPS).toBe(false);
  });

  it('shows Swift BIC for USD BicSwift', () => {
    const result = getPaymentMethodFields('USD', 'BicSwift');
    expect(result.showSwiftBic).toBe(true);
    expect(result.showRoutingNumber).toBe(false);
  });

  it('shows FPS for GBP FPS', () => {
    const result = getPaymentMethodFields('GBP', 'FPS');
    expect(result.showFPS).toBe(true);
    expect(result.showSwiftBic).toBe(false);
  });
});

describe('Constants', () => {
  it('formData contains expected currencies', () => {
    const currencies = formData.map(item => item.currency);
    expect(currencies).toEqual(['USD', 'GBP', 'EURO']);
  });

  it('foreignCurrencies contains expected values', () => {
    expect(foreignCurrencies).toEqual(['USD', 'GBP', 'EURO']);
  });

  it('accountTypes contains expected values', () => {
    expect(accountTypes).toEqual(['checking', 'savings']);
  });
});
