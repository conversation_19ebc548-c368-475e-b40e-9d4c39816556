import React from 'react';

import { CurrencyData, FormErrorsType, FormTouchedType, FormValuesType, RequestFormPropsType } from '+types';

import { UnitedKingdomBanks, UnitedStateBanks } from './foreignBankList';

interface ErrorMsgProps {
  touched: boolean | undefined;
  error: string | undefined;
  id: string;
  testId?: string;
}

export const accountTypes = ['checking', 'savings'];

export const ErrorMsg = ({ touched, error, id, testId }: ErrorMsgProps) => {
  if (!touched || !error) return null;

  return (
    <div className="input__errors" id={`${id}-error`} data-feedback="invalid" data-testid={testId}>
      <p>{error}</p>
    </div>
  );
};

export const foreignCurrencies = ['USD', 'GBP', 'EURO'];

export const formData: CurrencyData[] = [
  {
    currency: 'USD',
    paymentMethods: [
      { type: 'AbaRouting', label: 'Aba Routing' },
      { type: 'BicSwift', label: 'Swift BIC' }
    ]
  },
  {
    currency: 'GBP',
    paymentMethods: [
      { type: 'FPS', label: 'FPS' },
      { type: 'BicSwift', label: 'Swift BIC' }
    ]
  },
  {
    currency: 'EURO',
    paymentMethods: [
      { type: 'AbaRouting', label: 'Aba Routing' },
      { type: 'BicSwift', label: 'Swift BIC' }
    ]
  }
];

export const initialValues = {
  first_name: '',
  last_name: '',
  sender_name: '',
  sender_email: '',
  business_name: '',
  payment_method: '',
  account_number: '',
  swift_bic: '',
  routing_number: '',
  bank_name: '',
  country: '',
  recipient_address: '',
  state: '',
  city: '',
  zip_code: '',
  beneficiary_type: '',
  account_number_type: 'account_number',
  sort_code: '',
  iban: '',
  fps: '',
  account_type: '',
  intermediary_routing_number: ''
};

export const bankListMapping: { [key: string]: string[] } = {
  USD: UnitedStateBanks,
  GBP: UnitedKingdomBanks
};

export const getInitialFormValues = (): FormValuesType => ({
  first_name: '',
  last_name: '',
  sender_name: '',
  sender_email: '',
  business_name: '',
  payment_method: '',
  account_number: '',
  swift_bic: '',
  routing_number: '',
  bank_name: '',
  country: '',
  recipient_address: '',
  state: '',
  city: '',
  zip_code: '',
  account_number_type: 'account_number',
  sort_code: '',
  iban: '',
  beneficiary_type: '',
  fps: '',
  account_type: '',
  intermediary_routing_number: ''
});

export const getFormProps = (
  form: FormValuesType,
  setForm: React.Dispatch<React.SetStateAction<FormValuesType>>,
  touched: FormTouchedType,
  setTouched: React.Dispatch<React.SetStateAction<FormTouchedType>>,
  errors: FormErrorsType,
  setErrors: React.Dispatch<React.SetStateAction<FormErrorsType>>,
  validate: (values: FormValuesType) => FormErrorsType
): RequestFormPropsType => ({
  values: form,
  touched,
  errors,
  isValid: Object.keys(errors).length === 0,
  getFieldProps: (name: keyof FormValuesType) => ({
    name,
    value: form[name],
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      const value = e.target.value;
      setForm(prev => {
        const updated = { ...prev, [name]: value };
        setErrors(validate(updated));
        return updated;
      });
      setTouched(prev => ({ ...prev, [name]: true }));
    },
    onBlur: () => {
      setTouched(prev => ({ ...prev, [name]: true }));
      setErrors(validate(form));
    }
  }),
  setFieldValue: (name: keyof FormValuesType, value: string) => {
    setForm(prev => {
      const updated = { ...prev, [name]: value };
      setErrors(validate(updated));
      return updated;
    });
    setTouched(prev => ({ ...prev, [name]: true }));
  },
  handleBlur: (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
    const name = e.target.name as keyof FormValuesType;
    setTouched(prev => ({ ...prev, [name]: true }));
    setErrors(validate(form));
  }
});

export function getPaymentMethodFields(currency: string, paymentMethod: string) {
  const usdEuro = ['USD', 'EURO'].includes(currency);
  return {
    showRoutingNumber: usdEuro && paymentMethod === 'AbaRouting',
    showSwiftBic: (usdEuro && paymentMethod === 'BicSwift') || (currency === 'GBP' && paymentMethod === 'BicSwift'),
    showFPS: currency === 'GBP' && paymentMethod === 'FPS'
  };
}
