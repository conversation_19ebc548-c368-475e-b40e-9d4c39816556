import { FormValuesType } from '+types';

export type FormErrors = Partial<Record<keyof FormValuesType, string>>;

export const validateLocal = (values: FormValuesType): FormErrors => {
  const errors: FormErrors = {};
  if (values.beneficiary_type === 'individual') {
    if (!values.first_name) errors.first_name = 'First name is required';
    if (!values.last_name) errors.last_name = 'Last name is required';
  }
  if (values.beneficiary_type === 'corporate') {
    if (!values.business_name) errors.business_name = 'Business name is required';
    if (!values.recipient_address) errors.recipient_address = 'Full address is required';
  }
  if (!values.account_number) errors.account_number = 'Account number is required';
  if (!values.bank_name) errors.bank_name = 'Bank name is required';
  if (!values.zip_code) errors.zip_code = 'Postal code is required';
  if (!values.country) errors.country = 'Country is required';
  if (!values.state) errors.state = 'State/City is required';
  if (!values.sender_name) errors.sender_name = 'Sender name is required';
  if (!values.payment_method) errors.payment_method = 'Payment Method type is required';
  if (!values.account_type) errors.account_type = 'Account type is required';
  return errors;
};

export const validateIntl = (values: FormValuesType): FormErrors => {
  const errors: FormErrors = {};
  if (values.beneficiary_type === 'individual') {
    if (!values.first_name) errors.first_name = 'First name is required';
    if (!values.last_name) errors.last_name = 'Last name is required';
  }
  if (values.beneficiary_type === 'corporate') {
    if (!values.business_name) errors.business_name = 'Business name is required';
  }
  if (!values.bank_name) errors.bank_name = 'Bank name is required';
  if (!values.zip_code) errors.zip_code = 'Postal code is required';
  if (!values.country) errors.country = 'Country is required';
  if (!values.state) errors.state = 'State/City is required';
  if (!values.sender_name) errors.sender_name = 'Sender name is required';
  if (!values.account_type) errors.account_type = 'Account type is required';
  if (!values.intermediary_routing_number) errors.intermediary_routing_number = 'Intermediary routing number is required';

  if (values.account_number_type === 'account_number') {
    if (!values.account_number) errors.account_number = 'Account number is required';
  }
  if (values.account_number_type === 'iban') {
    if (!values.iban) errors.iban = 'IBAN is required';
    if (!values.swift_bic) errors.swift_bic = 'Swift BIC is required';
  }
  return errors;
};
