import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import ForeignPayouts from '../index';

const mockProps = {
  currency: 'USD',
  onValidityChange: vi.fn(),
  onFormChange: vi.fn()
};

const MockedForeignPayouts = () => {
  return (
    <MockIndex>
      <ForeignPayouts {...mockProps} />
    </MockIndex>
  );
};

describe('ForeignPayouts', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<MockedForeignPayouts />);

    await waitFor(async () => {
      const result = await axe(container);
      expect(result).toHaveNoViolations();
    });
  });

  it('renders LocalTransferForm by default', () => {
    render(<MockedForeignPayouts />);
    expect(screen.getByTestId('local-form')).toBeInTheDocument();
    expect(screen.getByText(/Local Transfer/i)).toBeInTheDocument();
  });

  it('switches to InternationalTransferForm', () => {
    render(<MockedForeignPayouts />);
    userEvent.click(screen.getByText(/International Transfer/i));
    expect(screen.getByTestId('intl-form')).toBeInTheDocument();
    expect(screen.getByText(/International Transfer/i)).toBeInTheDocument();
  });

  it('shows modal when switching with dirty form', () => {
    render(<MockedForeignPayouts />);
    const input = screen.getAllByTestId('recipientName');
    expect(input).toHaveLength(2);
    userEvent.type(input[0], 'John');
    userEvent.click(screen.getByText(/International Transfer/i));
    waitFor(() => {
      expect(screen.getByText(/Leaving the transfer form\?/i)).toBeInTheDocument();
      userEvent.click(screen.getByText(/Yes, Leave/i));
    });
  });
});
