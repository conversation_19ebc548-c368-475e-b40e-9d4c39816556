import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import LocalTransferForm from '../LocalTransfer';

const mockProps = {
  currency: 'USD',
  onValidityChange: vi.fn(),
  onFormChange: vi.fn()
};

const MockedLocalTransferForm = () => {
  return (
    <MockIndex>
      <LocalTransferForm
        currency={mockProps.currency}
        onValidityChange={mockProps.onValidityChange}
        onFormChange={mockProps.onFormChange}
      />
    </MockIndex>
  );
};

describe('ForeignPayouts', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<MockedLocalTransferForm />);
    const result = await axe(container);
    expect(result).toHaveNoViolations();
  });

  it('switches to corporate fields when Corporate Account is selected', async () => {
    render(<MockedLocalTransferForm />);
    const corporateRadio = screen.getByText(/Corporate Account/i);
    await userEvent.click(corporateRadio);
    expect(screen.getByLabelText(/Recipient's Business Name/i)).toBeInTheDocument();
    expect(screen.queryByLabelText(/Recipient's Name/i)).not.toBeInTheDocument();
  });

  it('displays errors when touched and errors are present', async () => {
    render(<MockedLocalTransferForm />);
    const [firstName, lastName] = screen.getAllByTestId('recipientName');
    await userEvent.clear(firstName);
    await userEvent.tab();
    await userEvent.clear(lastName);
    await userEvent.tab();
    expect(await screen.findByText(/First name/i)).toBeInTheDocument();
    expect(await screen.findByText(/Last name/i)).toBeInTheDocument();
  });

  it('renders input fields', () => {
    render(<MockedLocalTransferForm />);
    expect(screen.getAllByTestId('recipientName')).toHaveLength(2);
    expect(screen.getByLabelText(/Account Number/i)).toBeInTheDocument();
    expect(screen.getByTestId('accountNumber')).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's City/i)).toBeInTheDocument();
    expect(screen.getByTestId('city')).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's Address/i)).toBeInTheDocument();
    expect(screen.getByTestId('recipientAddress')).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's Postcode/i)).toBeInTheDocument();
    expect(screen.getByTestId('zipcode')).toBeInTheDocument();
    expect(screen.getByLabelText(/Sender's Name/i)).toBeInTheDocument();
    expect(screen.getByTestId('senderName')).toBeInTheDocument();
    expect(screen.getByLabelText(/Sender's Email Address/i)).toBeInTheDocument();
    expect(screen.getByTestId('senderEmail')).toBeInTheDocument();
  });

  it('renders select dropdowns', () => {
    render(<MockedLocalTransferForm />);
    expect(screen.getByLabelText(/Individual Account/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Corporate Account/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Account Type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Payment Method/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's Country/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/State/i)).toBeInTheDocument();
  });
});
