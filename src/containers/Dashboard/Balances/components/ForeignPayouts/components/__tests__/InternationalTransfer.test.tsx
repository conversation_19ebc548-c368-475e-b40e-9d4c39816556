import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import InternationalTransferForm from '../InternationalTransfer';

const mockCountriesList = [
  { value: 'US', label: 'United States', isoCode: 'US' },
  { value: 'GB', label: 'United Kingdom', isoCode: 'GB' }
];

const mockStatesList = {
  US: ['California', 'Texas'],
  GB: ['London', 'Manchester']
};

const mockProps = {
  currency: 'USD',
  countriesList: mockCountriesList,
  statesList: mockStatesList,
  onValidityChange: vi.fn(),
  onFormChange: vi.fn()
};

const MockedInternationalTransferForm = () => {
  return (
    <MockIndex>
      <InternationalTransferForm
        currency={mockProps.currency}
        countriesList={mockProps.countriesList}
        statesList={mockProps.statesList}
        onValidityChange={mockProps.onValidityChange}
        onFormChange={mockProps.onFormChange}
      />
    </MockIndex>
  );
};

describe('InternationalTransfer', () => {
  it('switches to corporate fields when Corporate Account is selected', async () => {
    render(<MockedInternationalTransferForm />);
    const corporateRadio = screen.getByText(/Corporate Account/i);
    await userEvent.click(corporateRadio);
    expect(screen.queryByLabelText(/Recipient's Name/i)).not.toBeInTheDocument();
  });

  it('displays errors when touched and errors are present', async () => {
    render(<MockedInternationalTransferForm />);
    const [firstName, lastName] = screen.getAllByTestId('recipientName');
    await userEvent.clear(firstName);
    await userEvent.tab();
    await userEvent.clear(lastName);
    await userEvent.tab();
    expect(await screen.findByText(/First name/i)).toBeInTheDocument();
    expect(await screen.findByText(/Last name/i)).toBeInTheDocument();
  });

  it('renders input fields', async () => {
    render(<MockedInternationalTransferForm />);
    expect(screen.getAllByTestId('recipientName')).toHaveLength(2);
    expect(screen.getByTestId('accountNumber')).toBeInTheDocument();
    expect(screen.getByTestId('swiftBIC')).toBeInTheDocument();
    expect(screen.getByLabelText(/Intermediary Routing Number/i)).toBeInTheDocument();
    expect(screen.getByTestId('intermediaryRoutingNumber')).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's City/i)).toBeInTheDocument();
    expect(screen.getByTestId('city')).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's Address/i)).toBeInTheDocument();
    expect(screen.getByTestId('recipientAddress')).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's Postcode/i)).toBeInTheDocument();
    expect(screen.getByTestId('zipcode')).toBeInTheDocument();
    expect(screen.getByLabelText(/Sender's Name/i)).toBeInTheDocument();
    expect(screen.getByTestId('senderName')).toBeInTheDocument();
    expect(screen.getByLabelText(/Sender's Email Address/i)).toBeInTheDocument();
    expect(screen.getByTestId('senderEmail')).toBeInTheDocument();
    expect(screen.getByLabelText(/IBAN/i)).toBeInTheDocument();
    const ibanRadio = screen.getByLabelText(/IBAN/i);
    await userEvent.click(ibanRadio);
    const ibanInput = await screen.findByTestId('iban');
    expect(ibanInput).toBeInTheDocument();
  });

  it('renders select dropdowns', () => {
    render(<MockedInternationalTransferForm />);
    expect(screen.getByLabelText(/Individual Account/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Corporate Account/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Account Type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Recipient's Country/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/State/i)).toBeInTheDocument();
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<MockedInternationalTransferForm />);
    const result = await axe(container);
    expect(result).toHaveNoViolations();
  });
});
