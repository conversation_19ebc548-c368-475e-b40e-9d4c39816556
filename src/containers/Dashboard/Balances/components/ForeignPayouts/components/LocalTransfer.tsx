import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import RadioButton from '+containers/Dashboard/Shared/RadioButton';
import ListDropdown from '+dashboard/Shared/ListDropdown';
import { CountryItemType, IListItem } from '+types';
import { ForeignBankProps, FormErrorsType, FormTouchedType, FormValuesType } from '+types/balances-types';
import { cleanInput } from '+utils';

import {
  accountTypes,
  bankListMapping,
  ErrorMsg,
  formData,
  getFormProps,
  getInitialFormValues,
  getPaymentMethodFields
} from '../helpers/requestFormHelper';
import { validateLocal } from '../helpers/requestFormValidation';

import '../index.scss';

const LocalTransferForm = forwardRef(function LocalTransferForm(props: ForeignBankProps, ref) {
  const { countriesList, statesList, currency, onValidityChange, onFormChange } = props;
  const firstNameRef = useRef<HTMLInputElement>(null);
  const [localForm, setLocalForm] = useState<FormValuesType>(getInitialFormValues());
  const initialForm = useRef<FormValuesType>(getInitialFormValues());
  const [localTouched, setLocalTouched] = useState<FormTouchedType>({});
  const [localErrors, setLocalErrors] = useState<FormErrorsType>({});
  const { values, errors, touched, setFieldValue, handleBlur } = getFormProps(
    localForm,
    setLocalForm,
    localTouched,
    setLocalTouched,
    localErrors,
    setLocalErrors,
    validateLocal
  );
  const [isBankListOpen, setIsBankListOpen] = useState(false);
  const [beneficiaryType, setBeneficiaryType] = useState<'individual' | 'corporate'>('individual');

  const selectedCountry = countriesList?.find(country => country.value === values?.country) as CountryItemType | undefined;
  const isoCode = selectedCountry?.isoCode || selectedCountry?.value;
  const stateArray = useMemo(() => {
    const arr = isoCode ? statesList?.[isoCode] : [];
    if (!arr) return [];
    if (typeof arr[0] === 'string') {
      return arr.map(name => ({ name }));
    }
    return arr;
  }, [isoCode, statesList]);

  const bankList = useMemo(() => {
    return bankListMapping[currency]?.map(bank => ({ code: bank, name: bank, status: 'active' }));
  }, [currency]);

  const getBankListValue = (value?: string) => {
    if (!value) return { code: '', name: '', status: 'active' };
    return bankList?.find(bank => bank.name === value);
  };

  const paymentMethods = useMemo(() => {
    return formData.find(fd => fd.currency === currency)?.paymentMethods || [];
  }, [currency]);

  useImperativeHandle(ref, () => ({
    isDirty: () => {
      return Object.keys(localForm).some(
        key => localForm[key as keyof FormValuesType] !== initialForm.current[key as keyof FormValuesType]
      );
    },
    resetForm: () => {
      setLocalForm(getInitialFormValues());
      setLocalTouched({});
      setLocalErrors({});
      setBeneficiaryType('individual');
    },
    getFormValues: () => localForm
  }));

  useEffect(() => {
    firstNameRef.current?.focus();
    setFieldValue('beneficiary_type', 'individual');
  }, []);

  useEffect(() => {
    onValidityChange?.(Object.keys(localErrors).length === 0);
  }, [localErrors, onValidityChange]);

  useEffect(() => {
    onFormChange?.(localForm);
  }, [localForm, onFormChange]);

  const { showRoutingNumber, showSwiftBic, showFPS } = getPaymentMethodFields(currency, values.payment_method);

  return (
    <div className="form-wrapper">
      <div className="form-group account-type-radio">
        <div>
          <RadioButton
            checked={beneficiaryType === 'individual'}
            disabled={false}
            label="Individual Account"
            onChange={() => {
              setBeneficiaryType('individual');
              setFieldValue('beneficiary_type', 'individual');
            }}
          />
        </div>
        <div>
          <RadioButton
            checked={beneficiaryType === 'corporate'}
            disabled={false}
            label="Corporate Account"
            onChange={() => {
              setBeneficiaryType('corporate');
              setFieldValue('beneficiary_type', 'corporate');
            }}
          />
        </div>
      </div>
      {beneficiaryType === 'individual' && (
        <div className="form-group">
          <label className="withdraw-label" htmlFor="recipientName">
            <span className="dark">Recipient&apos;s Name</span>
          </label>
          <div className="recipient-name">
            <input
              id="firstName"
              className="form-control"
              data-testid="recipientName"
              type="text"
              name="first_name"
              placeholder="John"
              value={values?.first_name}
              onChange={e => setFieldValue('first_name', cleanInput(e.target.value))}
              onBlur={handleBlur}
              ref={firstNameRef}
            />
            <input
              id="lastName"
              className="form-control"
              data-testid="recipientName"
              type="text"
              name="last_name"
              placeholder="Doe"
              value={values?.last_name}
              onChange={e => setFieldValue('last_name', cleanInput(e.target.value))}
              onBlur={handleBlur}
            />
          </div>
          <ErrorMsg touched={touched?.first_name} error={errors?.first_name} id="firstName" />
          <ErrorMsg touched={touched?.last_name} error={errors?.last_name} id="lastName" />
        </div>
      )}
      {beneficiaryType === 'corporate' && (
        <div className="form-group">
          <label className="withdraw-label" htmlFor="businessName">
            <span className="dark">Recipient&apos;s Business Name</span>
          </label>
          <input
            id="businessName"
            className="form-control"
            data-testid="businessName"
            type="text"
            name="business_name"
            placeholder="Recipient's Business Name"
            value={values?.business_name}
            onChange={e => setFieldValue('business_name', cleanInput(e.target.value))}
            onBlur={handleBlur}
          />
          <ErrorMsg touched={touched?.business_name} error={errors?.business_name} id="businessName" />
        </div>
      )}
      <div className="form-group">
        <label className="withdraw-label" htmlFor="accountType">
          <span className="dark">Account Type</span>
        </label>
        <select
          id="accountType"
          className="form-control"
          value={values?.account_type || ''}
          onChange={e => {
            setFieldValue('account_type', e.target.value);
          }}
        >
          <option value="">- Select account type -</option>
          {accountTypes.map(type => (
            <option key={type} value={type}>
              {type}
            </option>
          ))}
        </select>
      </div>
      <hr className="form-divider" />
      <div className="form-group">
        <label className="withdraw-label" htmlFor="paymentMethod">
          <span className="dark">Payment Method</span>
        </label>
        <select
          id="paymentMethod"
          className="form-control"
          value={values?.payment_method || ''}
          onChange={e => {
            setFieldValue('payment_method', e.target.value);
            setFieldValue('routing_number', '');
            setFieldValue('swift_bic', '');
            setFieldValue('sort_code', '');
            setFieldValue('iban', '');
          }}
        >
          <option value="">- Select payment method -</option>
          {paymentMethods.map(method => (
            <option key={method.type} value={method.type}>
              {method.label}
            </option>
          ))}
        </select>
        <ErrorMsg touched={touched?.payment_method} error={errors?.payment_method} id="paymentMethod" />
      </div>
      {showRoutingNumber && (
        <div className="form-group">
          <label className="withdraw-label" htmlFor="routingNumber">
            <span className="dark">Routing number</span>
          </label>
          <input
            id="routingNumber"
            className="form-control"
            data-testid="routingNumber"
            type="text"
            name="routing_number"
            placeholder="00956"
            value={values?.routing_number}
            onChange={e => setFieldValue('routing_number', cleanInput(e.target.value))}
            onBlur={handleBlur}
          />
          <ErrorMsg touched={touched?.routing_number} error={errors?.routing_number} id="routingNumber" />
        </div>
      )}
      {showSwiftBic && (
        <div className="form-group">
          <label className="withdraw-label" htmlFor="swiftBic">
            <span className="dark">Swift BIC</span>
          </label>
          <input
            id="swiftBic"
            className="form-control"
            data-testid="swiftBic"
            type="text"
            name="swift_bic"
            placeholder="E.g ********"
            value={values?.swift_bic}
            onChange={e => setFieldValue('swift_bic', cleanInput(e.target.value))}
            onBlur={handleBlur}
          />
          <ErrorMsg touched={touched?.swift_bic} error={errors?.swift_bic} id="swiftBic" />
        </div>
      )}
      {showFPS && (
        <div className="form-group">
          <label className="withdraw-label" htmlFor="fps">
            <span className="dark">Sort Code</span>
          </label>
          <input
            id="fps"
            className="form-control"
            data-testid="fps"
            type="text"
            name="sort_code"
            placeholder="E.g 12-34-56"
            value={values?.sort_code}
            onChange={e => setFieldValue('sort_code', cleanInput(e.target.value))}
            onBlur={handleBlur}
          />
          <ErrorMsg touched={touched?.sort_code} error={errors?.sort_code} id="fps" />
        </div>
      )}
      <div className="form-group">
        <label className="withdraw-label" htmlFor="accountNumber">
          <span className="dark">Account Number</span>
        </label>
        <input
          id="accountNumber"
          className="form-control"
          data-testid="accountNumber"
          type="text"
          name="account_number"
          placeholder="e.g ************"
          value={values?.account_number}
          onChange={e => setFieldValue('account_number', cleanInput(e.target.value))}
          onBlur={handleBlur}
        />
        <ErrorMsg touched={touched?.account_number} error={errors?.account_number} id="accountNumber" />
      </div>
      <div className="form-group reason-container">
        <label htmlFor="bankNameDropdown" className="withdraw-label">
          <span className="dark form_label bank_name_label">Bank Name</span>
        </label>
        <ListDropdown
          data-testid="bankNameDropdown"
          list={bankList || []}
          type="bank"
          className="banks-list form-control"
          notFoundCTAText="Enter bank name"
          notFoundText="Bank not found?"
          allowCustomInput
          customInputShowCTAText="Or select from our list"
          customInputHiddenCTAText="Can't find your bank on the list?"
          customInputPlaceholder="Bank name"
          value={getBankListValue(values?.bank_name)}
          active={isBankListOpen}
          setActive={(open: boolean) => setIsBankListOpen(open)}
          setValue={(value: IListItem | string) => {
            const val = value as IListItem;
            setFieldValue('bank_name', val.name);
          }}
        />
        <ErrorMsg touched={touched?.bank_name} error={errors?.bank_name} id="bankName" />
      </div>
      <hr className="form-divider" />
      <div className="form-group">
        <label className="withdraw-label" htmlFor="country">
          <span className="dark">Recipient&apos;s Country</span>
        </label>
        <select
          id="country"
          className="form-control"
          aria-describedby="country"
          value={values?.country || ''}
          onChange={e => {
            setFieldValue('country', e.target.value);
            setFieldValue('state', '');
          }}
        >
          <option value="">- Select a country -</option>
          {countriesList?.map(({ value, label }) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
        <ErrorMsg touched={touched?.country} error={errors?.country} id="country" />
      </div>
      <div className="form-group">
        <label className="withdraw-label" htmlFor="state">
          <span className="dark">State</span>
        </label>
        <select
          id="state"
          className="form-control"
          aria-describedby="state"
          value={values?.state || ''}
          onChange={e => setFieldValue('state', e.target.value)}
        >
          <option value="">- Select a state -</option>
          {stateArray?.map((state: { name: string } | string) => {
            if (typeof state === 'string') {
              return (
                <option key={state} value={state}>
                  {state}
                </option>
              );
            }
            return (
              <option key={state.name} value={state.name}>
                {state.name}
              </option>
            );
          })}
        </select>
        <ErrorMsg touched={touched?.state} error={errors?.state} id="state" />
      </div>
      <div className="form-group">
        <label className="withdraw-label" htmlFor="city">
          <span className="dark">Recipient&apos;s City</span>
        </label>
        <input
          id="city"
          className="form-control"
          data-testid="city"
          type="text"
          name="name"
          placeholder="Enter City"
          value={values?.city}
          onChange={e => setFieldValue('city', cleanInput(e.target.value))}
          onBlur={handleBlur}
        />
      </div>
      <div className="form-group">
        <label className="withdraw-label" htmlFor="recipientAddress">
          <span className="dark">Recipient&apos;s Address</span>
        </label>
        <input
          id="recipientAddress"
          className="form-control"
          data-testid="recipientAddress"
          type="text"
          name="recipient_address"
          placeholder="123 Street Ave"
          value={values?.recipient_address}
          onChange={e => setFieldValue('recipient_address', cleanInput(e.target.value))}
          onBlur={handleBlur}
        />
        <ErrorMsg touched={touched?.recipient_address} error={errors?.recipient_address} id="recipientAddress" />
      </div>
      <div className="form-group">
        <label className="withdraw-label" htmlFor="zipCode">
          <span className="dark">Recipient&apos;s Postcode</span>
        </label>
        <input
          id="zipCode"
          className="form-control"
          data-testid="zipcode"
          type="text"
          name="zip_code"
          placeholder={'10017'}
          value={values?.zip_code}
          onChange={e => setFieldValue('zip_code', cleanInput(e.target.value))}
          onBlur={handleBlur}
        />
        <ErrorMsg touched={touched?.zip_code} error={errors?.zip_code} id="zipCode" />
      </div>
      <hr className="form-divider" />
      <div className="form-group">
        <label className="withdraw-label" htmlFor="senderName">
          <span className="dark">Sender&apos;s Name</span>
        </label>
        <input
          id="senderName"
          className="form-control"
          data-testid="senderName"
          type="text"
          name="name"
          placeholder="Sarah Doe"
          value={values?.sender_name}
          onChange={e => setFieldValue('sender_name', cleanInput(e.target.value))}
          onBlur={handleBlur}
        />
      </div>
      <div className="form-group">
        <label className="withdraw-label" htmlFor="senderEmail">
          <span className="dark">Sender&apos;s Email Address</span>
        </label>
        <input
          id="senderEmail"
          className="form-control"
          data-testid="senderEmail"
          type="email"
          name="sender_email"
          placeholder="<EMAIL>"
          value={values?.sender_email}
          onChange={e => setFieldValue('sender_email', cleanInput(e.target.value))}
          onBlur={handleBlur}
        />
      </div>
    </div>
  );
});

export default LocalTransferForm;
