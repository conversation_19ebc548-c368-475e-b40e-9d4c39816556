@import 'styles/base/variables';

.foreign_payouts_form {
  height: 32rem;
  overflow-y: auto;
  .form-buttons {
    align-items: center;
    justify-content: center;
    background: #ffff;
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 0 5px;

    .toggle-transfer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      background: #f1f6fa;
      border-radius: 5px;
      & button {
        color: #292b2c;
      }

      & .btn-selected {
        background: white;
        color: $kpyblue !important;
        font-size: 1rem;
        width: 50%;
        padding: 5px 0 5px 0;
        box-shadow: 0 0 10px rgba(62, 75, 91, 0.32);
      }
    }
  }
}

.form-wrapper {
  padding: 0 5px;
}

.recipient-name {
  display: flex;
  justify-content: space-between;

  & input {
    max-width: 49%;
  }

  > span {
    margin: 0 0 15px 0;
  }
}

.bank_name_label {
  color: #4a4a4a;
  font-weight: 500;
  font-size: 0.9rem;
}

.account-type-radio {
  display: flex;
  margin-top: 20px;

  :first-child {
    margin-right: 10px;
  }
}

.form-divider {
  border: none;
  border-bottom: 2px solid #dde2ec;
}
