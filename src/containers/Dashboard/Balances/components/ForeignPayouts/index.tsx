import React, { useRef, useState } from 'react';

import Modal from '+dashboard/Shared/Modal';
import useCountriesAndStates from '+hooks/useCountriesAndStates';
import { ForeignPayoutsPropsType, FormRefType, FormValuesType } from '+types';

import InternationalTransferForm from './components/InternationalTransfer';
import LocalTransferForm from './components/LocalTransfer';

const hasNonEmptyValue = (form: FormValuesType): boolean => {
  const excludedFields: string[] = ['beneficiary_type', 'account_number_type'];
  return Object.entries(form).some(
    ([fieldName, value]) => !excludedFields.includes(fieldName) && value != null && String(value).trim() !== ''
  );
};

const ForeignPayouts: React.FC<
  ForeignPayoutsPropsType & {
    onFormChange?: (values: FormValuesType) => void;
    onFormTypeChange?: (type: 'local' | 'international') => void;
  }
> = ({ currency, onValidityChange, onFormChange, onFormTypeChange }) => {
  const { countriesList, statesList } = useCountriesAndStates();

  const countries = countriesList.map(country => ({
    ...country,
    isoCode: country.value
  }));
  const states = statesList;

  const [activeForm, setActiveForm] = useState<'local' | 'international'>('local');
  const [showModal, setShowModal] = useState(false);
  const [pendingForm, setPendingForm] = useState<'local' | 'international' | null>(null);

  const localFormRef = useRef<FormRefType>(null);
  const intlFormRef = useRef<FormRefType>(null);

  const handleSwitch = (form: 'local' | 'international') => {
    let hasValue = false;
    if (activeForm === 'local') {
      const localForm = localFormRef.current?.getFormValues?.();
      hasValue = localForm ? hasNonEmptyValue(localForm) : false;
    } else {
      const intlForm = intlFormRef.current?.getFormValues?.();
      hasValue = intlForm ? hasNonEmptyValue(intlForm) : false;
    }
    if (form !== activeForm && hasValue) {
      setPendingForm(form);
      setShowModal(true);
    } else {
      setActiveForm(form);
      onFormTypeChange?.(form);
    }
  };

  const handleDiscard = () => {
    if (activeForm === 'local') {
      localFormRef.current?.resetForm?.();
    } else {
      intlFormRef.current?.resetForm?.();
    }
    setActiveForm(pendingForm!);
    onFormTypeChange?.(pendingForm!);
    setShowModal(false);
    setPendingForm(null);
    return Promise.resolve();
  };

  const handleStay = () => {
    setShowModal(false);
    setPendingForm(null);
  };

  return (
    <div className="foreign_payouts_form">
      <div className="form-buttons">
        <div className="toggle-transfer">
          <button
            data-testid="local-form"
            type="button"
            className={`btn ${activeForm === 'local' ? ' btn-selected ' : ''}`}
            onClick={() => handleSwitch('local')}
          >
            Local Transfer
          </button>
          <button
            data-testid="intl-form"
            type="button"
            className={`btn ${activeForm === 'international' ? ' btn-selected ' : ''}`}
            onClick={() => handleSwitch('international')}
          >
            International Transfer
          </button>
        </div>
      </div>
      {activeForm === 'local' ? (
        <LocalTransferForm
          ref={localFormRef}
          currency={currency}
          countriesList={countries}
          statesList={states}
          onFormChange={onFormChange}
          onValidityChange={onValidityChange}
        />
      ) : (
        <InternationalTransferForm
          ref={intlFormRef}
          currency={currency}
          countriesList={countries}
          statesList={states}
          onFormChange={onFormChange}
          onValidityChange={onValidityChange}
        />
      )}

      <Modal
        size="md"
        close={handleStay}
        heading="Leaving the transfer form?"
        description={
          <p style={{ color: '#414F5F', fontWeight: 400, display: 'block' }}>
            Are you sure you want to leave this form? This would mean that you would lose your entered data as the session will not be
            saved. This action cannot be undone.
          </p>
        }
        content={null}
        firstButtonText="Stay on form"
        firstButtonAction={handleStay}
        secondButtonText="Yes, Leave"
        secondButtonAction={handleDiscard}
        secondButtonColor="#F32345"
        visible={showModal}
        secondButtonActionIsTerminal={false}
      />
    </div>
  );
};

export default ForeignPayouts;
