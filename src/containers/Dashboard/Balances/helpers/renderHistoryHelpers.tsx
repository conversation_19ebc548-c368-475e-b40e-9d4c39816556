import { Link } from 'react-router-dom';

import { BalanceHistoryType, CurrencyType, HistorySourceType, HistoryTabType, IResponse, LienDataType, ReserveHistoryType } from '+types';
import { capitalize, capitalizeRemovedash, formatAmount, getDate, getTime, switchStatus } from '+utils';

const lienStatus = {
  active: 'Lien Placed',
  withheld: 'Lien Debited',
  released: 'Lien Released'
};

const filterHistory = (type: HistorySourceType, description: string, reference: string, direction: 'credit' | 'debit') => {
  const notAvailable = [undefined, null, '', ' '];
  if (type === 'payout_reversal') {
    return <span>{description}</span>;
  }
  if (description?.startsWith('Settlement')) {
    return (
      <>
        Settlement for{' '}
        <Link to={`/dashboard/settlements/${reference}`} style={{ fontWeight: 600 }}>
          {reference?.toUpperCase()}
        </Link>
      </>
    );
  }
  if (description?.startsWith('Chargeback')) {
    let rcDescription;
    if (description?.includes('processed')) {
      rcDescription = description.split('processed for ');
    } else if (description?.includes('deduction')) {
      rcDescription = description.split('deduction for ');
    }
    return (
      <>
        {rcDescription?.[0]} deduction for -{' '}
        <Link to={`/dashboard/disputes/chargebacks/${reference}`} style={{ fontWeight: 600 }}>
          {reference?.toUpperCase()}
        </Link>
      </>
    );
  }
  if (description?.startsWith('Refund')) {
    const rcDescription = description.split('processed for ');
    return (
      <>
        {rcDescription[0]} processed for -{' '}
        <Link to={`/dashboard/disputes/refunds/${reference}`} style={{ fontWeight: 600 }}>
          {reference?.toUpperCase()}
        </Link>
      </>
    );
  }
  if (description?.startsWith('\n')) {
    const word = description.split('\n');
    const newDescription = word[1].trim();
    if (newDescription?.startsWith('overpayment') || newDescription?.startsWith('underpayment')) {
      const ovDescription = newDescription.split('for ');
      return (
        <>
          Reversal for <span style={{ textTransform: 'capitalize' }}>{ovDescription[0]}</span> processed for -{' '}
          <Link to={`/dashboard/pay-ins/${ovDescription[1]}`} style={{ fontWeight: 600 }}>
            {ovDescription[1]?.toUpperCase()}
          </Link>
        </>
      );
    }
  }
  if (description?.startsWith('Rolling')) {
    const rcDescription = description.split(new RegExp(' for ', 'i'));
    return (
      <>
        {capitalize(rcDescription[0].replace('Rolling', ''))}
        {' for '}
        <Link to={`/dashboard/settlements/${reference}`} style={{ fontWeight: 600 }}>
          {reference?.toUpperCase()}
        </Link>
      </>
    );
  }
  if (notAvailable.includes(description)) return 'Not Available';
  if (type === 'wallet_conversion') {
    return (
      <>
        {description} -{' '}
        <Link to={`/dashboard/conversions/${reference}`} style={{ fontWeight: 600 }}>
          {reference?.toUpperCase()}
        </Link>
      </>
    );
  }
  if (type === 'lien') {
    return (
      <>
        {description} -{' '}
        <Link to={`/dashboard/pay-ins/lien/${reference}`} style={{ fontWeight: 600 }}>
          {reference?.toUpperCase()}
        </Link>
      </>
    );
  }
  return (
    <>
      {description}
      {!notAvailable.includes(reference) && (
        <>
          {' - '}
          {['settlement', 'wallet_settlement_conversion'].includes(type) ? (
            <Link to={`/dashboard/settlements/${reference}`} style={{ fontWeight: 600 }}>
              {reference?.toUpperCase()}
            </Link>
          ) : (
            <Link to={`/dashboard/${direction === 'credit' ? 'pay-ins' : 'payouts'}/${reference}`} style={{ fontWeight: 600 }}>
              {reference?.toUpperCase()}
            </Link>
          )}
        </>
      )}
    </>
  );
};

const renderBalanceHistory = (activeList: IResponse<BalanceHistoryType[]>['data'], activeCurrency: CurrencyType) => {
  return activeList?.data?.map(each => (
    <div className="div-table --balance-history --row" key={each.created_at + each.source_reference + each.source_type}>
      <div>
        <span className="body-row-header">Date/Time:</span>
        <span>{getDate(each.history_date)}</span>
        <span className="annotation" style={{ marginLeft: '5px' }}>
          {each?.history_date ? getTime(each?.history_date) : 'N/A'}
        </span>
      </div>
      <div>
        <span className="body-row-header">Details:</span>
        <span className="trim">{filterHistory(each.source_type, each.description, each.source_reference, each.direction)}</span>
      </div>

      <div>
        <span className="body-row-header">Amount ({activeCurrency}):</span>
        <span style={{ fontWeight: 600, color: each.direction === 'debit' ? '#F32345' : '#24B314' }}>{`${
          each.direction === 'debit' ? '-' : '+'
        }${formatAmount(each.amount)}`}</span>
      </div>
      <div>
        <span className="body-row-header">Balance After ({activeCurrency}):</span>
        <span style={{ color: 'rgba(0, 10, 26, 0.4)' }}>{formatAmount(each.balance_after)}</span>
      </div>
    </div>
  ));
};

const renderReserveBalanceHistory = (reserveBalance: IResponse<ReserveHistoryType[]>['data'], activeCurrency: CurrencyType) => {
  return reserveBalance?.data?.map(each => (
    <div
      className="div-table --balance-history --row --reserve-history"
      key={each.transaction_reference + each.history_date + each.balance_after}
    >
      <div>
        <span className="body-row-header">Date/Time:</span>
        <span>{getDate(each.history_date)}</span>
        <span className="annotation" style={{ marginLeft: '5px' }}>
          {each?.history_date ? getTime(each?.history_date) : 'N/A'}
        </span>
      </div>
      <div>
        <span className="body-row-header">Details:</span>
        <span style={{ maxWidth: '500px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {filterHistory(each.source_type, each.description, each.transaction_reference, each.direction)}
        </span>
      </div>

      <div>
        <span className="body-row-header">Amount ({activeCurrency}):</span>
        <span style={{ fontWeight: 600, color: each.direction === 'debit' ? '#F32345' : '#24B314' }}>{`${
          each.direction === 'debit' ? '-' : '+'
        }${formatAmount(each.amount)}`}</span>
      </div>

      <div>
        <span className="body-row-header">Balance After ({activeCurrency}):</span>
        <span style={{ color: 'rgba(0, 10, 26, 0.4)' }}>{formatAmount(each.balance_after)}</span>
      </div>

      <div>
        <span className="body-row-header">Release Date:</span>
        <span style={{ color: 'rgba(0, 10, 26, 0.4)' }}>
          {each.source_type === 'rolling_reserve' || !each.available_on ? '--' : getDate(each.available_on)}
        </span>
      </div>
    </div>
  ));
};

const renderLienHistory = (lienData: IResponse<LienDataType[]>['data']) => {
  return lienData?.data?.map(each => (
    <div className="div-table --row --lien-history" key={each.reference}>
      <div>
        <span className="body-row-header">Status:</span>
        <span className={`status-pill smaller ${switchStatus(`lien_${each.status}`)}`} />
        <span>{capitalizeRemovedash(lienStatus[each.status])}</span>
      </div>
      <div>
        <span className="body-row-header">Lien ID:</span>
        <span>
          <Link to={`/dashboard/pay-ins/lien/${each.reference}`} style={{ fontWeight: 600 }}>
            {each.reference?.toUpperCase()}
          </Link>
        </span>
      </div>
      <div>
        <span className="body-row-header">Transaction ID:</span>
        <span>
          {each.source_reference ? (
            <Link to={`/dashboard/pay-ins/${each.source_reference}`} style={{ fontWeight: 600 }}>
              {each.source_reference?.toUpperCase()}
            </Link>
          ) : (
            'N/A'
          )}
        </span>
      </div>
      <div>
        <span className="body-row-header">Date/Time:</span>
        <span>{getDate(each.created_at)}</span>
        <span className="annotation" style={{ marginLeft: '5px' }}>
          {each?.created_at ? getTime(each?.created_at) : 'N/A'}
        </span>
      </div>
      <div>
        <span>
          {formatAmount(Number(each.amount))} {each.currency}
        </span>
      </div>
    </div>
  ));
};

export const renderTransactionHistory = (
  activeTab: HistoryTabType,
  data: IResponse<BalanceHistoryType[]>['data'] | IResponse<ReserveHistoryType[]>['data'] | IResponse<LienDataType[]>['data'],
  activeCurrency: CurrencyType
) => {
  switch (activeTab) {
    case 'Balance':
      return renderBalanceHistory(data as IResponse<BalanceHistoryType[]>['data'], activeCurrency);
    case 'Reserve':
      return renderReserveBalanceHistory(data as IResponse<ReserveHistoryType[]>['data'], activeCurrency);
    case 'Lien':
      return renderLienHistory(data as IResponse<LienDataType[]>['data']);
    default:
      return null;
  }
};
