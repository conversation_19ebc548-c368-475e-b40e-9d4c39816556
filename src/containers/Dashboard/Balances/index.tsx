/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { lazy, Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { AxiosError } from 'axios';

import Table from '+dashboard/Shared/Table';
import { useBanks, usePermissions, useSearchQuery } from '+hooks';
import useFeedbackHandler from '+hooks/feedbackHandler';
import useExportDownloader from '+hooks/useExportDownloader';
import { BalanceServices } from '+services/balance-services';
import { MerchantServices } from '+services/merchant-services';
import { SettlementServices } from '+services/settlement-services';
import { TransactionServices } from '+services/transaction-services';
import useStore from '+store';
import Storage from '+store/storage';
import {
  BalanceHistoryType,
  BalancesDataType,
  CurrencyType,
  ExportActionType,
  FetchBanksResponseType,
  HistoryTabType,
  IResponse,
  LienDataType,
  LienSummaryType,
  ReserveHistoryType,
  VirtualAccount
} from '+types';
import { APIDownload, history, swapArrayPositions, switchCurrency } from '+utils';

import CurrencyTabs from '../Shared/CurrencyTabs';
import HeaderTabs from '../Shared/HeaderTabs';
import LoadingPlaceholder from '../Shared/LoadingPlaceholder';
import AddFunds from './components/AddFunds';
import LienInfo from './components/LienInfo';
import Withdraw from './components/Withdraw';
import { BalanceSummaryItem, exportServices, VirtualizedTableRows } from './helpers/BalancesHelper';
import { emptyStateMessage, renderTableHeadings, tableAnnotation, tableClassName } from './helpers/historyTableHelpers';

import './index.scss';

const ExportFilterModal = lazy(() => import('+dashboard/Shared/ExportFilterModal'));
const AdvanceExportModal = lazy(() => import('../Shared/AdvanceExportModal'));
const ConvertFunds = lazy(() => import('./components/ConvertFunds'));

function Balances() {
  const location = useLocation();
  const searchQuery = useSearchQuery<{ currency: CurrencyType; page: string; limit: string; tab: HistoryTabType }>();
  const userAccess = usePermissions('balance');
  Storage.setItem('saveRole', location.state as string);

  const { profile, merchantBalanceDetails } = useStore();
  const [showLargeExportModal, setLargeExportModal] = useState(false);

  const { feedbackInit } = useFeedbackHandler();
  const [balances, setBalances] = useState<BalancesDataType | object>({});
  const [modalStates, setModalStates] = useState({
    withdrawModal: false,
    fundsModal: false,
    exportModal: false,
    convertFundsModal: false
  });

  const [exportParams, setExportParams] = useState<{
    dateFrom?: string;
    dateTo?: string;
    currency: CurrencyType;
    format: ExportActionType['format'];
    fieldToExport: ExportActionType['fieldsToExport'];
    sortingParams?: string;
  } | null>(null);
  const activeTab = searchQuery.value?.tab ?? 'Balance';
  const activeCurrency = searchQuery.value?.currency ?? 'NGN';
  const currentPage = searchQuery.value?.page ?? '1';
  const limit = searchQuery.value?.limit ?? '10';

  const defaultMerchant = useStore(state => state.defaultMerchant);
  const availableCurrencies = useStore(state => state.availableCurrencies) as CurrencyType[];
  const MERCHANT_ENV = useStore(state => state.merchantEnv);
  const defaultCurrency = useStore(state => state.defaultCurrency);
  const [settlementAccounts, setSettlementAccounts] = useState<Record<string, unknown[]> | null>(null);
  const [tabs, setTabs] = useState<HistoryTabType[]>(['Balance', 'Reserve', 'Lien']);

  const showLien = MERCHANT_ENV === 'live' && availableCurrencies.includes(activeCurrency) && activeTab === 'Lien';

  const { data: banks, isFetching } = useBanks(activeCurrency);

  useEffect(() => {
    if (availableCurrencies.includes(activeCurrency)) {
      setTabs(['Balance', 'Reserve', 'Lien']);
    } else {
      setTabs(['Balance', 'Reserve']);
      if (activeTab === 'Lien') {
        searchQuery.setQuery({ tab: 'Balance' });
      }
    }
  }, [activeCurrency]);

  const { data: virtualAccount, refetch: refetchVirtualAccount } = MerchantServices.useFetchMerchantVirtualAccount({
    enabled: MERCHANT_ENV === 'live',
    showErrorMessage: false,
    onError: e => {
      const err = e as AxiosError<{ message: string }>;
      const error = err.response?.data;
      if (err?.response?.status !== 404) {
        feedbackInit({
          message: error?.message ?? 'There has been an error in getting your reserved bank account details',
          type: 'danger',
          action: {
            action: () => refetchVirtualAccount(),
            name: 'Try again'
          }
        });
      }
    }
  });

  const { isLoading: fetchingBalances, refetch: refetchBalance } = BalanceServices.useGetBalances({
    showErrorMessage: false,
    onSuccess: data => setBalances(data?.data || {}),
    onError: e => {
      const err = e as AxiosError<{ message: string }>;
      const error = err.response?.data;
      const notLive = error?.message?.includes('not been approved');
      const message = notLive ? 'Your account has not been approved yet' : 'There has been an error in getting your balances';
      feedbackInit({
        message,
        type: 'danger',
        action: {
          action: () => (notLive ? history.push('/dashboard/settings/business') : refetchBalance()),
          name: notLive ? 'Complete account setup' : 'Try again'
        }
      });
    }
  });
  const { data: reserveData, isLoading: reserveIsLoading } = BalanceServices.useGetRollingReserveHistory({
    currency: activeCurrency,
    params: { page: currentPage, limit },
    enabled: Object.hasOwn(merchantBalanceDetails?.[activeCurrency] ?? {}, 'reserve_balance'),
    refetchOnCloseFeedbackError: true
  });

  const { data: balanceHistoryData, isLoading: balanceHistoryIsLoading } = BalanceServices.useGetBalanceHistory({
    currency: activeCurrency,
    params: { page: currentPage, limit },
    refetchOnCloseFeedbackError: true
  });

  SettlementServices.useGetSettlementAccount({
    onSuccess: res => {
      let accountBuffer: Record<string, unknown[]> = {};
      Object.keys(res?.data || {}).forEach(key => {
        const filteredAccount = res.data[key].filter(x => x.status === 'active');
        accountBuffer = { ...accountBuffer, ...{ [`${key}`]: filteredAccount } };
      });

      setSettlementAccounts(accountBuffer);
    }
  });

  const { data: lienHistoryData, isLoading: lienHistoryIsLoading } = TransactionServices.useGetLiens({
    enabled: showLien,
    params: { currency: activeCurrency, page: currentPage, limit }
  });

  const { data: lienSummaryData } = TransactionServices.useGetLienSummary({
    enabled: showLien,
    params: { currency: activeCurrency }
  });

  const { getDownload } = useExportDownloader();

  useEffect(() => {
    void getDownload();
  }, []);

  const reserveBalance = (reserveData as { data: IResponse<ReserveHistoryType[]>['data'] })?.data;

  const balanceHistory = (balanceHistoryData as { data: IResponse<BalanceHistoryType[]>['data'] })?.data;

  const lienHistory = (lienHistoryData as { data: IResponse<LienDataType[]>['data'] })?.data;

  const lienSummary = (lienSummaryData as { data: { data: LienSummaryType } })?.data?.data;

  const isLoading = {
    Balance: balanceHistoryIsLoading,
    Reserve: reserveIsLoading,
    Lien: lienHistoryIsLoading
  };

  const exportHistory = (
    format: ExportActionType['format'],
    fieldToExport: ExportActionType['fieldsToExport'],
    from?: string,
    to?: string
  ) => {
    const parameterizeArray = (key: string, arr: string[]) => {
      arr = arr.map(encodeURIComponent);
      return arr.join(`&${key}[]=`);
    };

    setExportParams({
      currency: activeCurrency,
      format,
      fieldToExport,
      dateFrom: from,
      dateTo: to,
      sortingParams: parameterizeArray('fieldsToExport', Array.isArray(fieldToExport) ? fieldToExport : [fieldToExport])
    });
  };

  const handleTimeDescription = () => {
    if (!exportParams?.dateFrom && !exportParams?.dateTo) return 'of all time';
    if (!exportParams?.dateFrom && exportParams?.dateTo) return `up to ${exportParams?.dateTo}`;
    if (exportParams?.dateFrom && !exportParams?.dateTo) return `from ${exportParams?.dateFrom}`;
    return `from: ${exportParams?.dateFrom} to: ${exportParams?.dateTo}`;
  };

  const { isLoading: exportIsLoading } = BalanceServices[
    exportServices[activeTab as keyof typeof exportServices] as keyof typeof BalanceServices
  ]({
    enabled: Boolean(exportParams),
    params: exportParams,
    onSuccess: res => {
      setExportParams(null);
      if ((res as { status: number }).status === 202) {
        setLargeExportModal(true);
      } else {
        const type = exportParams?.format === 'csv' ? 'csv' : 'xlsx';
        APIDownload(res, `${activeCurrency} ${activeTab} history ${handleTimeDescription()}`, type);
        feedbackInit({
          message: `${switchCurrency[activeCurrency]} ${activeTab} successfully downloaded`,
          type: 'success'
        });
      }
      setModalStates({ ...modalStates, exportModal: false });
    },
    onError: () => {
      setExportParams(null);
    }
  });

  const { data: countResponse } = TransactionServices.useGetTransactionsCount({
    kind: 'payouts',
    shouldKeepPreviousData: true,
    params: { currency: activeCurrency }
  });

  const count = (countResponse as { data: { count: number } })?.data;

  const handleWithdrawButtonStatus = useCallback(
    (currentCurrency: CurrencyType) => {
      if ((balances[currentCurrency as keyof typeof balances] as BalancesDataType[keyof BalancesDataType])?.available_balance <= 0) {
        return true;
      }
      return !!(!defaultMerchant?.payout_limits?.disbursement_wallet?.USD?.min && currentCurrency === 'USD');
    },
    [balances, defaultMerchant?.payout_limits?.disbursement_wallet?.USD?.min]
  );

  const renderConvertFunds = useMemo(() => {
    return ['NGN', 'USD'].includes(activeCurrency);
  }, [activeCurrency]);

  const availableBalances = useMemo(() => {
    if (!balances || Object.keys(balances).length === 0) return [];
    return Object.keys(balances);
  }, [balances]);

  const currentDataMemo = useMemo(
    () => ({
      Balance: balanceHistory,
      Reserve: reserveBalance,
      Lien: lienHistory
    }),
    [balanceHistory, reserveBalance, lienHistory]
  );

  const setSearchQuery = useCallback(
    (value: CurrencyType) => {
      if (!availableCurrencies?.includes(value)) return;
      searchQuery.setQuery({ currency: value, page: '1' });
    },
    [availableCurrencies, searchQuery]
  );

  const setTabQuery = useCallback(
    (value: HistoryTabType) => {
      searchQuery.setQuery({ tab: value, currency: activeCurrency }, true);
    },
    [searchQuery, activeCurrency]
  );

  return (
    <section className="balances__page">
      <section className="os-tabs-w">
        {!fetchingBalances && (
          <div className="os-tabs-controls os-tabs-complex">
            <CurrencyTabs
              currencies={availableBalances}
              activeCurrency={activeCurrency}
              onClick={(value: string) => setSearchQuery(value as CurrencyType)}
            />
          </div>
        )}
      </section>
      <section className="history_summary_details mt-4">
        <div className="header-container">
          <div className="flex-1">
            <section className="balances__summary">
              {useMemo(() => {
                return (
                  swapArrayPositions(
                    Object.keys((balances as BalancesDataType)?.[activeCurrency] || {}),
                    0,
                    1
                  ) as (keyof BalancesDataType[keyof BalancesDataType])[]
                ).map((balance: keyof BalancesDataType[keyof BalancesDataType]) => (
                  <BalanceSummaryItem
                    key={balance}
                    balance={balance}
                    activeCurrency={activeCurrency}
                    balanceData={(balances as BalancesDataType)?.[activeCurrency]}
                  />
                ));
              }, [balances, activeCurrency])}
            </section>
          </div>

          <div className="history_summary_heading">
            <div className="__buttons">
              <AddFunds
                disabled={!['NGN'].includes(activeCurrency)}
                virtualAccount={(virtualAccount as { data: VirtualAccount })?.data || {}}
                visible={modalStates.fundsModal}
                close={() => setModalStates({ ...modalStates, fundsModal: true })}
                currency={activeCurrency}
                minWalletPayoutLimit={
                  defaultMerchant?.payout_limits?.disbursement_wallet?.[activeCurrency]?.min ||
                  (['NGN'].includes(activeCurrency) ? 1000 : 100)
                }
                balances={balances as BalancesDataType}
                refetchBalance={refetchBalance as () => void}
                walletPayoutLimit={defaultMerchant?.payout_limits?.disbursement_wallet?.[activeCurrency]?.max as number}
                availableCurrency={defaultMerchant?.available_currency ?? []}
              />
              <div className="balance-buttons">
                {defaultMerchant?.available_currency?.includes(activeCurrency) && userAccess === 'manage' && (
                  <Withdraw
                    banks={(banks as unknown as IResponse<FetchBanksResponseType[]>['data'])?.data || []}
                    currency={activeCurrency}
                    balances={balances as BalancesDataType}
                    refetchBalance={refetchBalance as () => void}
                    isFetchingBanks={isFetching}
                    minBankPayoutLimit={defaultMerchant?.payout_limits?.bank_account?.[activeCurrency]?.min || 1000}
                    minWalletPayoutLimit={
                      defaultMerchant?.payout_limits?.disbursement_wallet?.[activeCurrency]?.min ??
                      (['NGN'].includes(activeCurrency) ? 1000 : 100)
                    }
                    minMobileMoneyLimit={defaultMerchant?.payout_limits?.mobile_money?.[activeCurrency]?.min ?? 100}
                    bankPayoutLimit={defaultMerchant?.payout_limits?.bank_account?.[activeCurrency]?.max ?? 5000000}
                    walletPayoutLimit={defaultMerchant?.payout_limits?.disbursement_wallet?.[activeCurrency]?.max as number}
                    mobileMoneyLimit={defaultMerchant?.payout_limits?.mobile_money?.[activeCurrency]?.max ?? 150000}
                    disabled={handleWithdrawButtonStatus(activeCurrency)}
                    payoutLimitDetails={defaultMerchant?.payout_limits}
                    bankWithdrawalLimit={defaultMerchant?.withdrawal_limits?.bank_account?.[activeCurrency] || {}}
                    settlementAccounts={settlementAccounts as Record<string, unknown[]>}
                    balanceCount={count?.count || 0}
                  />
                )}
                <Suspense fallback={<LoadingPlaceholder type="text" content={1} rows={1} />}>
                  {renderConvertFunds && (
                    <ConvertFunds
                      visible={modalStates.convertFundsModal}
                      close={() => setModalStates({ ...modalStates, convertFundsModal: true })}
                      currency={activeCurrency}
                      convertFundLimits={defaultMerchant?.conversion_limits}
                      defaultCurrency={defaultCurrency}
                    />
                  )}
                </Suspense>
              </div>
            </div>
          </div>
        </div>
      </section>
      {useMemo(() => {
        if (!(lienSummary?.count > 0 && availableCurrencies.includes(activeCurrency))) return null;
        return <LienInfo data={lienSummary} currency={activeCurrency} />;
      }, [lienSummary?.count, activeCurrency, lienSummary])}

      <section className="os-tabs-w">
        <div className="os-tabs-controls os-tabs-complex balances__history-tabs">
          <HeaderTabs
            tabs={tabs}
            activeTab={activeTab}
            onClick={(value: string) => setTabQuery(value as HistoryTabType)}
            suffix="history"
            filterTabs={tab =>
              !(tab === 'Reserve' && !Object.hasOwn((balances as BalancesDataType)[activeCurrency] || {}, 'reserve_balance'))
            }
          />
          <div className="balances__history-export-button">
            <div>
              {(userAccess === 'manage' || userAccess === 'export') && (
                <button
                  type="button"
                  className="btn btn-secondary"
                  style={{ background: 'none', border: 'none', color: '#2376F3' }}
                  onClick={() => setModalStates({ ...modalStates, exportModal: true })}
                >
                  <i className="os-icon os-icon-arrow-up-right" />
                  <span>Export History</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </section>
      <Table
        hasPagination
        tableClassName={`${tableClassName[activeTab]}`}
        headings={renderTableHeadings(activeTab, activeCurrency)}
        totalItems={currentDataMemo[activeTab]?.paging?.total_items ?? 0}
        pageSize={currentDataMemo[activeTab]?.paging?.page_size ?? 0}
        loading={isLoading[activeTab]}
        current={parseInt(currentPage, 10)}
        limitAction={c => searchQuery.setQuery({ limit: String(c) })}
        actionFn={current => searchQuery.setQuery({ page: String(current) })}
        annotation={tableAnnotation[activeTab]}
        emptyStateHeading="No entries yet"
        emptyStateMessage={emptyStateMessage[activeTab]}
        tableWrapperClassName="element-box-tp"
      >
        <VirtualizedTableRows data={currentDataMemo[activeTab]} type={activeTab} currency={activeCurrency} />
      </Table>

      {modalStates.exportModal && (
        <Suspense fallback={<LoadingPlaceholder type="text" content={1} rows={1} />}>
          <AdvanceExportModal
            openExport={modalStates.exportModal}
            setOpenExport={() => setModalStates({ ...modalStates, exportModal: false })}
            exportAction={exportHistory}
            type={activeTab === 'Lien' ? 'liens' : 'balances'}
            dateRange
            showSuccessModal={false}
            isLoading={exportIsLoading}
          />
        </Suspense>
      )}

      {showLargeExportModal && (
        <Suspense fallback={<LoadingPlaceholder type="text" content={1} rows={1} />}>
          <ExportFilterModal close={() => setLargeExportModal(false)} email={profile.email} visible={showLargeExportModal} />
        </Suspense>
      )}
    </section>
  );
}

export default Balances;
