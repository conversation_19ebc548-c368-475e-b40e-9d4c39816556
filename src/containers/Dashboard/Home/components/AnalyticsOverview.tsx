import { AnalyticsServices } from '+services/analytics-services';
import { durationToday, formatAmount } from '+utils';

interface IAnalyticsOverviewProps {
  currency: string;
}

interface IAnalyticsOverviewData {
  payinsTotal?: number;
  payoutsTotal?: number;
  allTransactionsCount?: number;
  [key: string]: unknown;
}

const AnalyticsOverview = ({ currency }: IAnalyticsOverviewProps) => {
  const [startDate, endDate] = durationToday();

  const hookResult = AnalyticsServices.useGetPerformanceAnalytics({
    currency: currency.toLowerCase(),
    params: { startDate, endDate }
  });
  const data = hookResult?.data as { data?: IAnalyticsOverviewData } | undefined;
  const overview: IAnalyticsOverviewData = data?.data || {};

  return (
    <section className="element-analytics">
      <div className="element-analytics-header">
        <p>Today&apos;s Activity</p>
      </div>
      <div className="element-box analytics-overview__comp">
        <div className="el-tablo">
          <div className="value">{formatAmount(overview.payinsTotal || 0)}</div>
          <div className="label">Received Today ({currency})</div>
        </div>
        <div className="el-tablo">
          <div className="value">{formatAmount(overview.payoutsTotal || 0)}</div>
          <div className="label">Sent Today ({currency})</div>
        </div>
        <div className="el-tablo">
          <div className="value">{overview.allTransactionsCount || '--'}</div>
          <div className="label">Total Transactions</div>
        </div>
      </div>
    </section>
  );
};

export default AnalyticsOverview;
