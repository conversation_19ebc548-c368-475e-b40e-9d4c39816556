import { useState } from 'react';
import { useParams } from 'react-router-dom';

import { hasPayoutApproverAccess } from '+containers/Dashboard/Settings/MultiLevelApprovals/utils';
import { AuthServices } from '+services/auth-services';
import { ApprovalServices } from '+services/multilevel-approval-services';
import { TransactionServices } from '+services/transaction-services';
import { history, switchTrxnMessage } from '+utils';

const usePayOutDetails = () => {
  const { id } = useParams<{ id: string }>();
  const [modalType, setModalType] = useState('');

  const { data, isLoading, refetch } = TransactionServices.useGetSingleTransaction({
    kind: 'payouts',
    onError: () => history.goBack(),
    errorMessage: `There has been an error fetching the details for the transaction: ${id.toUpperCase()}.`,
    ref: id
  });

  const { data: userData } = AuthServices.useBootstrap({});
  const { data: approvalWorkflows } = ApprovalServices.useGetApprovalWorkflows({});

  const singleApproverAccess = hasPayoutApproverAccess(userData, approvalWorkflows, 'single_payout');

  const trxnStatusObj = switchTrxnMessage[data?.data?.status ?? ('processing' as keyof typeof switchTrxnMessage)];

  return { trxnStatusObj, modalType, setModalType, isLoading, data: data?.data, refetch, singleApproverAccess };
};

export default usePayOutDetails;
