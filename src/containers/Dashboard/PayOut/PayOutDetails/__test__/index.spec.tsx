import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import PayOutDetails from '../index';

const MockPayOutDetails = () => {
  return (
    <MockIndexWithRoute route="/:id" initialEntries={['/test']}>
      <PayOutDetails />
    </MockIndexWithRoute>
  );
};

describe('Payout Details Test', () => {
  test('PayOutDetails is accessible', async () => {
    const { container } = render(<MockPayOutDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  describe('Test for PayOutDetails Heading Summary', () => {
    test('Renders PayOutDetails Heading Summary', async () => {
      render(<MockPayOutDetails />);

      expect(await screen.findByTestId('transaction-details-header')).toBeInTheDocument();

      const heading = within(screen.getByTestId('transaction-details-header'));

      // Summation of the amount and fee results to 3,080.63
      expect(await heading.findByText(/3,080.63/i)).toBeInTheDocument();
      expect(heading.getAllByText(/NGN/i)).toHaveLength(3);
      expect(heading.getByText(/transaction successful/i)).toBeInTheDocument();

      expect(heading.getAllByText(/net amount/i)).toHaveLength(1);
      expect(heading.getByText(/3,000/i)).toBeInTheDocument();

      expect(heading.getAllByText(/fee/i)).toHaveLength(1);
      expect(heading.getByText(/80.63 NGN/i)).toBeInTheDocument();

      expect(heading.getByText(/date completed/i)).toBeInTheDocument();
      expect(heading.getByText(/18 feb 2024, 2:52 AM/i)).toBeInTheDocument();

      expect(heading.getByText(/transaction id/i)).toBeInTheDocument();
      expect(await heading.findByText(/payout-ref/i)).toBeInTheDocument();

      expect(heading.getByText(/view receipt/i)).toBeInTheDocument();
    });
  });

  describe('Test for Payout Receipt Section', () => {
    test('Renders correct details in the view receipt modal', async () => {
      vi.mock('@react-pdf/renderer', async () => {
        const original = await vi.importActual('@react-pdf/renderer');

        return {
          ...original,
          usePDF: () => [{ blob: null }, false]
        };
      });

      render(<MockPayOutDetails />);

      await userEvent.click(await screen.findByText(/view receipt/i));

      expect(await screen.findByTestId('modal')).toBeInTheDocument();

      const modal = within(await screen.findByTestId('modal'));

      expect(modal.getByText(/receipt for undefined/i)).toBeInTheDocument();
      expect(modal.getByText(/NGN 3,000.00/i)).toBeInTheDocument();
      expect(modal.getByText(/your payment has been successfully processed/i)).toBeInTheDocument();

      expect(modal.getByText(/transaction summary/i)).toBeInTheDocument();

      expect(modal.getByText(/amount/i)).toBeInTheDocument();
      expect(modal.getAllByText(/3,000.00/i)).toHaveLength(2);

      expect(modal.getByText(/customer name/i)).toBeInTheDocument();
      expect(modal.getByText(/user/i)).toBeInTheDocument();

      expect(modal.getByText(/date completed/i)).toBeInTheDocument();
      expect(modal.getByText(/18 feb 2024, 2:52 AM/i)).toBeInTheDocument();

      expect(modal.getByText(/transaction type/i)).toBeInTheDocument();
      expect(modal.getByText(/bank transfer/i)).toBeInTheDocument();

      expect(modal.getByText(/account number/i)).toBeInTheDocument();
      expect(modal.getByText(/*********/i)).toBeInTheDocument();

      expect(modal.getAllByText(/bank/i)).toHaveLength(3);
      expect(modal.getByText(/access bank nigeria/i)).toBeInTheDocument();

      expect(modal.getByText(/trace ID/i)).toBeInTheDocument();
      expect(modal.getByText(/54321/i)).toBeInTheDocument();

      expect(modal.getByText(/payout reference/i)).toBeInTheDocument();
      expect(await modal.findByText(/payout-ref/i)).toBeInTheDocument();

      expect(modal.getByText(/description/i)).toBeInTheDocument();
      expect(modal.getByText(/test transfer/i)).toBeInTheDocument();

      expect(modal.getByText(/powered by/i)).toBeInTheDocument();

      expect(modal.getByText(/close/i)).toBeInTheDocument();
      expect(modal.getByText(/download/i)).toBeInTheDocument();

      await userEvent.click(modal.getByText(/close/i));

      expect(modal.queryByTestId('modal')).not.toBeInTheDocument();
    });
  });

  describe('Test for More Transaction Details Section in Payout Details', () => {
    test('Renders More Transaction Details Section in Payout Details', async () => {
      render(<MockPayOutDetails />);
      expect(await screen.findByTestId('payout-more-transaction-details')).toBeInTheDocument();
    });

    test('Renders More Transaction Details Section in Payout Details', async () => {
      render(<MockPayOutDetails />);
      const moreDetails = within(await screen.findByTestId('payout-more-transaction-details'));

      expect(moreDetails.getAllByText(/more transaction details/i)).toHaveLength(2);

      expect(moreDetails.getByText(/status/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/success/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/trace id/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/54321/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/amount charged/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/3,080.63 NGN/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/currency charged/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/Naira/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/Fee bearer/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/merchant/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/date created/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/18 feb 2024, 2:52 AM/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/date completed/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/18 feb 2024, 2:52 AM/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/bulk id/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/test-payouts-batch/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/kora reference/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/kora-ref/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/description/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/Test transfer/i));
    });
  });

  describe('Test for Recipient Information Section in Payout Details', () => {
    test('Renders Recipient Information Section in Payout Details', async () => {
      render(<MockPayOutDetails />);
      expect(await screen.findByTestId('recipient-information-section')).toBeInTheDocument();
    });

    test('Renders More Transaction Details Section in Payout Details', async () => {
      render(<MockPayOutDetails />);
      const info = within(await screen.findByTestId('recipient-information-section'));

      expect(info.getByText(/payment method/i)).toBeInTheDocument();
      expect(info.getByText(/bank transfer/i)).toBeInTheDocument();

      expect(info.getByText('Bank')).toBeInTheDocument();
      expect(info.getByText(/Access Bank Nigeria/i)).toBeInTheDocument();

      expect(info.getByText(/account number/i)).toBeInTheDocument();
      expect(info.getByText(/**********/i)).toBeInTheDocument();

      expect(info.getByText(/account name/i)).toBeInTheDocument();
      expect(info.getByText(/user/i)).toBeInTheDocument();

      expect(info.getByText(/Email/i)).toBeInTheDocument();
      expect(info.getByText(/<EMAIL>/i)).toBeInTheDocument();
    });
  });
});
