/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { PayOutMappingTypes } from '+types';

import ApprovePayoutModal from '../components/ApprovePayoutModal';

const mockProps = {
  onClose: vi.fn(),
  authState: { two_factor_type: 'otp' as const, code: '7629', identifier: 'e1ad01d3-ed5b-40f3-b855-e9ec809c91c5' },
  otp: '',
  setOtp: vi.fn(),
  amount: 1350,
  currency: 'NGN',
  destination: {
    type: 'bank_account' as PayOutMappingTypes,
    details: {
      customer_name: 'O<PERSON>ayowa Kayode',
      bank_name: '<PERSON><PERSON>',
      account_number: '**********',
      account_name: 'Test account'
    }
  },
  refetchPayout: vi.fn(),
  authorizeApprovePayout: vi.fn(),
  close: vi.fn()
};

const MockApprovePayout = () => {
  return (
    <MockIndex>
      <ApprovePayoutModal {...mockProps} />
    </MockIndex>
  );
};

describe('ApprovePayoutModal', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<MockApprovePayout />);

    await waitFor(async () => {
      const result = await axe(container);
      expect(result).toHaveNoViolations();
    });
  });

  it('renders the modal with correct content', () => {
    render(<MockApprovePayout />);
    expect(screen.getByText('Some important details of your payout:')).toBeInTheDocument();
    expect(screen.getByText('Total Amount')).toBeInTheDocument();
    expect(screen.getByText('1,350.00')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Bank Account')).toBeInTheDocument();
  });

  it('enables the second button when OTP is valid', () => {
    render(<MockApprovePayout />);
    const secondButton = screen.getByText('Confirm');
    expect(secondButton).not.toBeDisabled();
  });

  it('calls setOtp when OTP input changes with values', async () => {
    render(<MockApprovePayout />);
    const otpInput = screen.getByPlaceholderText('Enter verification code');
    await userEvent.type(otpInput, '7629');

    expect(mockProps.setOtp).toHaveBeenCalledTimes(4);
    expect(mockProps.setOtp).toHaveBeenNthCalledWith(1, '7');
    expect(mockProps.setOtp).toHaveBeenNthCalledWith(2, '6');
    expect(mockProps.setOtp).toHaveBeenNthCalledWith(3, '2');
    expect(mockProps.setOtp).toHaveBeenNthCalledWith(4, '9');
  });

  test('close when "Cancel" button is clicked', async () => {
    render(<MockApprovePayout />);
    await userEvent.click(screen.getByText(/Back/));
    await waitFor(() => {
      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });
});
