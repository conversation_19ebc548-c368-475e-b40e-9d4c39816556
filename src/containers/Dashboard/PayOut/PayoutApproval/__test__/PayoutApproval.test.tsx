/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { QueryObserverResult } from '@tanstack/react-query';
import { render, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { DestinationType } from '+types';

import PayoutApproval from '..';

type ApprovalType = {
  payoutDecline: boolean;
  payoutApprove: boolean;
};

type PayloadType = {
  [Property in keyof ApprovalType]?: ApprovalType[Property];
};

const mockProps: {
  event: ApprovalType;
  updateEvent: React.Dispatch<PayloadType>;
  reference: string;
  refetchPayout: () => Promise<QueryObserverResult<unknown, Error>>;
  amount: number;
  currency: string;
  destination: DestinationType;
} = {
  event: {
    payoutDecline: false,
    payoutApprove: false
  },
  updateEvent: vi.fn(),
  reference: 'KPY-PO-202503210951KJ4o9J05017',
  refetchPayout: vi.fn(),
  amount: 1000,
  currency: 'NGN',
  destination: {} as DestinationType
};

const MockedPayoutApproval = () => {
  return (
    <MockIndex>
      <PayoutApproval {...mockProps} />
    </MockIndex>
  );
};

describe('PayoutApproval Component', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<MockedPayoutApproval />);

    await waitFor(async () => {
      const result = await axe(container);
      expect(result).toHaveNoViolations();
    });
  });
});
