/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { cleanInput } from '+utils';

import DeclinePayoutModal from '../components/DeclinePayoutModal';

const mockProps = {
  onClose: vi.fn(),
  reference: 'KPY-PO-202503210951KJ4o9J05017',
  initiateDeclineSinglePayout: vi.fn().mockResolvedValue({ success: true }),
  authorizeDelinePayout: vi.fn(),
  authState: { two_factor_type: 'otp' as const, code: '7429', identifier: 'e1ad01d3-ed5b-40f3-b855-e9ec809c91c5' },
  otp: '',
  setOtp: vi.fn(),
  declinedReason: 'Payout declined due to insufficient balance.',
  handleDeclinePayout: vi.fn(reason => {
    mockProps.declinedReason = reason;
  }),
  refetchPayout: vi.fn(),
  amount: 4500,
  currency: 'NGN'
};

const MockDeclinePayout = () => {
  return (
    <MockIndex>
      <DeclinePayoutModal {...mockProps} />
    </MockIndex>
  );
};

describe('DeclinePayoutModal', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<MockDeclinePayout />);

    await waitFor(async () => {
      const result = await axe(container);
      expect(result).toHaveNoViolations();
    });
  });

  it('renders the modal with correct content', () => {
    render(<MockDeclinePayout />);
    expect(screen.getByText('Confirm Payout Decline')).toBeInTheDocument();
    expect(screen.getByText(/declining means the transaction would not be processed. This action cannot be undone./)).toBeInTheDocument();
    expect(screen.getByText('4,500.00 NGN')).toBeInTheDocument();
  });

  it('calls handle function to authorize decline payout', async () => {
    render(<MockDeclinePayout />);

    const descriptionInput = screen.getByPlaceholderText('Maximum 150 characters');

    const inputValue = 'Payout declined due to insufficient balance..';
    const sanitizeTestValue = cleanInput(inputValue).trim().replace(/\.+$/, '');

    await userEvent.clear(descriptionInput);
    await userEvent.type(descriptionInput, inputValue);

    await waitFor(() => {
      expect(mockProps.handleDeclinePayout).toHaveBeenLastCalledWith(sanitizeTestValue);
    });

    expect(mockProps.declinedReason).toBe(sanitizeTestValue);
  });

  it('renders the OTP input field when the OTP stage is active', async () => {
    mockProps.declinedReason = 'Payou declined';

    render(<MockDeclinePayout />);

    const nextButton = screen.getByText('Next');
    expect(nextButton).not.toBeDisabled();
    await userEvent.click(nextButton);

    const otpInput = await screen.findByPlaceholderText('Enter verification code');
    expect(otpInput).toBeInTheDocument();
  });

  test('close when "Cancel" button is clicked', async () => {
    render(<MockDeclinePayout />);
    await userEvent.click(screen.getByText(/Close/));
    await waitFor(() => {
      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });
});
