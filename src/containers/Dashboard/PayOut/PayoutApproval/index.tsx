import React, { useEffect, useState } from 'react';

import { useOTPAuth } from '+hooks';
import { ApprovalServices } from '+services/multilevel-approval-services';
import { IPayoutApprovalProps } from '+types';

import ApprovePayoutModal from './components/ApprovePayoutModal';
import DeclinePayoutModal from './components/DeclinePayoutModal';

const PayoutApproval: React.FC<IPayoutApprovalProps> = ({ modalType, data, close, refetch }) => {
  const { authState, updateAuthState, updateCountdownStatus, resendOTP } = useOTPAuth();
  const [paymentReference, setPaymentReference] = useState('');
  const [declinedReason, setDeclinedReason] = useState('');
  const [otp, setOtp] = useState('');

  const { amount, currency, reference, destination, approvalWorkFlowDetails } = data || {};

  const { mutateAsync: initiateSinglePayoutApproval } = ApprovalServices.useInitiateSinglePayoutApproval({
    onSuccess: ({ data }) => {
      const { payment_reference, authData } = data;
      const { identifier, type: two_factor_type } = authData;

      updateAuthState({ ...authState, ...authData, identifier, two_factor_type });
      setPaymentReference(payment_reference);
    },
    showErrorMessage: true,
    showSuccessMessage: false,
    errorMessageBannerLevel: true
  });

  const { mutateAsync: authorizeDecline } = ApprovalServices.useAuthorizeDeclineSinglePayout({
    showErrorMessage: true,
    showSuccessMessage: true,
    errorMessageBannerLevel: true,
    successMessageBannerLevel: false
  });
  const { mutateAsync: authorizeApprovePayout } = ApprovalServices.useAuthorizeApproveSinglePayout({
    showErrorMessage: true,
    showSuccessMessage: true,
    errorMessageBannerLevel: true,
    successMessageBannerLevel: false
  });

  const authorizeDeclinePayout = async () => {
    await authorizeDecline({
      payment_reference: paymentReference,
      declined_reason: declinedReason,
      auth_data: {
        two_factor_type: authState.two_factor_type,
        identifier: authState.identifier,
        code: otp
      }
    });
  };

  const authorizeApprove = async () => {
    await authorizeApprovePayout({
      payment_reference: paymentReference,
      auth_data: {
        two_factor_type: authState.two_factor_type,
        identifier: authState.identifier,
        code: otp
      }
    });
  };

  const handleDeclinePayout = (reason: string) => {
    setDeclinedReason(reason.replace(/\n+$/, ''));
  };

  const handleOtpResend = async () => {
    updateCountdownStatus(false);
    await resendOTP();
  };

  useEffect(() => {
    if (modalType === 'approve') {
      initiateSinglePayoutApproval({ reference: data?.reference });
    }
  }, [modalType]);

  return (
    <>
      {modalType === 'decline' && (
        <DeclinePayoutModal
          reference={reference}
          onClose={close}
          otp={otp}
          setOtp={setOtp}
          authState={authState}
          declinedReason={declinedReason}
          handleDeclinePayout={handleDeclinePayout}
          refetchPayout={refetch}
          amount={amount}
          currency={currency}
          authorizeDeclinePayout={authorizeDeclinePayout}
          initiateDeclineSinglePayout={initiateSinglePayoutApproval}
          handleOtpResend={handleOtpResend}
          details={approvalWorkFlowDetails}
        />
      )}
      {modalType === 'approve' && (
        <ApprovePayoutModal
          otp={otp}
          setOtp={setOtp}
          authState={authState}
          onClose={close}
          refetchPayout={refetch}
          amount={amount}
          currency={currency}
          destination={destination}
          authorizeApprovePayout={authorizeApprove}
          details={approvalWorkFlowDetails}
          handleOtpResend={handleOtpResend}
        />
      )}
    </>
  );
};

export default PayoutApproval;
