/* eslint-disable react/jsx-props-no-spreading */
import { useState } from 'react';

import { getOtpDescriptors } from '+containers/Dashboard/Issuing/utils';
import CountdownTimer from '+containers/Dashboard/Shared/CountdownTimer';
import Modal from '+dashboard/Shared/Modal';
import { useOTPAuth } from '+hooks';
import useStore from '+store';
import { IDeclineModalProps } from '+types';
import { formatAmount, maskEmail, otpLength } from '+utils';

export default function DeclinePayoutModal({
  onClose,
  reference,
  initiateDeclineSinglePayout,
  authorizeDeclinePayout,
  authState,
  otp,
  setOtp,
  declinedReason,
  handleDeclinePayout,
  refetchPayout,
  amount,
  currency,
  handleOtpResend,
  details
}: IDeclineModalProps) {
  const [type, setType] = useState('');
  const defaultMerchant = useStore(store => store.defaultMerchant);
  const { updateCountdownStatus, countdownIsCompleted, updateAuthState } = useOTPAuth();
  const { otpLabel, otpPlaceholder } = getOtpDescriptors(authState.two_factor_type);
  const merchantEmail = defaultMerchant?.email || '';
  const { MIN_LENGTH, MAX_LENGTH } = otpLength;

  const setDisabled = () => {
    if (type === 'otpStage') {
      return !otp || otp.length === 0;
    }

    if (declinedReason) return false;
    if (otp) {
      if (otp.length) {
        return false;
      }
      return true;
    }
    return true;
  };

  const declinePayoutContent = () => (
    <>
      <p style={{ color: '#414F5F', fontWeight: 400 }}>
        You are about to decline a{' '}
        <span className="font-weight-bold">
          {formatAmount(amount)} {currency}
        </span>{' '}
        payout initiated by <strong>{details?.initiator?.name};</strong>
        <span className="font-weight-bold">{''}</span> declining means the transaction would not be processed. This action cannot be undone.
      </p>
      <label htmlFor="checkout-description">Description</label>
      <textarea
        name="description"
        className="form-control"
        placeholder="Maximum 150 characters"
        rows={3}
        maxLength={150}
        value={declinedReason}
        onChange={e => handleDeclinePayout(e.target.value.replace(/\.+$/, ''))}
        required
      />
    </>
  );

  const getConfirmAction = () => {
    const message = {
      otp: `the OTP (one-time PIN) that was sent to your email (${maskEmail(merchantEmail)}).`,
      totp: 'the authentication code from your authenticator app',
      totp_recovery_code: 'a recovery code'
    };
    return (
      <div className="reserved-vcard-container stack-xl">
        <p>To proceed, enter {message[authState.two_factor_type] || ''}</p>
        <div className="stack-md">
          <label htmlFor="otp" className="rvc-label" style={{ fontSize: '0.8rem' }}>
            {otpLabel}
          </label>
          <input
            role="textbox"
            type="text"
            name="otp"
            id="otp"
            className="form-control"
            placeholder={otpPlaceholder}
            aria-label="otp"
            aria-describedby="max-spend-range"
            inputMode="numeric"
            autoComplete="one-time-code"
            minLength={MIN_LENGTH}
            maxLength={MAX_LENGTH}
            value={otp}
            onChange={e => setOtp(e.target.value)}
          />
          {authState.two_factor_type === 'totp' && (
            <div className="otp-cta">
              <span>Can&apos;t access authenticator app?</span>
              <button
                type="button"
                className="semibold btn btn-link"
                onClick={() => updateAuthState({ two_factor_type: 'totp_recovery_code' })}
              >
                Confirm using recovery codes
              </button>
            </div>
          )}
          {authState.two_factor_type === 'otp' && (
            <div className="otp-cta with-countdown">
              <span>You didn&apos;t receive a code?</span>
              {countdownIsCompleted ? (
                <button
                  disabled
                  type="button"
                  className="semibold btn btn-link"
                  onClick={() => {
                    handleOtpResend;
                  }}
                >
                  Resend code.
                </button>
              ) : (
                <CountdownTimer targetTime={30} onCountdownEnd={() => updateCountdownStatus(true)} />
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  const switchModal = (kind: string) => {
    let content;
    switch (kind) {
      case 'otpStage':
        content = {
          heading: 'Confirm and Decline',
          content: getConfirmAction(),
          firstButtonText: 'Cancel',
          secondButtonText: 'Confirm',
          secondButtonColor: '#F32345',
          secondButtonAction: async () => {
            await authorizeDeclinePayout();
            refetchPayout();
          },
          completedHeading: 'Payout Declined',
          completedDescription: 'You have successfully declined this initiated payout.'
        };
        break;
      default:
        content = {
          heading: 'Confirm Payout Decline',
          content: declinePayoutContent(),
          firstButtonText: 'Close',
          secondButtonColor: '#F32345',
          secondButtonText: 'Next',
          secondButtonAction: async () => {
            await initiateDeclineSinglePayout({ reference });
            setType('otpStage');
          },
          secondButtonActionIsTerminal: false
        };
        break;
    }
    return {
      size: 'md',
      close: () => {
        onClose();
        setType('init');
        setOtp('');
        handleDeclinePayout('');
      },
      ...content,
      secondButtonDisable: setDisabled()
    };
  };

  return <Modal {...switchModal(type)} />;
}
