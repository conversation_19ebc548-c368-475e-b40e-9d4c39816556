/* eslint-disable react/jsx-props-no-spreading */
import { useState } from 'react';

import { getOtpDescriptors } from '+containers/Dashboard/Issuing/utils';
import CountdownTimer from '+containers/Dashboard/Shared/CountdownTimer';
import Modal from '+dashboard/Shared/Modal';
import { useOTPAuth } from '+hooks';
import useStore from '+store';
import { IApproveModalProps } from '+types';
import { capitalizeRemovedash, formatAmount, maskEmail, otpLength } from '+utils';

const ApprovePayoutModal = ({
  onClose,
  authState,
  otp,
  setOtp,
  amount,
  currency,
  destination,
  refetchPayout,
  authorizeApprovePayout,
  details,
  handleOtpResend
}: IApproveModalProps) => {
  const [type, setType] = useState('');
  const defaultMerchant = useStore(store => store.defaultMerchant);
  const { updateCountdownStatus, countdownIsCompleted, updateAuthState } = useOTPAuth();
  const { otpLabel, otpPlaceholder } = getOtpDescriptors(authState.two_factor_type);
  const merchantEmail = defaultMerchant?.email || '';
  const { MIN_LENGTH, MAX_LENGTH } = otpLength;

  const setDisabled = () => {
    if (type === 'otpStage') {
      return !otp || otp.length === 0;
    }
    if (otp) {
      if (otp.length) {
        return false;
      }
      return true;
    }
    return true;
  };

  const getOtpContent = () => {
    const message = {
      otp: `the OTP (one-time PIN) that was sent to your email (${maskEmail(merchantEmail)}).`,
      totp: 'the authentication code from your authenticator app',
      totp_recovery_code: 'a recovery code'
    };
    return (
      <div className="reserved-vcard-container stack-xl">
        <p>To proceed, enter {message[authState.two_factor_type] || ''}</p>
        <div className="stack-md">
          <label htmlFor="otp" className="rvc-label" style={{ fontSize: '0.8rem' }}>
            {otpLabel}
          </label>
          <input
            role="textbox"
            type="text"
            name="otp"
            id="otp"
            className="form-control"
            placeholder={otpPlaceholder}
            aria-label="otp"
            aria-describedby="max-spend-range"
            inputMode="numeric"
            autoComplete="one-time-code"
            minLength={MIN_LENGTH}
            maxLength={MAX_LENGTH}
            value={otp}
            onChange={e => setOtp(e.target.value)}
          />
          {authState.two_factor_type === 'totp' && (
            <div className="otp-cta">
              <span>Can&apos;t access authenticator app?</span>
              <button
                type="button"
                className="semibold btn btn-link"
                onClick={() => updateAuthState({ two_factor_type: 'totp_recovery_code' })}
              >
                Confirm using recovery codes
              </button>
            </div>
          )}
          {authState.two_factor_type === 'otp' && (
            <div className="otp-cta with-countdown">
              <span>You didn&apos;t receive a code?</span>
              {countdownIsCompleted ? (
                <button
                  disabled
                  type="button"
                  className="semibold btn btn-link"
                  onClick={() => {
                    handleOtpResend;
                  }}
                >
                  Resend code.
                </button>
              ) : (
                <CountdownTimer targetTime={30} onCountdownEnd={() => updateCountdownStatus(true)} />
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Modal
      close={() => onClose()}
      heading="Authorize and Confirm Payout"
      description={
        <p className="text-secondary font-weight-normal">
          Please confirm that you want to proceed with this payout. Once started, the payout would not be stopped until the transaction has
          been processed.
        </p>
      }
      content={
        <>
          <div className="p-4 bg-light rounded">
            <p>Some important details of your payout:</p>
            <p className="overpayment-underpayment-list">
              <span className="overpayment-underpayment-list-left-side font-weight-bold">Total Amount</span>
              <span className="text-secondary">
                <span className="font-weight-bold">{formatAmount(amount)}</span> {currency}
              </span>
            </p>
            <p className="overpayment-underpayment-list">
              <span className="overpayment-underpayment-list-left-side font-weight-bold">Initiator</span>
              <span className="text-secondary font-weight-bold">{details?.initiator?.name}</span>
            </p>
            <p className="overpayment-underpayment-list m-0">
              <span className="overpayment-underpayment-list-left-side font-weight-bold">Type</span>
              <span className="text-secondary font-weight-semibold">{capitalizeRemovedash(destination?.type) || 'Not Available'} </span>
            </p>
          </div>

          <div className="pt-5">{getOtpContent()}</div>
        </>
      }
      firstButtonText="Back"
      secondButtonText="Confirm"
      secondButtonColor="#24B314"
      secondButtonDisable={setDisabled()}
      secondButtonAction={async () => {
        try {
          await authorizeApprovePayout();
          refetchPayout();
          setType('');
        } finally {
          setOtp('');
          setType('otpStage');
        }
      }}
      completedHeading="Payout Approved"
      completedDescription="You have successfully approved this initiated payout."
    />
  );
};

export default ApprovePayoutModal;
