import Modal from '+containers/Dashboard/Shared/Modal';
import OtpContent from '+containers/Dashboard/Shared/OtpContent';
import { AuthDataType, DefaultMerchantType } from '+types';
import { twoFAInputValidation } from '+utils';

export default function OtpModal({
  visible,
  close,
  onVerify,
  defaultMerchant,
  setTwoFactorType,
  twoFactorType,
  authData,
  setAuthData,
  timeLeft,
  onResendToken
}: {
  visible: boolean;
  close: () => void;
  onVerify: () => void;
  resendText?: string;
  verifyText?: string;
  twoFactorType: 'otp' | 'totp' | 'totp_recovery_code';
  defaultMerchant: Partial<DefaultMerchantType>;
  setTwoFactorType: ((type: 'otp' | 'totp' | 'totp_recovery_code') => void) | undefined;
  authData: {
    code: string;
    identifier: string;
    two_factor_type: string;
  };
  setAuthData: ({ code, identifier, two_factor_type }: Partial<AuthDataType>) => void;
  timeLeft?: number;
  onResendToken?: () => void;

}) {

  return (
    <Modal
      size={'md'}
      close={close}
      visible={visible}
      secondButtonAction={onVerify}
      secondButtonText={'Verify'}
      firstButtonAction={close}
      completedHeading="Success"
      secondButtonDisable={!twoFAInputValidation(twoFactorType, authData.code)}
      heading={'Confirm Your Identity'}
      content={
        <OtpContent
          email={defaultMerchant?.userEmail || ''}
          twoFactorType={twoFactorType}
          setTwoFactorType={setTwoFactorType}
          authData={authData}
          setAuthData={setAuthData}
          timeLeft={timeLeft}
          onResendToken={onResendToken}
        />
      }
    />
  );
}
