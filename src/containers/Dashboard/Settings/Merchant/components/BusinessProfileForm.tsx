import { Field, FieldProps, Form, Formik } from 'formik';
import * as Yup from 'yup';

import CustomPhoneInput from '+containers/Shared/CustomPhoneInput';
import CustomInput from '+shared/CustomInput';
import { IBusinessProfileFormProps } from '+types';
import { cleanInput, DefaultRequiredValidation, EmailValidation, formatPhoneNumber, PhoneValidation, URLValidation } from '+utils';

const BusinessProfileForm = ({
  initialValues,
  handleSubmit,
  isLoading,
  setIsEditing,
  initializing,
  formikRef
}: IBusinessProfileFormProps) => {
  const validationSchema = Yup.object().shape({
    description: Yup.string().required('Business description is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    website: Yup.string().url('Invalid URL').required('Website is required'),
    phone_number: Yup.string()
      .required('Phone number is required')
      .test('is-valid-phone', 'A valid phone number is required', value => !PhoneValidation(value))
  });

  return (
    <div className="element-box business-profile-form">
      <Formik innerRef={formikRef} initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
        {({ values, setFieldValue, errors }) => (
          <Form>
            <div className="business-profile-form__left">
              <div className="form-group">
                <Field
                  name="business_name"
                  label="Business Name"
                  placeholder=""
                  type="text"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setFieldValue('business_name', cleanInput(e?.target?.value));
                  }}
                  component={CustomInput}
                  disabled
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <Field
                  name="phone_number"
                  label="Phone Number"
                  placeholder="Enter phone number"
                  type="text"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFieldValue('phone_number', formatPhoneNumber(e?.target?.value))}
                  component={CustomPhoneInput}
                  className="form-control phone-input"
                />
              </div>
              <div className="form-group">
                <Field
                  name="description"
                  validate={() => {
                    return DefaultRequiredValidation(values?.description, 'Business description');
                  }}
                >
                  {({ field }: FieldProps) => (
                    <>
                      <label htmlFor="description"> Business Description</label>
                      <textarea
                        {...field}
                        id={field.name}
                        name={field.name}
                        className="form-control"
                        rows={3}
                        maxLength={150}
                        value={values.description || ''}
                        onChange={e => setFieldValue('description', cleanInput(e?.target?.value))}
                      />
                      {errors.description ? (
                        <div className="input__errors">
                          <p>{errors.description}</p>
                        </div>
                      ) : null}
                    </>
                  )}
                </Field>
              </div>
            </div>
            <div className="business-profile-form__right">
              <div className="form-group business-website">
                <Field
                  name="country"
                  label="Country"
                  placeholder=""
                  className="form-control"
                  component={CustomInput}
                  maxLength="150"
                  value={values.country || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFieldValue('country', cleanInput(e?.target?.value))}
                  disabled
                />
              </div>
              <div className="form-group">
                <Field
                  name="email"
                  label="Support Email"
                  placeholder="E.g, <EMAIL>"
                  component={CustomInput}
                  validate={(event: string) => EmailValidation(event)}
                  maxLength="100"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFieldValue('email', cleanInput(e?.target?.value))}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <Field
                  name="website"
                  label="Website"
                  placeholder="website"
                  component={CustomInput}
                  maxLength="150"
                  value={values?.website || ''}
                  validate={() => {
                    return DefaultRequiredValidation(values?.website, 'website') || URLValidation(values?.website);
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFieldValue('website', cleanInput(e?.target?.value))}
                  className="form-control"
                />
              </div>
              <div className="form-buttons-w business-profile-form-button">
                <button
                  type="submit"
                  className="btn btn-primary d-flex align-items-center mr-1"
                  disabled={isLoading || initializing}
                  data-testid="save-button"
                >
                  {isLoading || initializing ? (
                    <>
                      <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" />
                      &nbsp;&nbsp;
                    </>
                  ) : null}
                  {isLoading ? <span style={{ marginLeft: '0.5rem' }}>Saving...</span> : `Save Changes`}
                </button>
                <button type="button" className="btn btn-grey" onClick={() => setIsEditing(false)}>
                  Cancel
                </button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BusinessProfileForm;
