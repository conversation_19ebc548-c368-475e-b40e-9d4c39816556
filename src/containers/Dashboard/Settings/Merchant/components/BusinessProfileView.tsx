import * as Flags from 'country-flag-icons/react/3x2';

import { IBusinessProfileViewProps } from '+types';

import ProfileDetail from './ProfileDetail';

const BusinessProfileView = ({ defaultMerchant, businessProfile, setIsEditing, defaultBusiness }: IBusinessProfileViewProps) => {
  const Flag = Flags[defaultMerchant?.country?.iso2 as keyof typeof Flags];
  const merchantInfo = defaultBusiness?.details?.contact;
  return (
    <>
      <div className="business-profile__head">
        <span>
          <h5 className="compliance-heading">Business Profile</h5>
          <span className="description">Details about your company/organization as submitted during onboarding.</span>
        </span>
        <button className="btn btn-primary" onClick={() => setIsEditing(true)}>
          Update
        </button>
      </div>
      <div className="box mt-3">
        <div className="list-row" style={{ width: '50%' }}>
          <ProfileDetail label="Business Name" value={defaultMerchant?.name} />
          <ProfileDetail label="Phone Number" value={merchantInfo?.support_phone} />
          <ProfileDetail label="Business Description" value={businessProfile?.business_description} />
          <ProfileDetail label="Support Email" value={merchantInfo?.support_email} />
        </div>
        <div className="list-row" style={{ marginBottom: '0.5rem' }}>
          <ProfileDetail
            label="Country"
            value={
              <span>
                {defaultMerchant?.country?.name}&nbsp;&nbsp;{Flag && <Flag width={20} />}
              </span>
            }
          />

          <ProfileDetail label="Email" value={defaultMerchant?.email} />
          <ProfileDetail label="Website" value={businessProfile?.website} />
          <ProfileDetail
            label={defaultBusiness?.business_type === 'ngo' ? 'NGO Type' : 'Industry'}
            value={
              defaultBusiness?.business_type === 'ngo'
                ? defaultBusiness?.details?.business_profile?.incorporated_trustee_category?.label
                : defaultBusiness?.details?.business_profile?.industry?.label
            }
          />
        </div>
      </div>
    </>
  );
};

export default BusinessProfileView;
