import { render, screen } from '@testing-library/react';
import ProfileDetail from '../components/ProfileDetail';
import { axe } from 'jest-axe';


describe('ProfileDetail Component', () => {
  it('renders the label and value correctly', () => {
    render(<ProfileDetail label="Business Name" value="Test Business" />);

    // Check that the label is rendered
    expect(screen.getByText('Business Name:')).toBeInTheDocument();

    // Check that the value is rendered
    expect(screen.getByText('Test Business')).toBeInTheDocument();
  });

  it('renders "N/A" when value is not provided', () => {
    render(<ProfileDetail label="Business Name" />);

    // Check that the label is rendered
    expect(screen.getByText('Business Name:')).toBeInTheDocument();

    // Check that "N/A" is rendered as the default value
    expect(screen.getByText('N/A')).toBeInTheDocument();
  });

  it('renders React nodes as the value', () => {
    render(
      <ProfileDetail
        label="Country"
        value={
          <span>
            United States <img src="flag.png" alt="US Flag" />
          </span>
        }
      />
    );

    // Check that the label is rendered
    expect(screen.getByText('Country:')).toBeInTheDocument();

    // Check that the React node is rendered
    expect(screen.getByText('United States')).toBeInTheDocument();
    expect(screen.getByAltText('US Flag')).toBeInTheDocument();
  });

  it('has no violations', async () => {
    const { container } = render(<ProfileDetail label="Business Name" value="Test Business" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  })
});
