import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { IBusinessProfileViewProps } from '+types';

import BusinessProfileView from '../components/BusinessProfileView';

describe('BusinessProfileView Component', () => {
  const mockSetIsEditing = vi.fn();

  const defaultProps: IBusinessProfileViewProps = {
    defaultMerchant: {
      name: 'Test Merchant',
      email: '<EMAIL>',
      country: {
        name: 'United States',
        iso2: 'US',
        id: 1,
        is_active: true
      }
    },
    businessProfile: {
      business_description: 'A test business description',
      website: 'https://example.com',
      incorporated_trustee_category: {
        label: 'Non-Profit'
      },
      industry: {
        label: 'Technology'
      }
    },
    defaultBusiness: {
      business_type: 'ngo',
      details: {
        contact: {
          support_phone: '+1234567890',
          support_email: '<EMAIL>'
        },
        business_profile: {
          incorporated_trustee_category: {
            label: 'Non-Profit'
          },
          industry: {
            label: 'Technology'
          },
          business_description: 'A test business description',
          website: 'https://example.com'
        }
      }
    },
    setIsEditing: mockSetIsEditing
  };

  it('renders the component with all details', () => {
    render(<BusinessProfileView {...defaultProps} />);

    // Check headings
    expect(screen.getByText('Business Profile')).toBeInTheDocument();
    expect(screen.getByText('Details about your company/organization as submitted during onboarding.')).toBeInTheDocument();

    // Check profile details
    expect(screen.getByText('Business Name:')).toBeInTheDocument();
    expect(screen.getByText('Test Merchant')).toBeInTheDocument();

    expect(screen.getByText('Phone Number:')).toBeInTheDocument();
    expect(screen.getByText('+1234567890')).toBeInTheDocument();

    expect(screen.getByText('Business Description:')).toBeInTheDocument();
    expect(screen.getByText('A test business description')).toBeInTheDocument();

    expect(screen.getByText('Support Email:')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();

    expect(screen.getByText('Country:')).toBeInTheDocument();
    expect(screen.getByText('United States')).toBeInTheDocument();

    expect(screen.getByText('Email:')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();

    expect(screen.getByText('Website:')).toBeInTheDocument();
    expect(screen.getByText('https://example.com')).toBeInTheDocument();

    expect(screen.getByText('NGO Type:')).toBeInTheDocument();
    expect(screen.getByText('Non-Profit')).toBeInTheDocument();
  });

  it('renders "Industry" instead of "NGO Type" when business_type is not "ngo"', () => {
    const props = {
      ...defaultProps,
      defaultBusiness: {
        ...defaultProps.defaultBusiness,
        business_type: 'corporate'
      }
    };

    render(<BusinessProfileView {...props} />);

    expect(screen.getByText('Industry:')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
  });

  it('calls setIsEditing when the "Update" button is clicked', async () => {
    render(<BusinessProfileView {...defaultProps} />);

    const updateButton = screen.getByText('Update');
    await userEvent.click(updateButton);

    expect(mockSetIsEditing).toHaveBeenCalledTimes(1);
    expect(mockSetIsEditing).toHaveBeenCalledWith(true);
  });

  it('renders "N/A" when a value is missing', () => {
    const props: IBusinessProfileViewProps = {
      ...defaultProps,
      businessProfile: {
        ...defaultProps.businessProfile,
        website: undefined
      }
    };

    render(<BusinessProfileView {...props} />);

    expect(screen.getByText('Website:')).toBeInTheDocument();
    expect(screen.getByText('N/A')).toBeInTheDocument();
  });
  it('has no violations', async () => {
    const { container } = render(<BusinessProfileView {...defaultProps} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
