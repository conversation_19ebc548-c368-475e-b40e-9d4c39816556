import { act, renderHook } from '@testing-library/react';

import MockIndex from '+mock/MockIndex';

import { useBusinessProfile } from '../hooks/useBusinessProfile';

describe('useBusinessProfile', () => {
  it('should initialize correctly', () => {
    const { result } = renderHook(() => useBusinessProfile(), {
      wrapper: MockIndex
    });
    expect(result.current.isEditing).toBe(false);
    expect(result.current.openOtpModal).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.formikRef.current).toBeNull();
  });
  it('updates isEditing state correctly', () => {
    const { result } = renderHook(() => useBusinessProfile(), {
      wrapper: MockIndex
    });

    act(() => {
      result.current.setIsEditing(true);
    });

    expect(result.current.isEditing).toBe(true);
  });

  it('updates openOtpModal state correctly', () => {
    const { result } = renderHook(() => useBusinessProfile(), {
      wrapper: MockIndex
    });

    act(() => {
      result.current.setOpenOtpModal(true);
    });

    expect(result.current.openOtpModal).toBe(true);
  });
});
