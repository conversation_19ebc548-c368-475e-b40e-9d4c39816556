import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import BusinessProfileForm from '../components/BusinessProfileForm';

describe('BusinessProfileForm Component', () => {
  const mockHandleSubmit = vi.fn();
  const mockSetIsEditing = vi.fn();

  const defaultProps = {
    initialValues: {
      description: '',
      email: '',
      website: '',
      phone_number: '',
      country: 'United States',
      business_name: 'Test Business',
    },
    handleSubmit: mockHandleSubmit,
    isLoading: false,
    setIsEditing: mockSetIsEditing,
    initializing: false,
    formikRef: { current: null },
  };

  const renderForm = (props = defaultProps) => render(<BusinessProfileForm {...props} />);

  const fillForm = async () => {
    await userEvent.type(screen.getByLabelText('Business Description'), 'A test business description');
    await userEvent.type(screen.getByLabelText('Phone Number'), '***********');
    await userEvent.type(screen.getByLabelText('Support Email'), '<EMAIL>');
    await userEvent.type(screen.getByLabelText('Website'), 'https://example.com');
  };

  it('renders the form with all fields', () => {
    renderForm();

    expect(screen.getByLabelText('Business Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Phone Number')).toBeInTheDocument();
    expect(screen.getByLabelText('Business Description')).toBeInTheDocument();
    expect(screen.getByLabelText('Country')).toBeInTheDocument();
    expect(screen.getByLabelText('Support Email')).toBeInTheDocument();
    expect(screen.getByLabelText('Website')).toBeInTheDocument();
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('disables the submit button when isLoading or initializing is true', () => {
    renderForm({ ...defaultProps, isLoading: true });
    expect(screen.getByTestId('save-button')).toBeDisabled();
  });

  it('calls setIsEditing when Cancel button is clicked', async () => {
    renderForm();

    const cancelButton = screen.getByText('Cancel');
    await userEvent.click(cancelButton);

    expect(mockSetIsEditing).toHaveBeenCalledWith(false);
  });

  it('displays validation error for invalid phone number', async () => {
    renderForm();

    await userEvent.type(screen.getByLabelText('Phone Number'), '0812344');

    const submitButton = screen.getByText('Save Changes');
    await userEvent.click(submitButton);

    expect(await screen.findByText('A valid phone number is required')).toBeInTheDocument();
  })

  it('displays validation errors for required fields', async () => {
    renderForm();

    const submitButton = screen.getByText('Save Changes');
    await userEvent.click(submitButton);

    expect(await screen.findByText('Business description is required')).toBeInTheDocument();
    expect(await screen.findByText('Phone number is required')).toBeInTheDocument();
    expect(await screen.findByText('Email is required')).toBeInTheDocument();
    expect(await screen.findByText('Website is required')).toBeInTheDocument();
  });

  it('calls handleSubmit with correct values when the form is submitted', async () => {
    renderForm();
    await fillForm();

    const submitButton = screen.getByText('Save Changes');
    await userEvent.click(submitButton);

    expect(mockHandleSubmit).toHaveBeenCalledWith(
      {
        description: 'A test business description',
        phone_number: '+2348123456786',
        email: '<EMAIL>',
        website: 'https://example.com',
        country: 'United States',
        business_name: 'Test Business',
      },
      expect.any(Object)
    );
  });

  it('has no accessibility violations', async () => {
    const { container } = renderForm();
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
