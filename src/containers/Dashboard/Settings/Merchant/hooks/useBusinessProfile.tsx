import { useRef, useState } from 'react';
import { FormikProps, FormikTouched } from 'formik';

import { useOtp } from '+hooks';
import useFeedbackHandler from '+hooks/feedbackHandler';
import useMerchantAuth from '+hooks/useMerchantAuth';
import { MerchantServices } from '+services/merchant-services';
import useStore from '+store';
import { AuthDataType, ErrorType } from '+types';

interface IBusinessProfileFormValues {
  description: string;
  email: string;
  website: string;
  phone_number: string;
  country: string;
  business_name: string;
}

export const useBusinessProfile = () => {
  const defaultMerchant = useStore(store => store.defaultMerchant);
  const defaultBusiness = useStore(store => store.merchantKYC);
  const merchantInfo = defaultBusiness?.details?.contact;
  const businessProfile = defaultBusiness?.details?.business_profile;

  const [isEditing, setIsEditing] = useState(false);
  const [openOtpModal, setOpenOtpModal] = useState(false);
  const [authData, setAuthData] = useState<AuthDataType>({
    code: '',
    identifier: '',
    two_factor_type: ''
  });
  const [twoFactorType, setTwoFactorType] = useState<'otp' | 'totp' | 'totp_recovery_code'>('otp');

  const { feedbackInit, closeFeedback } = useFeedbackHandler();
  const { bootstrap } = useMerchantAuth();
  const { isLoading, mutateAsync } = MerchantServices.useUpdateMerchantDetails({});

  const formikRef = useRef<FormikProps<IBusinessProfileFormValues>>(null);

  const { timeLeft, initiateToken, onResendToken, initializing } = useOtp({
    action: 'update_business_profile',
    authData,
    setAuthData: (authData: Partial<AuthDataType>) => setAuthData(prev => ({ ...prev, ...authData })),
    setTwoFactorType,
    onInitiationComplete: () => {
      setOpenOtpModal(true);
    }
  });

  const handleSubmit = async (data: IBusinessProfileFormValues) => {
    const updatedData = {
      business_description: data.description,
      support_email: data.email,
      phone: data.phone_number,
      website: data.website
    };

    const payload = {
      ...updatedData,
      authorization: {
        code: authData.code,
        identifier: authData.identifier,
        two_factor_type: twoFactorType
      }
    };

    try {
      await mutateAsync(payload);
      await bootstrap();
      setIsEditing(false);
      feedbackInit({
        message: 'Successfully updated your business details',
        type: 'success'
      });
      setTimeout(() => closeFeedback(), 3000);
    } catch (error) {
      const errorMessage = (error as ErrorType)?.response?.data?.message;
      feedbackInit({
        message: errorMessage ?? `There's an issue updating your details. Try again later`,
        type: 'danger',
        componentLevel: true
      });
      throw error;
    }
  };

  const validateAndOpenOtpModal = async () => {
    if (formikRef.current) {
      const errors = await formikRef.current.validateForm();
      if (Object.keys(errors).length === 0) {
        initiateToken({ isAsync: false });
      } else {
        formikRef.current.setTouched(
          Object.keys(errors).reduce((acc, key) => {
            acc[key as keyof IBusinessProfileFormValues] = true;
            return acc;
          }, {} as FormikTouched<IBusinessProfileFormValues>)
        );
      }
    }
  };

  const onVerifyOtp = async () => {
    if (formikRef.current) {
      const formValues = formikRef.current.values;
      try {
        await handleSubmit(formValues);
      } catch (error) {
        throw error;
      }
    }
  };

  return {
    defaultMerchant,
    defaultBusiness,
    merchantInfo,
    businessProfile,
    isEditing,
    setIsEditing,
    openOtpModal,
    setOpenOtpModal,
    authData,
    setAuthData,
    twoFactorType,
    setTwoFactorType,
    handleSubmit,
    isLoading,
    formikRef,
    timeLeft,
    initiateToken,
    onResendToken,
    initializing,
    validateAndOpenOtpModal,
    onVerifyOtp
  };
};
