import React, { useEffect, useState } from 'react';

import { useOTPAuth, usePermissions } from '+hooks';
import { ApprovalServices } from '+services/multilevel-approval-services';
import { defaultApproversStateType, defaultInitiatorsStateType, IFetchedUser, IFilteredUsers } from '+types';
import { cleanInput } from '+utils';

import ApprovalWorkflowModal from './components/ApprovalWorkflowModal';
import ApproversAccordion from './components/ApproversAccordion';
import InitiatorsAccordion from './components/InitiatorsAccordion';
import ManageApprovalWorkflowModal from './components/ManageApprovalWorkflowModal';
import ToggleButton from './components/ToggleButton';
import { approverInitiatorRolesDocumentationUrl } from './helpers/approvalHelpers';
import { createDebouncedSearch, getInitiatorsAndApproversPayload, getProductWorkflowReference } from './utils';

import './index.scss';

const MultiLevelApprovals = () => {
  /*========================== MANAGE STATES============================*/
  const userAccess = usePermissions('approval_workflow_configuration');
  const canEditApprovals = userAccess === 'manage';
  const { authState, updateAuthState, updateCountdownStatus, resendOTP } = useOTPAuth();
  const [otp, setOtp] = useState('');

  const [workflowModalOpen, setWorkflowModalOpen] = useState(false);
  const [initiatorModalState, setInitiatorModalState] = useState(false);
  const [showManageWorkflowModal, setShowManageWorkflowModal] = useState(false);
  const [approverModalState, setApproverModalState] = useState(false);

  const [selectedPayoutType, setSelectedPayoutType] = useState<'single_payout' | 'bulk_payout' | null>(null);
  const [users, setUsers] = useState<IFetchedUser[]>([]);
  const [approvalReference, setApprovalReference] = useState('');
  const [actionType, setActionType] = useState<'ENABLE' | 'DISABLE'>('ENABLE');
  const [isApprovalEnabled, setIsApprovalEnabled] = useState(false);

  const [hasInitiated, setHasInitiated] = useState({
    single_payout: false,
    bulk_payout: false
  });
  const [payoutState, setPayoutState] = useState({
    isEnabled: {
      single_payout: false,
      bulk_payout: false
    },
    isToggled: {
      single_payout: false,
      bulk_payout: false
    }
  });

  const [initiatorState, setInitiatorState] = useState<defaultInitiatorsStateType>({
    searchTerm: '',
    designatedInitiators: [] as string[],
    filteredMembers: [] as IFilteredUsers[],
    showAll: false,
    loading: false,
    error: ''
  });

  const [approverState, setApproverState] = useState<defaultApproversStateType>({
    searchTerm: '',
    designatedApprovers: [] as string[],
    filteredMembers: [] as IFilteredUsers[],
    showAll: false,
    loading: false,
    error: ''
  });

  const { designatedInitiators, loading: loadingInitiators } = initiatorState;
  const { designatedApprovers, loading: loadingApprovers } = approverState;

  /*==========================APIS INTEGRATION=========================*/

  const { data: workFlowUsers, error: fetchError, isLoading } = ApprovalServices.useGetApprovalWorkflowUsers({});

  const { data: approvalWorkflows, refetch: refetchApprovalWorkflows } = ApprovalServices.useGetApprovalWorkflows({});

  const isApprovalWorkflowExist = approvalWorkflows?.data?.length > 0;

  const workflowReference = {
    single_payout: getProductWorkflowReference('single_payout', approvalWorkflows || {}) ?? '',
    bulk_payout: getProductWorkflowReference('bulk_payout', approvalWorkflows || {}) ?? ''
  };

  const { mutateAsync: initiateApprovalWorkflow } = ApprovalServices.useInitiateApprovalWorkflow({
    onSuccess: data => {
      const { reference, auth } = data?.data?.data || {};
      const { identifier, two_factor_type } = auth;

      updateAuthState({ ...authState, ...auth, identifier, two_factor_type });
      setApprovalReference(reference);
    },
    showErrorMessage: true,
    showSuccessMessage: false,
    errorMessageBannerLevel: true
  });

  const triggerInitiateApprovalWorkflow = async () => {
    const { initiators, approvers } = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);
    await initiateApprovalWorkflow({
      type: selectedPayoutType,
      initiators,
      approvers
    });
  };

  const { mutateAsync: authorizeApprovalWorkflow, isSuccess } = ApprovalServices.useAuthorizeApprovalWorkflow({
    showErrorMessage: true,
    showSuccessMessage: true,
    errorMessageBannerLevel: true,
    successMessageBannerLevel: false
  });

  const createApprovalWorkflow = async () => {
    const { initiators, approvers } = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);

    await authorizeApprovalWorkflow({
      reference: approvalReference,
      auth: {
        identifier: authState.identifier,
        two_factor_type: authState.two_factor_type,
        code: otp
      },
      data: {
        type: selectedPayoutType,
        initiators,
        approvers
      }
    });

    setPayoutState(prevState => ({
      ...prevState,
      isEnabled: {
        ...prevState.isEnabled,
        [selectedPayoutType as 'single_payout' | 'bulk_payout']: true
      }
    }));
    setHasInitiated(prevState => ({
      ...prevState,
      [selectedPayoutType as 'single_payout' | 'bulk_payout']: true
    }));
    refetchApprovalWorkflows();
    closeWorkflowModal();
    setShowManageWorkflowModal(false);
  };

  const { mutateAsync: initiateManageApprovalWorkflow } = ApprovalServices.useInitiateManageApprovalWorkflow({
    queryData: {
      approvalWorkflowRef: selectedPayoutType === 'single_payout' ? workflowReference.single_payout : workflowReference.bulk_payout
    },
    onSuccess: data => {
      const { reference, auth, enabled } = data?.data?.data || {};
      const { identifier, two_factor_type } = auth;

      updateAuthState({ ...authState, ...auth, identifier, two_factor_type });
      setApprovalReference(reference);
      setIsApprovalEnabled(enabled);
    },
    showErrorMessage: true,
    showSuccessMessage: false,
    errorMessageBannerLevel: true
  });

  const triggerInitiateManageApprovalWorkflow = async () => {
    await initiateManageApprovalWorkflow({
      enabled: !isApprovalEnabled
    });
  };

  const { mutateAsync: authorize } = ApprovalServices.useAuthorizeManageApprovalWorkflow({
    queryData: {
      approvalWorkflowRef: approvalReference
    },
    showErrorMessage: true,
    enabled: true,
    showSuccessMessage: true,
    errorMessageBannerLevel: true,
    successMessageBannerLevel: false
  });

  const authorizeManageApprovalWorkflow = async ({ enabled }: { enabled: boolean }) => {
    await authorize({
      reference: approvalReference,
      auth: {
        identifier: authState.identifier,
        two_factor_type: authState.two_factor_type,
        code: otp
      },
      data: {
        enabled
      }
    });
    setPayoutState(prevState => ({
      ...prevState,
      isEnabled: {
        ...prevState.isEnabled,
        [selectedPayoutType as 'single_payout' | 'bulk_payout']: true
      }
    }));
    setActionType(enabled === true ? 'DISABLE' : 'ENABLE');
    setOtp('');
  };

  const { mutateAsync: initiateUpdate } = ApprovalServices.useInitiateUpdateInitiators({
    onSuccess: data => {
      const { reference, auth } = data?.data?.data || {};
      const { identifier, two_factor_type } = auth;

      updateAuthState({ ...authState, ...auth, identifier, two_factor_type });
      setApprovalReference(reference);
    },
    showErrorMessage: true,
    showSuccessMessage: false,
    errorMessageBannerLevel: false,
    reference: approvalReference
  });

  const initiateUpdateInitiators = async () => {
    const { initiators } = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);

    await initiateUpdate({
      initiators
    });
  };

  const { mutateAsync: authorizeUpdate } = ApprovalServices.useAuthorizeUpdateInitiators({
    showErrorMessage: true,
    showSuccessMessage: true,
    errorMessageBannerLevel: true,
    successMessageBannerLevel: false,
    reference: approvalReference
  });

  const authorizeUpdateInitiators = async (refetchSingleInitiatorsWorkflow: () => void, refetchBulkInitiatorsWorkflow: () => void) => {
    const { initiators } = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);

    await authorizeUpdate({
      reference: approvalReference,
      auth: {
        identifier: authState.identifier,
        two_factor_type: authState.two_factor_type,
        code: otp
      },
      data: {
        initiators
      }
    });
    refetchSingleInitiatorsWorkflow();
    refetchBulkInitiatorsWorkflow();
    refetchApprovalWorkflows();
  };

  const { mutateAsync: initiateApprovers } = ApprovalServices.useInitiateUpdateApprovers({
    onSuccess: data => {
      const { reference, auth } = data?.data?.data || {};
      const { identifier, two_factor_type } = auth;

      updateAuthState({ ...authState, ...auth, identifier, two_factor_type });
      setApprovalReference(reference);
    },
    showErrorMessage: true,
    showSuccessMessage: false,
    errorMessageBannerLevel: true,
    reference: approvalReference
  });

  const initiateUpdateApprovers = async () => {
    const { approvers } = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);

    await initiateApprovers({
      approvers
    });
  };

  const { mutateAsync: authorizeApprovers } = ApprovalServices.useAuthorizeUpdateApprovers({
    showErrorMessage: true,
    showSuccessMessage: true,
    errorMessageBannerLevel: true,
    successMessageBannerLevel: false,
    reference: approvalReference
  });

  const authorizeUpdateApprovers = async (refetchSingleApproversWorkflow: () => void, refetchBulkApproversWorkflow: () => void) => {
    const { approvers } = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);

    await authorizeApprovers({
      reference: approvalReference,
      auth: {
        identifier: authState.identifier,
        two_factor_type: authState.two_factor_type,
        code: otp
      },
      data: {
        approvers
      }
    });
    refetchSingleApproversWorkflow();
    refetchBulkApproversWorkflow();
    refetchApprovalWorkflows();
  };

  /*==========================UTILS FUNCTIONS=========================*/

  const debouncedSearchInitiator = createDebouncedSearch(
    ((state: defaultInitiatorsStateType | defaultApproversStateType) =>
      setInitiatorState(state as defaultInitiatorsStateType)) as React.Dispatch<
      React.SetStateAction<defaultInitiatorsStateType | defaultApproversStateType>
    >,
    users
  );
  const debouncedSearchApprover = createDebouncedSearch(
    ((state: defaultInitiatorsStateType | defaultApproversStateType) =>
      setApproverState(state as defaultApproversStateType)) as React.Dispatch<
      React.SetStateAction<defaultInitiatorsStateType | defaultApproversStateType>
    >,
    users
  );

  /*==========================HANDLERS COMPOSITIONS=========================*/

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>, type: 'initiator' | 'approver') => {
    const searchTerm = cleanInput(e.target.value);

    switch (type) {
      case 'initiator':
        setInitiatorState((prevState: defaultInitiatorsStateType) => ({
          ...prevState,
          searchTerm,
          error: ''
        }));

        if (searchTerm) {
          debouncedSearchInitiator(searchTerm);
        } else {
          setInitiatorState((prevState: defaultInitiatorsStateType) => ({
            ...prevState,
            filteredMembers: [],
            loading: false
          }));
        }
        break;

      case 'approver':
        setApproverState((prevState: defaultApproversStateType) => ({
          ...prevState,
          searchTerm,
          error: ''
        }));

        if (searchTerm) {
          debouncedSearchApprover(searchTerm);
        } else {
          setApproverState((prevState: defaultApproversStateType) => ({
            ...prevState,
            filteredMembers: [],
            loading: false
          }));
        }
        break;

      default:
        break;
    }
  };

  const handleResetInitiatorState = (setState: React.Dispatch<React.SetStateAction<defaultInitiatorsStateType>>) => {
    setState({
      searchTerm: '',
      designatedInitiators: [],
      filteredMembers: [],
      showAll: false,
      loading: false,
      error: ''
    });
  };

  const handleResetApproverState = (setState: React.Dispatch<React.SetStateAction<defaultApproversStateType>>) => {
    setState({
      searchTerm: '',
      designatedApprovers: [],
      filteredMembers: [],
      showAll: false,
      loading: false,
      error: ''
    });
  };

  const handleSelectInitiator = (name: string) => {
    if (!initiatorState.designatedInitiators.includes(name)) {
      setInitiatorState(prevState => {
        const updatedList = [...prevState.designatedInitiators, name];
        return {
          ...prevState,
          designatedInitiators: updatedList,
          searchTerm: '',
          filteredMembers: []
        };
      });
    }
  };

  const handleRemoveInitiator = (name: string) => {
    setInitiatorState(prevState => {
      const updatedList = prevState.designatedInitiators.filter(item => item !== name);
      return {
        ...prevState,
        designatedInitiators: updatedList
      };
    });
  };

  const handleSelectApprover = (name: string) => {
    if (!approverState.designatedApprovers.includes(name)) {
      setApproverState(prevState => {
        const updatedList = [...prevState.designatedApprovers, name];
        return {
          ...prevState,
          designatedApprovers: updatedList,
          searchTerm: '',
          filteredMembers: []
        };
      });
    }
  };

  const handleRemoveApprover = (name: string) => {
    setApproverState(prevState => {
      const updatedList = prevState.designatedApprovers.filter(item => item !== name);
      return {
        ...prevState,
        designatedApprovers: updatedList
      };
    });
  };

  const handleOtp = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOtp(cleanInput(e.target.value));
  };

  const handleManageToggle = async () => {
    const type = selectedPayoutType;
    if (!type) return;

    const isToggleOn = payoutState.isEnabled[type];
    setPayoutState(prevState => ({
      ...prevState,
      isToggled: {
        ...prevState.isToggled,
        [type]: true
      }
    }));

    setPayoutState(prevState => ({
      ...prevState,
      isEnabled: {
        ...prevState.isEnabled,
        [type]: !isToggleOn
      },
      isToggled: {
        ...prevState.isToggled,
        [type]: false
      }
    }));
    closeManageWorkflowModal();
  };

  const toggleSettings = (type: 'single_payout' | 'bulk_payout') => {
    const isToggleOn = payoutState.isEnabled[type];

    if (hasInitiated[type]) {
      setShowManageWorkflowModal(true);
    } else {
      setWorkflowModalOpen(true);
    }
    setSelectedPayoutType(type);
    setActionType(isToggleOn ? 'DISABLE' : 'ENABLE');
  };

  const closeWorkflowModal = () => {
    setWorkflowModalOpen(false);
    setSelectedPayoutType(null);
    handleResetInitiatorState(setInitiatorState);
    handleResetApproverState(setApproverState);
  };

  const closeManageWorkflowModal = () => {
    setShowManageWorkflowModal(false);
  };

  const view = {
    visibleMembers: designatedInitiators.slice(0, 6),
    hiddenMembers: designatedInitiators.slice(6),
    visibleApprovers: designatedApprovers.slice(0, 6),
    hiddenApprovers: designatedApprovers.slice(6)
  };

  const handleOtpResend = async () => {
    updateCountdownStatus(false);
    await resendOTP();
  };

  const handleUpdateProductRef = (actionRef: string) => {
    setApprovalReference(actionRef);
  };

  /*==========================SIDE EFFECTS=========================*/

  useEffect(() => {
    if (workFlowUsers) {
      setUsers(Array.isArray(workFlowUsers?.data) ? workFlowUsers?.data : []);
    } else if (fetchError) {
      setInitiatorState(prevState => ({ ...prevState, error: fetchError.message }));
      setApproverState(prevState => ({ ...prevState, error: fetchError.message }));
    }
    setInitiatorState(prevState => ({ ...prevState, loading: isLoading }));
    setApproverState(prevState => ({ ...prevState, loading: isLoading }));
  }, [workFlowUsers, fetchError, isLoading]);

  useEffect(() => {
    if (approvalWorkflows) {
      const singlePayoutInitiated = approvalWorkflows?.data?.some(
        (workflow: { type: string; enabled: boolean }) => workflow.type === 'single_payout'
      );

      const bulkPayoutInitiated = approvalWorkflows?.data?.some(
        (workflow: { type: string; enabled: boolean }) => workflow.type === 'bulk_payout'
      );

      const singlePayoutEnabled = approvalWorkflows?.data?.some(
        (workflow: { type: string; enabled: boolean }) => workflow.type === 'single_payout' && workflow.enabled
      );

      const bulkPayoutEnabled = approvalWorkflows?.data?.some(
        (workflow: { type: string; enabled: boolean }) => workflow.type === 'bulk_payout' && workflow.enabled
      );

      setPayoutState(prevState => ({
        ...prevState,
        isEnabled: {
          single_payout: singlePayoutEnabled,
          bulk_payout: bulkPayoutEnabled
        }
      }));

      setHasInitiated({
        single_payout: singlePayoutInitiated,
        bulk_payout: bulkPayoutInitiated
      });

      if (singlePayoutEnabled || bulkPayoutEnabled) {
        setActionType('DISABLE');
      } else {
        setActionType('ENABLE');
      }
    }
  }, [approvalWorkflows]);

  return (
    <>
      <div className="mt-4 border-bottom">
        <div className="row">
          <div className="col-sm-12">
            <div className="element-wrapper">
              <div className="element-box p-0">
                <h5 className="form-header">Approvals</h5>
                <div className="form-desc p-0 products-info">
                  Here you can manage if you want your team&apos;s products to have an initiator and an approver. <br /> You can also give
                  team members access to said roles.{' '}
                  <a
                    href={approverInitiatorRolesDocumentationUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Learn more about approver-initiator roles"
                  >
                    Learn more about what this means and how you can set it up.
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="business-profile__main mt-4" style={{ borderBottom: '1px solid rgba(19, 15, 15, 0.1)' }}>
        <div className="business-profile__head">
          <span className="col-4 p-0">
            <h5>Payout Products</h5>
            <span className="description">
              These are our payout products and here you can enable/disable approvals for your team&apos;s payouts.
            </span>
          </span>
          <span className="col-6 toggle-button-wrapper">
            <div className="toggle-button">
              <div>
                <h5>Single Dashboard Payout</h5>
                <div className="description">Enable/disable approvals for single dashboard payouts</div>
              </div>
              <ToggleButton
                isOn={payoutState.isEnabled.single_payout}
                isLoading={payoutState.isToggled.single_payout}
                onToggle={canEditApprovals ? () => toggleSettings('single_payout') : () => {}}
                canToggle={canEditApprovals}
                aria-label="Enable/disable approvals for single dashboard payouts"
              />
            </div>
            <div className="toggle-button">
              <div>
                <h5>Bulk Dashboard Payout</h5>
                <div className="description">Enable/disable approvals for bulk dashboard payouts</div>
              </div>
              <ToggleButton
                isOn={payoutState.isEnabled.bulk_payout}
                isLoading={payoutState.isToggled.bulk_payout}
                onToggle={canEditApprovals ? () => toggleSettings('bulk_payout') : () => {}}
                canToggle={canEditApprovals}
                aria-label="Enable/disable approvals for bulk payouts"
              />
            </div>
          </span>
        </div>
      </div>
      {isApprovalWorkflowExist && (
        <div className="row">
          <div className="col-sm-12">
            <div className="element-wrapper p-0">
              <div className="element-box p-0">
                <InitiatorsAccordion
                  approvalWorkflows={approvalWorkflows}
                  isLoading={loadingInitiators}
                  refetchTableData={() => {}}
                  searchQueryValue={{}}
                  setQuery={() => {}}
                  isModalOpen={initiatorModalState}
                  setIsModalOpen={setInitiatorModalState}
                  initiatorState={initiatorState}
                  setInitiatorState={setInitiatorState}
                  handleSearch={handleSearch}
                  handleSelectInitiator={handleSelectInitiator}
                  handleRemoveInitiator={handleRemoveInitiator}
                  handleOtp={handleOtp}
                  view={view}
                  resetInitiatorState={() => handleResetInitiatorState(setInitiatorState)}
                  initiateUpdateInitiators={initiateUpdateInitiators}
                  authorizeUpdateInitiators={authorizeUpdateInitiators}
                  refetchApprovalWorkflows={refetchApprovalWorkflows}
                  handleUpdateProductRef={handleUpdateProductRef}
                  workflowReference={workflowReference}
                  authState={authState}
                />
              </div>
            </div>
          </div>
        </div>
      )}
      {isApprovalWorkflowExist && (
        <div className="row">
          <div className="col-sm-12">
            <div className="element-wrapper p-0">
              <div className="element-box p-0">
                <ApproversAccordion
                  approvalWorkflows={approvalWorkflows}
                  isLoading={loadingApprovers}
                  refetchTableData={() => {}}
                  searchQueryValue={{}}
                  setQuery={() => {}}
                  isModalOpen={approverModalState}
                  setIsModalOpen={setApproverModalState}
                  approverState={approverState}
                  setApproverState={setApproverState}
                  handleSearch={handleSearch}
                  handleSelectApprover={handleSelectApprover}
                  handleRemoveApprover={handleRemoveApprover}
                  handleOtp={handleOtp}
                  view={view}
                  resetApproverState={() => handleResetApproverState(setApproverState)}
                  initiateUpdateApprovers={initiateUpdateApprovers}
                  authorizeUpdateApprovers={authorizeUpdateApprovers}
                  refetchApprovalWorkflows={refetchApprovalWorkflows}
                  handleUpdateProductRef={handleUpdateProductRef}
                  workflowReference={workflowReference}
                  authState={authState}
                />
              </div>
            </div>
          </div>
        </div>
      )}
      <ApprovalWorkflowModal
        close={closeWorkflowModal}
        visible={workflowModalOpen}
        payoutType={selectedPayoutType}
        initiatorState={initiatorState}
        approverState={approverState}
        setInitiatorState={setInitiatorState}
        setApproverState={setApproverState}
        handleSearch={handleSearch}
        handleSelectInitiator={handleSelectInitiator}
        handleRemoveInitiator={handleRemoveInitiator}
        handleSelectApprover={handleSelectApprover}
        handleRemoveApprover={handleRemoveApprover}
        handleOtp={handleOtp}
        view={view}
        triggerInitiateApprovalWorkflow={triggerInitiateApprovalWorkflow}
        createApprovalWorkflow={createApprovalWorkflow}
        isSuccess={isSuccess}
        authState={authState}
      />
      <ManageApprovalWorkflowModal
        triggerInitiateManageApprovalWorkflow={triggerInitiateManageApprovalWorkflow}
        authorizeManageApprovalWorkflow={authorizeManageApprovalWorkflow}
        close={closeManageWorkflowModal}
        visible={showManageWorkflowModal}
        onToggle={handleManageToggle}
        actionType={actionType}
        authState={authState}
        otp={otp}
        setOtp={setOtp}
        handleOtpResend={handleOtpResend}
      />
    </>
  );
};

export default MultiLevelApprovals;
