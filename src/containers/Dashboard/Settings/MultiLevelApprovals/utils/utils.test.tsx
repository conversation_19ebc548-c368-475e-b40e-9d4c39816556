import React, { useState } from 'react';
import { act, render, screen } from '@testing-library/react';

import { defaultApproversStateType, defaultInitiatorsStateType, IFetchedUser } from '+types';

import { createDebouncedSearch, findUserByName, getInitiatorsAndApproversPayload } from './index';

describe('find User By Name', () => {
  const users: IFetchedUser[] = [
    { firstname: '<PERSON>yn<PERSON>', lastname: '<PERSON>wa<PERSON>', id: 1, kora_id: '482' },
    { firstname: 'Olumayowa', lastname: 'Kayode', id: 2, kora_id: '483' }
  ];

  test('should find user by name', () => {
    const user = findUserByName('<PERSON><PERSON><PERSON> Nwakor', users);
    expect(user).toEqual(users[0]);
  });

  test('should return undefined if user is not found', () => {
    const user = findUserByName('<PERSON> Johnson', users);
    expect(user).toBeUndefined();
  });
});

describe('get Initiators And Approvers Payload', () => {
  const users: IFetchedUser[] = [
    { firstname: '<PERSON>yn<PERSON>', lastname: '<PERSON><PERSON><PERSON>', id: 1, kora_id: '482' },
    { firstname: 'Olumayowa', lastname: 'Kayode', id: 2, kora_id: '483' }
  ];

  test('should return initiators and approvers payload', () => {
    const designatedInitiators = ['Lynda Nwakor'];
    const designatedApprovers = ['Olumayowa Kayode'];
    const payload = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);
    expect(payload).toEqual({
      initiators: [{ user_id: 1, user_kora_id: '482' }],
      approvers: [{ user_id: 2, user_kora_id: '483' }]
    });
  });

  test('should handle users not found', () => {
    const designatedInitiators = ['Alice Johnson'];
    const designatedApprovers = ['Bob Brown'];
    const payload = getInitiatorsAndApproversPayload(designatedInitiators, designatedApprovers, users);
    expect(payload).toEqual({
      initiators: [],
      approvers: []
    });
  });
});

describe('create Debounced Search', () => {
  const users: IFetchedUser[] = [
    { firstname: 'Lynda', lastname: 'Nwakor', id: 1, kora_id: '482' },
    { firstname: 'Olumayowa', lastname: 'Kayode', id: 2, kora_id: '483' }
  ];

  const useTestComponent = () => {
    const [state, setState] = useState<defaultInitiatorsStateType | defaultApproversStateType>({
      filteredMembers: [],
      loading: false,
      error: '',
      searchTerm: '',
      designatedApprovers: [],
      showAll: false
    });
    const debouncedSearch = createDebouncedSearch(
      setState as React.Dispatch<React.SetStateAction<defaultInitiatorsStateType | defaultApproversStateType>>,
      users
    );
    return { state, debouncedSearch };
  };

  const TestComponent = () => {
    const { state, debouncedSearch } = useTestComponent();
    return (
      <div>
        <button onClick={() => debouncedSearch('Alice')}>Search</button>
        <div>{state.error}</div>
      </div>
    );
  };

  test('should handle no matched users', async () => {
    render(<TestComponent />);

    act(() => {
      screen.getByText('Search').click();
    });

    await new Promise(resolve => setTimeout(resolve, 1000));

    expect(screen.getByText('User not found. Please check the username and try again.')).toBeInTheDocument();
  });
});
