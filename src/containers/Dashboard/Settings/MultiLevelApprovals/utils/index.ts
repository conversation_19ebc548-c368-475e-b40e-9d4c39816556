import React from 'react';
import { useDebouncedCallback } from 'use-debounce';

import { defaultApproversStateType, defaultInitiatorsStateType, IApprovalWorkflow, IFetchedUser, SubtabKeyType } from '+types';

export const productType = [
  {
    label: 'Single Payout',
    value: 'single_payout',
    key: 'single_payout'
  },
  {
    label: 'Bulk Payout',
    value: 'bulk_payout',
    key: 'bulk_payout'
  }
];

export const findUserByName = (name: string, users: IFetchedUser[]) => {
  return users.find(user => `${user.firstname} ${user.lastname}`.toLowerCase() === name.toLowerCase());
};

export const getInitiatorsAndApproversPayload = (designatedInitiators: string[], designatedApprovers: string[], users: IFetchedUser[]) => {
  const mapNamesToPayload = (names: string[]) => {
    return names
      .map(name => {
        const user = findUserByName(name, users);
        return user ? { user_id: user.id, user_kora_id: user.kora_id } : null;
      })
      .filter(user => user !== null);
  };

  const initiators = mapNamesToPayload(designatedInitiators);
  const approvers = mapNamesToPayload(designatedApprovers);
  return { initiators, approvers };
};

export const createDebouncedSearch = (
  setState: React.Dispatch<React.SetStateAction<defaultInitiatorsStateType | defaultApproversStateType>>,
  users: IFetchedUser[]
) => {
  return useDebouncedCallback((searchTerm: string) => {
    setState(prevState => ({ ...prevState, loading: true }));

    if (!searchTerm) {
      setState(prevState => ({
        ...prevState,
        filteredMembers: [],
        loading: false
      }));
      return;
    }

    const matchedUsers = users.filter(user => `${user.firstname} ${user.lastname}`.toLowerCase().includes(searchTerm.toLowerCase()));

    setTimeout(() => {
      setState(prevState => ({
        ...prevState,
        filteredMembers: matchedUsers,
        loading: false,
        error: matchedUsers.length === 0 ? 'User not found. Please check the username and try again.' : ''
      }));
    }, 500);
  }, 200);
};

export const getProductWorkflowReference = (
  product: string,
  approvalWorkflows: { data?: { type: string; reference: string }[] }
): string | undefined => {
  const productWorkflow = approvalWorkflows?.data?.find(workflow => workflow?.type === product);
  return productWorkflow?.reference;
};

export const createHandleTabChange = (
  setActiveTab: React.Dispatch<React.SetStateAction<SubtabKeyType>>,
  setQuery: (query: { tab: string; subtab: SubtabKeyType }) => void,
  searchQueryValue: { tab: string; subtab: SubtabKeyType }
) => {
  return (subtabValue: SubtabKeyType) => {
    setActiveTab(subtabValue);
    setQuery({ tab: searchQueryValue.tab, subtab: subtabValue });
  };
};

export function hasPayoutApproverAccess(
  userData: { data?: { account?: { email?: string } } },
  approvalWorkflows: IApprovalWorkflow | IApprovalWorkflow['data'],
  payoutType: 'single_payout' | 'bulk_payout'
): boolean {
  const userEmail = userData?.data?.account?.email?.toLowerCase() || '';

  const workflows = Array.isArray(approvalWorkflows) ? approvalWorkflows : (approvalWorkflows as IApprovalWorkflow)?.data;

  if (!Array.isArray(workflows)) {
    return false;
  }

  const payoutWorkflow = workflows.find(workflow => workflow.type === payoutType);
  if (!payoutWorkflow) {
    return false;
  }

  const approvers = payoutWorkflow.approvers || [];

  return approvers.some(approver => {
    const approverEmail = approver?.email?.toLowerCase() || '';
    return approverEmail === userEmail;
  });
}
