@import 'styles/base/variables';

.toggle-button-wrapper {
  background-color: #f9fbfd;
  padding: 20px;
  border-radius: 5px;
  margin-bottom: 40px;

  .toggle-button {
    display: flex;
    justify-content: space-between;

    &:nth-of-type(2) {
      margin-top: 2rem;
    }
  }
}

.action-info {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  p {
    font-size: 1rem;
    color: #915200;
  }
}

.form-group {
  position: relative;
}

.search-results {
  position: absolute;
  background: white;
  border: 1px solid #f3f4f8;
  width: 100%;
  max-height: 150px;
  overflow-y: auto;
  z-index: 1;
}

.search-results li {
  font-size: small;
  padding: 8px;
  cursor: pointer;
}

.search-results li:hover {
  background: #f3f4f8;
}

.selected-members {
  margin-top: 10px;
  font-size: small;
}

.member-box {
  display: inline-block;
  background: #f3f4f8;
  padding: 0 0 0 0.3rem;
  margin: 5px;
  border-radius: 5px;
  position: relative;
}

.more-members {
  display: inline-block;
  margin: 5px;
}

.more-members {
  position: relative;
}

.notification-box {
  position: absolute;
  bottom: 120%;
  right: 0;
  background-color: white;
  padding: 10px;
  box-shadow: 0 0 10px rgba(62, 75, 91, 0.32);
  z-index: 9999;
  white-space: nowrap;
  width: min-content;
  border-radius: 0.6rem;
}

.notification-box .member-box {
  display: block;
  margin: 5px 0;
  justify-content: space-between;
}

.products-info {
  max-width: 60%;

  @media (max-width: $breakpoint-tablet) {
    max-width: 100%;
  }
}
