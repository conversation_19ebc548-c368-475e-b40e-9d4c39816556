/* eslint-disable no-unused-vars */

import Icon from '+containers/Dashboard/Shared/Icons';
import { usePermissions } from '+hooks';
import { ApprovalRowProps, IremoveApprovalDetails } from '+types';
import { capitalizeRemovedash, getDate, getTime } from '+utils';

const ApprovalRow = ({
  trxn,
  handleDetailsAction,
  roleType
}: {
  trxn: ApprovalRowProps;
  handleDetailsAction: (arg: IremoveApprovalDetails) => void;
  roleType: 'approvers' | 'initiators';
}) => {
  const userAccess = usePermissions('approval_workflow_configuration');
  const canEditApprovals = userAccess === 'manage';

  const removeApprovalDetails = {
    reference: trxn?.workflow_reference,
    data: {
      type: trxn?.workflow_type,
      [roleType]: [
        {
          user_id: trxn?.id,
          user_kora_id: trxn?.kora_id
        }
      ]
    }
  };

  const handleSelectRole = (roleItem: IremoveApprovalDetails) => {
    handleDetailsAction(roleItem);
  };

  return (
    <div key={trxn?.id} className="div-table --history-table --row" tabIndex={0}>
      <div className="--column">
        <span className="body-row-header">Team Member:</span>
        <span>{trxn?.firstname}</span>
      </div>
      <div className="--column">
        <span className="body-row-header">Email:</span>
        <span>{trxn?.email}</span>
      </div>
      <div className="--column">
        <span className="body-row-header" data-testid="product-label">
          Product:
        </span>
        <span data-testid="product-value">{capitalizeRemovedash(trxn?.workflow_type)}</span>
      </div>
      <div className="--column">
        <span className="body-row-header">Date Assigned:</span>
        <span>
          {getDate(trxn?.dateAssigned)}
          <span className="annotation ml-1">{getTime(trxn?.dateAssigned)}</span>
        </span>
      </div>
      <div className="--column">
        {canEditApprovals && (
          <button type="button" onClick={() => handleSelectRole(removeApprovalDetails)}>
            <Icon name="details" />
          </button>
        )}
      </div>
    </div>
  );
};

export default ApprovalRow;
