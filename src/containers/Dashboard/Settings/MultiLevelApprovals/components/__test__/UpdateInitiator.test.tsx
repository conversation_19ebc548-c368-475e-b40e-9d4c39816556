/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import UpdateInitiatorsWorkflow from '../UpdateInitiators';

const mockProps = {
  close: vi.fn(),
  initiatorState: {
    searchTerm: '',
    designatedInitiators: [],
    filteredMembers: [],
    showAll: false,
    loading: false,
    error: ''
  },
  setInitiatorState: vi.fn(),
  handleSearch: vi.fn(),
  handleSelectInitiator: vi.fn(),
  handleRemoveInitiator: vi.fn(),
  handleOtp: vi.fn(),
  view: {
    visibleMembers: [],
    hiddenMembers: []
  },
  debouncedSearchInitiator: vi.fn(),
  triggerInitiateApprovalWorkflow: vi.fn(),
  createApprovalWorkflow: vi.fn(),
  setSelectedPayoutType: vi.fn(),
  otpPlaceholder: 'Enter verification code',
  initiatorsCount: 0,
  approvalWorkflows: {
    data: [
      {
        reference: 'ref-123',
        type: 'Type A',
        initiators: [],
        approvers: [],
        enabled: true,
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-01-02T00:00:00Z'
      }
    ]
  },
  isLoading: false,
  refetchTableData: vi.fn(),
  handleAccordionToggle: vi.fn(),
  selectedInitiator: null,
  setSelectedInitiator: vi.fn(),
  workflowError: null,
  searchQueryValue: {},
  setQuery: vi.fn(),
  isModalOpen: false,
  setIsModalOpen: vi.fn(),
  resetInitiatorState: vi.fn()
};

const MockedUpdateInitiatorsWorkflow = () => {
  return (
    <MockIndex>
      <UpdateInitiatorsWorkflow {...mockProps} />
    </MockIndex>
  );
};

describe('UpdateInitiatorsWorkflow', () => {
  test('has no accessibility violations', async () => {
    const { container } = render(<MockedUpdateInitiatorsWorkflow />);

    await waitFor(async () => {
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  test('renders the update initiators modal in the workflow without crashing', () => {
    render(<MockedUpdateInitiatorsWorkflow />);
    const renderInputField = screen.getByTestId('initiator');
    expect(renderInputField).toBeInTheDocument();

    expect(screen.getByPlaceholderText('Enter team member name')).toBeInTheDocument();
    expect(screen.getByText('Assign product')).toBeInTheDocument();
    expect(screen.getByText('Add Initiator(s) for Payouts')).toBeInTheDocument();
  });

  test.skip('Update initiators in the approval workflow', async () => {
    render(<MockedUpdateInitiatorsWorkflow />);

    const continueButton = screen.getByText('Continue to review');
    expect(continueButton).toBeInTheDocument();

    await userEvent.click(continueButton);

    await userEvent.click(screen.getByText(/Continue to review/));

    await waitFor(() => {
      expect(mockProps.triggerInitiateApprovalWorkflow).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(mockProps.createApprovalWorkflow).toHaveBeenCalled();
    });
  });

  test('close when "Cancel" button is clicked', async () => {
    render(<MockedUpdateInitiatorsWorkflow />);
    await userEvent.click(screen.getByText(/Cancel/));
    await waitFor(() => {
      expect(mockProps.close).toHaveBeenCalled();
    });
  });
});
