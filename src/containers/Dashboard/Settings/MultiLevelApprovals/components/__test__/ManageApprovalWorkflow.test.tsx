/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { TwoFactorAuthType } from '+types';

import ManageApprovalWorkflowModal from '../ManageApprovalWorkflowModal';

const mockProps = {
  close: vi.fn(),
  visible: true,
  onToggle: vi.fn(),
  actionType: '',
  authState: { two_factor_type: 'otp' as TwoFactorAuthType, code: '123456', identifier: '<EMAIL>' },
  otp: '',
  setOtp: vi.fn(),
  createApprovalWorkflow: vi.fn(),
  triggerInitiateManageApprovalWorkflow: vi.fn(),
  authorizeManageApprovalWorkflow: vi.fn()
};

const MockedManageApprovalWorkflowModal = (
  props: Partial<typeof mockProps> & { actionType?: 'ENABLE' | 'DISABLE' | 'CONFIRM_ENABLE' | 'CONFIRM_DISABLE' }
) => {
  return (
    <MockIndex>
      <ManageApprovalWorkflowModal
        {...mockProps}
        {...props}
        actionType={(props.actionType as 'ENABLE' | 'DISABLE') || (mockProps.actionType as 'ENABLE' | 'DISABLE')}
      />
    </MockIndex>
  );
};

describe('ManageApprovalWorkflowModal', () => {
  test('has no accessibility violations', async () => {
    const { container } = render(<MockedManageApprovalWorkflowModal />);

    await waitFor(async () => {
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  test('renders the manage approval workflow modal without crashing', () => {
    render(<MockedManageApprovalWorkflowModal />);
    expect(screen.getByText(/Enable Payouts Approval/)).toBeInTheDocument();
  });

  test('renders the OTP input when action type is CONFIRM_ENABLE', () => {
    render(<MockedManageApprovalWorkflowModal actionType="CONFIRM_ENABLE" />);
    expect(screen.getByLabelText(/otp/i)).toBeInTheDocument();
  });

  test.skip('calls createApprovalWorkflow when "Confirm" button is clicked', async () => {
    render(<MockedManageApprovalWorkflowModal actionType="CONFIRM_ENABLE" />);
    await userEvent.type(screen.getByLabelText(/otp/i), '123456');
    await userEvent.click(screen.getByText(/Yes Enable/));
    await waitFor(() => {
      expect(mockProps.createApprovalWorkflow).toHaveBeenCalled();
    });
  });

  test('calls close when "Cancel" button is clicked', async () => {
    render(<MockedManageApprovalWorkflowModal actionType="CONFIRM_ENABLE" />);
    await userEvent.click(screen.getByText(/Cancel/));
    expect(mockProps.close).toHaveBeenCalled();
  });

  test('renders the disable modal when action type is DISABLE', () => {
    render(<MockedManageApprovalWorkflowModal actionType="DISABLE" />);
    expect(screen.getByText(/Disable Payouts Approval/)).toBeInTheDocument();
  });

  test('renders the confirm disable modal when action type is CONFIRM_DISABLE', () => {
    render(<MockedManageApprovalWorkflowModal actionType="CONFIRM_DISABLE" />);
    expect(screen.getByText(/Authorize and Confirm/)).toBeInTheDocument();
  });
});
