/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { payoutType } from '+types';

import ApprovalWorkflowModal from '../ApprovalWorkflowModal';

const mockProps = {
  close: vi.fn(),
  visible: true,
  payoutType: 'single_payout' as payoutType,
  initiatorState: { searchTerm: '', designatedInitiators: [], filteredMembers: [], showAll: false, loading: false, error: '' },
  approverState: { searchTerm: '', designatedApprovers: [], filteredMembers: [], showAll: false, loading: false, error: '' },
  setInitiatorState: vi.fn(),
  setApproverState: vi.fn(),
  handleSearch: vi.fn(),
  handleSelectInitiator: vi.fn(),
  handleSelectApprover: vi.fn(),
  handleRemoveInitiator: vi.fn(),
  handleRemoveApprover: vi.fn(),
  handleOtp: vi.fn(),
  view: { visibleMembers: [], hiddenMembers: [], visibleApprovers: [], hiddenApprovers: [] },
  debouncedSearchInitiator: vi.fn(),
  debouncedSearchApprover: vi.fn(),
  triggerInitiateApprovalWorkflow: vi.fn(),
  createApprovalWorkflow: vi.fn(),
  resetInitiatorState: vi.fn(),
  resetApproverState: vi.fn()
};

const MockedApprovalWorkflowModal = () => {
  return (
    <MockIndex>
      <ApprovalWorkflowModal {...mockProps} />
    </MockIndex>
  );
};

describe('ApprovalWorkflowModal', () => {
  test('has no accessibility violations', async () => {
    const { container } = render(<MockedApprovalWorkflowModal />);

    await waitFor(async () => {
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  test('renders single payout settings modal when payoutType is single_payout', () => {
    render(<MockedApprovalWorkflowModal />);
    expect(screen.getByText(/Add Initiator for Single Payouts/i)).toBeInTheDocument();
  });

  test('renders bulk payout settings modal when payoutType is bulk_payout', () => {
    render(
      <MockIndex>
        <ApprovalWorkflowModal {...mockProps} payoutType="bulk_payout" />
      </MockIndex>
    );
    expect(screen.getByText(/Add Initiator for Bulk Payouts/i)).toBeInTheDocument();
  });
});
