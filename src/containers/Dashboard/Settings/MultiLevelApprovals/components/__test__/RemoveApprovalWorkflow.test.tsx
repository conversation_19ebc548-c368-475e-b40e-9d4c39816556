import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { vi } from 'vitest';

import { mockedRemoveApprovalWorkflow } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';

import RemoveApprovalWorkflowModal from '../RemoveApprovalWorkflowModal';

expect.extend(toHaveNoViolations);

const mockRemoveApprovalWorkflow = vi.fn().mockResolvedValue(mockedRemoveApprovalWorkflow);

vi.mock('+services/multilevel-approval-services', () => ({
  ApprovalServices: {
    useRemoveApprovalWorkflow: vi.fn(() => ({
      mutateAsync: mockRemoveApprovalWorkflow
    }))
  }
}));

const mockApproverProps = {
  close: vi.fn(),
  type: 'remove approver',
  removeApprovalDetails: {
    reference: 'KPY-APP-WF-WfxXSo6qiOE1Ruu',
    data: {
      type: 'approver',
      userId: '898'
    }
  }
};

const mockInitiatorProps = {
  close: vi.fn(),
  type: 'remove initiator',
  removeApprovalDetails: {
    reference: 'KPY-APP-WF-WfxXSo6qiOE1Ruu',
    data: {
      type: 'initiator',
      userId: '899'
    }
  }
};

describe('RemoveApprovalWorkflowModal', () => {
  test('has no accessibility violations when rendering remove approver', async () => {
    const { container } = render(
      <MockIndex>
        <RemoveApprovalWorkflowModal
          close={mockApproverProps.close}
          type={mockApproverProps.type}
          removeApprovalDetails={mockApproverProps.removeApprovalDetails}
        />
      </MockIndex>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('has no accessibility violations when rendering remove initiator', async () => {
    const { container } = render(
      <MockIndex>
        <RemoveApprovalWorkflowModal
          close={mockApproverProps.close}
          type={mockApproverProps.type}
          removeApprovalDetails={mockInitiatorProps.removeApprovalDetails}
        />
      </MockIndex>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('renders the remove approval modal for approver without crashing', () => {
    render(
      <MockIndex>
        <RemoveApprovalWorkflowModal
          close={mockApproverProps.close}
          type={mockApproverProps.type}
          removeApprovalDetails={mockApproverProps.removeApprovalDetails}
        />
      </MockIndex>
    );
    expect(screen.getByText(/Remove Approver/)).toBeInTheDocument();
  });

  test('renders the remove approval modal for initiator without crashing', () => {
    render(
      <MockIndex>
        <RemoveApprovalWorkflowModal
          close={mockInitiatorProps.close}
          type={mockInitiatorProps.type}
          removeApprovalDetails={mockInitiatorProps.removeApprovalDetails}
        />
      </MockIndex>
    );
    expect(screen.getByText(/Remove Initiator/)).toBeInTheDocument();
  });

  test('removeApprovalWorkflow when "Yes, Remove" button is clicked for approver', async () => {
    render(
      <MockIndex>
        <RemoveApprovalWorkflowModal
          close={mockApproverProps.close}
          type={mockApproverProps.type}
          removeApprovalDetails={mockApproverProps.removeApprovalDetails}
        />
      </MockIndex>
    );
    await userEvent.click(screen.getByText(/Yes, Remove/));
    expect(mockRemoveApprovalWorkflow).toHaveBeenCalled();
    await waitFor(() => {
      expect(screen.getByText(/Approver Removed/i)).toBeInTheDocument();
    });
  });

  test('removeApprovalWorkflow when "Yes, Remove" button is clicked for initiator', async () => {
    render(
      <MockIndex>
        <RemoveApprovalWorkflowModal
          close={mockInitiatorProps.close}
          type={mockInitiatorProps.type}
          removeApprovalDetails={mockInitiatorProps.removeApprovalDetails}
        />
      </MockIndex>
    );
    await userEvent.click(screen.getByText(/Yes, Remove/));
    expect(mockRemoveApprovalWorkflow).toHaveBeenCalled();
    await waitFor(() => {
      expect(screen.getByText(/Initiator Removed/i)).toBeInTheDocument();
    });
  });

  test('close when "Go Back" button is clicked', async () => {
    render(
      <MockIndex>
        <RemoveApprovalWorkflowModal
          close={mockApproverProps.close}
          type={mockApproverProps.type}
          removeApprovalDetails={mockApproverProps.removeApprovalDetails}
        />
      </MockIndex>
    );
    await userEvent.click(screen.getByText(/Go Back/));
    await waitFor(() => {
      expect(mockApproverProps.close).toHaveBeenCalled();
    });
  });
});
