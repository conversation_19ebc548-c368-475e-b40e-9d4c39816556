/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { vi } from 'vitest';

import { mockBulkInitiatorsTableData, mockSingleInitiatorsTableData } from '+__mock__/mockData';
import * as hooks from '+hooks';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import ApprovalRow from '../ApprovalRow';
import InitiatorsAccordion from '../InitiatorsAccordion';

const mockProps = {
  initiatorsCount: 2,
  approvalWorkflows: {
    data: [
      {
        reference: 'KPY-APP-WF-rvSCqjHEnRKUcXK',
        type: 'single',
        initiators: [{ id: 1, name: '<PERSON><PERSON><PERSON>' }],
        approvers: [],
        enabled: true,
        createdAt: '2025-03-01T00:00:00Z',
        updatedAt: '2025-03-01T00:00:00Z'
      },
      {
        reference: 'KPY-APP-WF-rvSCqjHEnRKUcXK',
        type: 'bulk',
        initiators: [],
        approvers: [],
        enabled: true,
        createdAt: '2025-03-01T00:00:00Z',
        updatedAt: '2025-03-01T00:00:00Z'
      }
    ]
  },
  isLoading: false,
  refetchTableData: vi.fn(),
  searchQueryValue: { tab: 'initiator', subtab: 'single_payout' },
  setQuery: vi.fn(),
  isModalOpen: false,
  setIsModalOpen: vi.fn(),
  initiatorState: {
    searchTerm: '',
    designatedInitiators: [],
    filteredMembers: [],
    showAll: true,
    loading: false,
    error: ''
  },
  resetApproverState: vi.fn(),
  resetInitiatorState: vi.fn(),
  handleSearch: vi.fn(),
  handleSelectInitiator: vi.fn(),
  handleRemoveInitiator: vi.fn(),
  handleOtp: vi.fn(),
  view: { visibleMembers: [], hiddenMembers: [] },
  debouncedSearchInitiator: vi.fn(),
  triggerInitiateApprovalWorkflow: vi.fn(),
  createApprovalWorkflow: vi.fn(),
  setSelectedPayoutType: vi.fn(),
  setInitiatorState: vi.fn()
};

const MockedInitiatorsAccordion = () => {
  return (
    <MockIndex>
      <InitiatorsAccordion {...mockProps} />
    </MockIndex>
  );
};

vi.spyOn(hooks, 'usePermissions').mockReturnValue('manage');

const getSingleInitiator = () => mockSingleInitiatorsTableData.data[0];
const getBulkInitiator = () => mockBulkInitiatorsTableData.data[0];

const MockedInitiatorRow = ({ trxn }: { trxn: unknown }) => (
  <MockIndex>
    <ApprovalRow trxn={trxn} handleDetailsAction={vi.fn()} roleType="initiators" />
  </MockIndex>
);

describe('Initiators Accordion', () => {
  test.skip('has no accessibility violations', async () => {
    const { container } = render(<MockedInitiatorsAccordion />);

    await waitFor(async () => {
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  test('renders the accordion when an initiator is added to the approval workflow', () => {
    render(<MockedInitiatorsAccordion />);
    expect(screen.getByText(/Initiators/)).toBeInTheDocument();
  });

  test('opens the modal when "Add new Initiator" button is clicked', async () => {
    render(<MockedInitiatorsAccordion />);
    await userEvent.click(screen.getByText(/Add new Initiator/));
    expect(mockProps.setIsModalOpen).toHaveBeenCalledWith(true);
  });

  test('renders the single and bulk payout tabs for initiators', () => {
    render(<MockedInitiatorsAccordion />);
    expect(screen.getByText(/Single Payouts/)).toBeInTheDocument();
    expect(screen.getByText(/Bulk Payouts/)).toBeInTheDocument();
  });

  test('fetch the initiator table data when a tab is changed for a single payout', async () => {
    render(<MockedInitiatorsAccordion />);
    await userEvent.click(screen.getByText(/Single Payouts/));
    expect(mockProps.setQuery).toHaveBeenCalledWith({ tab: 'initiator', subtab: 'single_payout' });
  });

  test('fetch the initiator table data when a tab is changed for a bulk payout', async () => {
    render(<MockedInitiatorsAccordion />);
    await userEvent.click(screen.getByText(/Bulk Payouts/));
    expect(mockProps.setQuery).toHaveBeenCalledWith({ tab: 'initiator', subtab: 'bulk_payout' });
  });

  test('show empty state for workflow table with no initiator list', async () => {
    const setQuery = vi.fn();
    const localProps = { ...mockProps, setQuery };
    render(
      <MockIndex>
        <InitiatorsAccordion {...localProps} />
      </MockIndex>
    );
    await userEvent.click(screen.getByText(/Single Payouts/));
    await userEvent.click(screen.getByText(/Bulk Payouts/));
    expect(setQuery).toHaveBeenCalledWith({ tab: 'initiator', subtab: 'single_payout' });
    await waitFor(() => {
      expect(screen.getByText(/Workflow Not Found/)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText(/There is no workflow on the list/i)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText(/Refresh/i)).toBeInTheDocument();
    });
  });

  test('renders single workflow data list, when bulk payout workflow data is empty', async () => {
    server.use(
      http.get('/api/initiator-workflow/:workflowReference/users', ({ params, request }) => {
        const url = new URL(request.url, 'http://localhost');
        const userType = url.searchParams.get('user_type');
        if (userType === 'initiators') {
          if (params.workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockSingleInitiatorsTableData, { status: 200 });
          }
          if (params.workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json({ data: [] }, { status: 200 });
          }
        }
        return HttpResponse.json({ data: [] }, { status: 200 });
      })
    );
    render(<MockedInitiatorRow trxn={getSingleInitiator()} />);
    expect(await screen.findByText(/Ugo/)).toBeInTheDocument();
    expect(await screen.findByText(/<EMAIL>/)).toBeInTheDocument();
    expect(screen.getByText('11 Jun 2025')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('renders bulk workflow data list, when single payout workflow data is empty', async () => {
    server.use(
      http.get('/api/initiator-workflow/:workflowReference/users', ({ params, request }) => {
        const url = new URL(request.url, 'http://localhost');
        const userType = url.searchParams.get('user_type');
        if (userType === 'initiators') {
          if (params.workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockBulkInitiatorsTableData, { status: 200 });
          }
          if (params.workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json({ data: [] }, { status: 200 });
          }
        }
        return HttpResponse.json({ data: [] }, { status: 200 });
      })
    );
    render(<MockedInitiatorRow trxn={getBulkInitiator()} />);
    expect(await screen.findByText(/Lynda/)).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
    expect(screen.getByText('11 Jun 2025')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('renders both single and bulk payout workflow data for available data', async () => {
    server.use(
      http.get('/api/initiator-workflow/:workflowReference/users', ({ params, request }) => {
        const url = new URL(request.url, 'http://localhost');
        const userType = url.searchParams.get('user_type');
        if (userType === 'initiators') {
          if (params.workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockSingleInitiatorsTableData, { status: 200 });
          }
          if (params.workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockBulkInitiatorsTableData, { status: 200 });
          }
        }
        return HttpResponse.json({ data: [] }, { status: 200 });
      })
    );
    render(
      <>
        <MockedInitiatorRow trxn={getSingleInitiator()} />
        <MockedInitiatorRow trxn={getBulkInitiator()} />
      </>
    );
    expect(await screen.findByText(/Lynda/)).toBeInTheDocument();
    expect(screen.getByText(/Ugo/)).toBeInTheDocument();

    const productValues = screen.getAllByTestId('product-value').map(node => node.textContent?.toLowerCase());
    expect(productValues).toContain('single payout');
    expect(productValues).toContain('bulk payout');
  });
});
