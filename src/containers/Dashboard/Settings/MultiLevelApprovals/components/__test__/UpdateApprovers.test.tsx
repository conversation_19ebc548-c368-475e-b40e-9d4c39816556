/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import UpdateApproverWorkflow from '../UpdateApprovers';

const mockProps = {
  close: vi.fn(),
  approverState: {
    searchTerm: '',
    designatedApprovers: [],
    filteredMembers: [],
    showAll: false,
    loading: false,
    error: ''
  },
  setApproverState: vi.fn(),
  handleSearch: vi.fn(),
  handleSelectApprover: vi.fn(),
  handleRemoveApprover: vi.fn(),
  handleOtp: vi.fn(),
  view: {
    visibleApprovers: [],
    hiddenApprovers: []
  },
  debouncedSearchInitiator: vi.fn(),
  triggerInitiateApprovalWorkflow: vi.fn(),
  createApprovalWorkflow: vi.fn(),
  setSelectedPayoutType: vi.fn(),
  otpPlaceholder: 'Enter verification code',
  approverCount: 0,
  approvalWorkflows: {
    data: [
      {
        reference: 'ref-123',
        type: 'Type A',
        initiators: [],
        approvers: [],
        enabled: true,
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-01-02T00:00:00Z'
      }
    ]
  },
  isLoading: false,
  refetchTableData: vi.fn(),
  handleAccordionToggle: vi.fn(),
  selectedApprover: null,
  setSelectedApprover: vi.fn(),
  workflowError: null,
  searchQueryValue: {},
  setQuery: vi.fn(),
  isModalOpen: false,
  setIsModalOpen: vi.fn(),
  resetApproverState: vi.fn(),
  approversCount: 0
};

const MockedUpdateApproverWorkflow = () => {
  return (
    <MockIndex>
      <UpdateApproverWorkflow {...mockProps} />
    </MockIndex>
  );
};

describe('UpdateApproverWorkflow', () => {
  test('has no accessibility violations', async () => {
    const { container } = render(<MockedUpdateApproverWorkflow />);

    await waitFor(async () => {
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  test('renders the update approver modal in the workflow without crashing', () => {
    render(<MockedUpdateApproverWorkflow />);
    const renderInputField = screen.getByTestId('approver');
    expect(renderInputField).toBeInTheDocument();

    expect(screen.getByPlaceholderText('Enter team member name')).toBeInTheDocument();
    expect(screen.getByText('Assign product')).toBeInTheDocument();
    expect(screen.getByText('Add Approver(s) for Payouts')).toBeInTheDocument();
  });

  test.skip('Update approver in the approval workflow', async () => {
    render(<MockedUpdateApproverWorkflow />);

    const continueButton = screen.getByText('Continue to review');
    expect(continueButton).toBeInTheDocument();

    await userEvent.click(continueButton);

    await userEvent.click(screen.getByText(/Continue to review/));

    await waitFor(() => {
      expect(mockProps.triggerInitiateApprovalWorkflow).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(mockProps.createApprovalWorkflow).toHaveBeenCalled();
    });
  });

  test('close when "Cancel" button is clicked', async () => {
    render(<MockedUpdateApproverWorkflow />);
    await userEvent.click(screen.getByText(/Cancel/));
    expect(mockProps.close).toHaveBeenCalled();
  });
});
