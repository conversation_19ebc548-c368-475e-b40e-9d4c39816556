/* eslint-disable react/jsx-props-no-spreading */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { vi } from 'vitest';

import { mockBulkApproversTableData, mockSingleApproversTableData } from '+__mock__/mockData';
import * as hooks from '+hooks';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import ApprovalRow from '../ApprovalRow';
import ApproversAccordion from '../ApproversAccordion';

const mockProps = {
  approversCount: 1,
  approvalWorkflows: {
    data: [
      {
        reference: 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ',
        type: 'single_payout',
        initiators: [],
        approvers: [{ id: 1, name: '<PERSON><PERSON>' }],
        enabled: true,
        createdAt: '2025-03-01T00:00:00Z',
        updatedAt: '2025-03-01T00:00:00Z'
      },
      {
        reference: 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ',
        type: 'bulk_payout',
        initiators: [],
        approvers: [{ id: 1, name: 'Ugo Dev' }],
        enabled: true,
        createdAt: '2025-03-01T00:00:00Z',
        updatedAt: '2025-03-01T00:00:00Z'
      }
    ]
  },
  isLoading: false,
  refetchTableData: vi.fn(),
  searchQueryValue: { tab: 'approver', subtab: 'single_payout' },
  setQuery: vi.fn(),
  isModalOpen: false,
  setIsModalOpen: vi.fn(),
  approverState: {
    searchTerm: '',
    designatedApprovers: [],
    filteredMembers: [],
    showAll: true,
    loading: false,
    error: ''
  },
  setApproverState: vi.fn(),
  handleSearch: vi.fn(),
  handleSelectApprover: vi.fn(),
  handleRemoveApprover: vi.fn(),
  handleOtp: vi.fn(),
  view: { visibleApprovers: [], hiddenApprovers: [] },
  debouncedSearchApprover: vi.fn(),
  triggerInitiateApprovalWorkflow: vi.fn(),
  createApprovalWorkflow: vi.fn(),
  setSelectedPayoutType: vi.fn(),
  resetApproverState: vi.fn(),
  workflowReference: {
    single_payout: 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ',
    bulk_payout: 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ'
  }
};

const MockedApproversAccordion = () => (
  <MockIndex>
    <ApproversAccordion {...mockProps} />
  </MockIndex>
);

const getSingleApprover = () => mockSingleApproversTableData.data[0];
const getBulkApprover = () => mockBulkApproversTableData.data[0];

const MockedApprovalRow = ({ trxn }: { trxn: unknown }) => (
  <MockIndex>
    <ApprovalRow trxn={trxn} handleDetailsAction={vi.fn()} roleType="approvers" />
  </MockIndex>
);

vi.spyOn(hooks, 'usePermissions').mockReturnValue('manage');

describe('Approvers Accordion', () => {
  test.skip('has no accessibility violations', async () => {
    const { container } = render(<MockedApproversAccordion />);
    await waitFor(async () => {
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  test('renders the accordion when an approver is added to the approval workflow', () => {
    render(<MockedApproversAccordion />);
    expect(screen.getByText(/Approvers/)).toBeInTheDocument();
  });

  test('show the modal when "Add new Approver" button is clicked', async () => {
    render(<MockedApproversAccordion />);
    await userEvent.click(screen.getByText(/Add new Approver/));
    expect(mockProps.setIsModalOpen).toHaveBeenCalledWith(true);
  });

  test('renders the approvers single and bulk payout tabs', () => {
    render(<MockedApproversAccordion />);
    expect(screen.getByText(/Single Payouts/)).toBeInTheDocument();
    expect(screen.getByText(/Bulk Payout/)).toBeInTheDocument();
  });

  test('show empty state for workflow table with no approver list', async () => {
    const setQuery = vi.fn();
    const localProps = { ...mockProps, setQuery };
    render(
      <MockIndex>
        <ApproversAccordion {...localProps} />
      </MockIndex>
    );
    await userEvent.click(screen.getByText(/Single Payout/));
    await userEvent.click(screen.getByText(/Bulk Payout/));
    expect(setQuery).toHaveBeenCalledWith({ tab: 'approver', subtab: 'single_payout' });
    await waitFor(() => {
      expect(screen.getByText(/Workflow Not Found/)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText(/There is no workflow on the list/i)).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText(/Refresh/i)).toBeInTheDocument();
    });
  });

  test('renders single workflow data list, when bulk payout workflow data is empty', async () => {
    server.use(
      http.get('/api/approval-workflow/:workflowReference/users', ({ params, request }) => {
        const url = new URL(request.url, 'http://localhost');
        const userType = url.searchParams.get('user_type');
        if (userType === 'approvers') {
          if (params.workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockSingleApproversTableData, { status: 200 });
          }
          if (params.workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json({ data: [] }, { status: 200 });
          }
        }
        return HttpResponse.json({ data: [] }, { status: 200 });
      })
    );
    render(<MockedApprovalRow trxn={getSingleApprover()} />);
    expect(await screen.findByText(/Ugo/)).toBeInTheDocument();
    expect(await screen.findByText(/ugochukwu\+<EMAIL>/)).toBeInTheDocument();
    expect(screen.getByText('6 Jun 2025')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('renders bulk workflow data list, when single payout workflow data is empty', async () => {
    server.use(
      http.get('/api/approval-workflow/:workflowReference/users', ({ params, request }) => {
        const url = new URL(request.url, 'http://localhost');
        const userType = url.searchParams.get('user_type');
        if (userType === 'approvers') {
          if (params.workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockBulkApproversTableData, { status: 200 });
          }
          if (params.workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json({ data: [] }, { status: 200 });
          }
        }
        return HttpResponse.json({ data: [] }, { status: 200 });
      })
    );
    render(<MockedApprovalRow trxn={getBulkApprover()} />);
    expect(await screen.findByText(/Lynda/)).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
    expect(screen.getByText('6 Jun 2025')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('renders both single and bulk payout workflow data for available data', async () => {
    server.use(
      http.get('/api/approval-workflow/:workflowReference/users', ({ params, request }) => {
        const url = new URL(request.url, 'http://localhost');
        const userType = url.searchParams.get('user_type');
        if (userType === 'approvers') {
          if (params.workflowReference === 'KPY-APP-WF-SINGLE_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockSingleApproversTableData, { status: 200 });
          }
          if (params.workflowReference === 'KPY-APP-WF-BULK_PAYOUT-u29wgyxVOGSdjxJ') {
            return HttpResponse.json(mockBulkApproversTableData, { status: 200 });
          }
        }
        return HttpResponse.json({ data: [] }, { status: 200 });
      })
    );
    render(
      <>
        <MockedApprovalRow trxn={getSingleApprover()} />
        <MockedApprovalRow trxn={getBulkApprover()} />
      </>
    );
    expect(await screen.findByText(/Lynda/)).toBeInTheDocument();
    expect(screen.getByText(/Ugo/)).toBeInTheDocument();

    const productValues = screen.getAllByTestId('product-value').map(node => node.textContent?.toLowerCase());
    expect(productValues).toContain('single payout');
    expect(productValues).toContain('bulk payout');
  });
});
