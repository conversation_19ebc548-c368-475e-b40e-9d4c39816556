import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import ToggleButton from '../ToggleButton';

describe('ToggleButton', () => {
  test('has no accessibility violations', async () => {
    const { container } = render(<ToggleButton isOn={false} isLoading={false} onToggle={() => {}} />);

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('renders correctly when button is off', () => {
    render(<ToggleButton isOn={false} isLoading={false} onToggle={() => {}} />);
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).not.toHaveClass('on');
  });

  test('renders correctly when button is on', () => {
    render(<ToggleButton isOn={true} isLoading={false} onToggle={() => {}} />);
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('on');
  });

  test('renders loader when button is loading', () => {
    render(<ToggleButton isOn={false} isLoading={true} onToggle={() => {}} />);
    const loader = screen.getByAltText('Loader');
    expect(loader).toBeInTheDocument();
  });

  test('handles user interaction when button is clicked', async () => {
    const onToggle = vi.fn();
    render(<ToggleButton isOn={false} isLoading={false} onToggle={onToggle} />);
    const button = screen.getByRole('button');
    await userEvent.click(button);
    expect(onToggle).toHaveBeenCalledTimes(1);
  });
});
