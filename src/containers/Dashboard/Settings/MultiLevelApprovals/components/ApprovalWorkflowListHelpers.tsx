/* eslint-disable no-unused-vars */
import { ApprovalRowProps, IremoveApprovalDetails } from '+types';

import ApprovalRow from './ApprovalRow';

export const getTableProps = () => ({
  tableClassName: '--history-table',
  headings: [
    {
      value: 'TEAM MEMBER'
    },
    {
      value: 'EMAIL'
    },
    {
      value: 'PRODUCT'
    },
    {
      value: 'DATE ASSIGNED'
    },
    {
      value: ''
    }
  ],
  emptyStateHeading: 'Workflow Not Found',
  emptyStateMessage: 'There is no workflow on the list'
});

export const InitiatorsRow = ({
  rowData,
  handleDetailsAction
}: {
  rowData: ApprovalRowProps[];
  handleDetailsAction: (arg: IremoveApprovalDetails) => void;
}) => {
  return (
    <div>
      {rowData.map((trxn: ApprovalRowProps) => (
        <ApprovalRow key={trxn.id} trxn={trxn} handleDetailsAction={handleDetailsAction} roleType="initiators" />
      ))}
    </div>
  );
};

export const ApproversRow = ({
  rowData,
  handleDetailsAction
}: {
  rowData: ApprovalRowProps[];
  handleDetailsAction: (arg: IremoveApprovalDetails) => void;
}) => {
  return (
    <div>
      {rowData.map((trxn: ApprovalRowProps) => (
        <ApprovalRow key={trxn.id} trxn={trxn} handleDetailsAction={handleDetailsAction} roleType="approvers" />
      ))}
    </div>
  );
};
