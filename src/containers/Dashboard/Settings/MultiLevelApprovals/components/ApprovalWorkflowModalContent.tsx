import React, { useState } from 'react';

import { getOtpDescriptors } from '+containers/Dashboard/Issuing/utils';
import useStore from '+store';
import { actionType, IApprovalWorkflowModal } from '+types';

import {
  getAddApproverContent,
  getAddInitiatorContent,
  getReviewApproverContent,
  getReviewInitiatorContent,
  setDisabled
} from '../helpers/approvalHelpers';

import '../index.scss';

const ApprovalWorkflowModalContent = ({
  initiatorState,
  approverState,
  setInitiatorState,
  setApproverState,
  handleSearch,
  handleSelectInitiator,
  handleSelectApprover,
  handleRemoveInitiator,
  handleRemoveApprover,
  handleOtp,
  view,
  triggerInitiateApprovalWorkflow,
  createApprovalWorkflow,
  isSuccess,
  authState
}: IApprovalWorkflowModal) => {
  const [otp, setOtp] = useState<string>('');
  const defaultMerchant = useStore(store => store.defaultMerchant);
  const merchantEmail = defaultMerchant?.email;
  const { otpLabel, otpPlaceholder } = authState
    ? getOtpDescriptors(authState.two_factor_type)
    : { otpLabel: '', otpPlaceholder: '' };
  const {
    searchTerm: initiatorSearchTerm,
    designatedInitiators,
    filteredMembers: filteredInitiatorMembers,
    showAll: showAllInitiators,
    loading: loadingInitiators,
    error: errorInitiators
  } = initiatorState;
  const {
    searchTerm: approverSearchTerm,
    designatedApprovers,
    filteredMembers: filteredApproverMembers,
    loading: loadingApprovers,
    error: errorApprovers
  } = approverState;

  const singlePayoutSettingsModal = (type: actionType, close: () => void, setType: React.Dispatch<React.SetStateAction<actionType>>) => {
    let content;
    switch (type) {
      case 'addInitiator':
        content = {
          heading: 'Add Initiator for Single Payouts',
          description:
            'Add an initiator for your company payouts. This person would be able to initiate payouts that would then need to be approved.',
          content: getAddInitiatorContent({
            initiatorSearchTerm,
            errorInitiators,
            loadingInitiators,
            filteredInitiatorMembers,
            designatedInitiators,
            handleSearch,
            handleSelectInitiator,
            handleRemoveInitiator,
            view,
            setInitiatorState,
            showAllInitiators
          }),
          firstButtonText: 'Cancel',
          secondButtonText: 'Add initiator(s)',
          secondButtonAction: () => setType('reviewInitiator'),
          secondButtonActionIsTerminal: false
        };
        break;
      case 'reviewInitiator':
        content = {
          heading: 'Review Initiator',
          description: 'Please review the initiator(s) you are adding to your payouts',
          content: getReviewInitiatorContent({
            designatedInitiators,
            handleRemoveInitiator
          }),
          firstButtonText: 'Back',
          firstButtonAction: () => {
            setType('addInitiator');
          },
          secondButtonText: 'Add initiator(s)',
          secondButtonAction: () => setType('addApprover'),
          secondButtonActionIsTerminal: false
        };
        break;
      case 'addApprover':
        content = {
          heading: 'Add Approver for Single Payouts',
          description: 'Add an approver for your company payouts. This person would be able to approve payouts that have been initiated.',
          content: getAddApproverContent({
            approverSearchTerm,
            errorApprovers,
            loadingApprovers,
            filteredApproverMembers,
            designatedApprovers,
            handleSearch,
            handleSelectApprover,
            handleRemoveApprover,
            view,
            setApproverState,
            showAllInitiators
          }),
          firstButtonText: 'Back',
          firstButtonAction: () => {
            setType('reviewInitiator');
          },
          secondButtonText: 'Review and confirm',
          secondButtonAction: async () => {
            triggerInitiateApprovalWorkflow();
            setType('reviewApprover');
          },
          secondButtonActionIsTerminal: false
        };
        break;
      case 'reviewApprover':
        content = {
          heading: 'Review',
          content: getReviewApproverContent({
            designatedInitiators,
            designatedApprovers,
            handleRemoveInitiator,
            handleRemoveApprover,
            authState,
            otpLabel,
            otpPlaceholder,
            merchantEmail,
            handleOtp,
            setOtp
          }),
          firstButtonText: 'Cancel',
          firstButtonAction: close,
          secondButtonText: 'Confirm',
          secondButtonAction: async () => {
            setOtp('');
            createApprovalWorkflow();
          },
          completedHeading: 'Single payouts approval enabled',
          completedDescription: 'Payouts approval for your team has been added successfully.',
          secondButtonActionIsTerminal: isSuccess
        };
        break;
    }

    return {
      size: 'md',
      close: () => {
        close();
        setType('addInitiator');
      },
      ...content,
      secondButtonDisable: setDisabled(type, initiatorState, approverState, otp)
    };
  };

  const bulkPayoutSettingsModal = (type: actionType, close: () => void, setType: React.Dispatch<React.SetStateAction<actionType>>) => {
    let content;
    switch (type) {
      case 'addInitiator':
        content = {
          heading: 'Add Initiator for Bulk Payouts',
          description:
            'Add an initiator for your company bulk payouts. This person would be able to initiate bulk payouts that would then need to be approved.',
          content: getAddInitiatorContent({
            initiatorSearchTerm,
            errorInitiators,
            loadingInitiators,
            filteredInitiatorMembers,
            designatedInitiators,
            handleSearch,
            handleSelectInitiator,
            handleRemoveInitiator,
            view,
            setInitiatorState,
            showAllInitiators
          }),
          firstButtonText: 'Cancel',
          secondButtonText: 'Add initiator(s)',
          secondButtonAction: () => setType('reviewInitiator'),
          secondButtonActionIsTerminal: false
        };
        break;
      case 'reviewInitiator':
        content = {
          heading: 'Review Initiator',
          description:
            'Please review the details of the initiator(s) you are adding to your bulk payouts. Ensure all information is accurate before proceeding.',
          content: getReviewInitiatorContent({
            designatedInitiators,
            handleRemoveInitiator
          }),
          firstButtonText: 'Back',
          firstButtonAction: () => {
            setType('addInitiator');
          },
          secondButtonText: 'Add initiator(s)',
          secondButtonAction: () => setType('addApprover'),
          secondButtonActionIsTerminal: false
        };
        break;
      case 'addApprover':
        content = {
          heading: 'Add Approver for Bulk Payouts',
          description:
            'Add an approver for your company bulk payouts. This person would be able to approve bulk payouts that have been initiated.',
          content: getAddApproverContent({
            approverSearchTerm,
            errorApprovers,
            loadingApprovers,
            filteredApproverMembers,
            designatedApprovers,
            handleSearch,
            handleSelectApprover,
            handleRemoveApprover,
            view,
            setApproverState,
            showAllInitiators
          }),
          firstButtonText: 'Back',
          firstButtonAction: () => {
            setType('reviewInitiator');
          },
          secondButtonText: 'Review and confirm',
          secondButtonAction: async () => {
            triggerInitiateApprovalWorkflow();
            setType('reviewApprover');
          },
          secondButtonActionIsTerminal: false
        };
        break;
      case 'reviewApprover':
        content = {
          heading: 'Review',
          content: getReviewApproverContent({
            designatedInitiators,
            designatedApprovers,
            handleRemoveInitiator,
            handleRemoveApprover,
            authState,
            otpLabel,
            otpPlaceholder,
            merchantEmail,
            handleOtp,
            setOtp
          }),
          firstButtonText: 'Cancel',
          secondButtonText: 'Confirm',
          secondButtonAction: async () => {
            setOtp('');
            createApprovalWorkflow();
          },
          completedHeading: 'Bulk payouts approval enabled',
          completedDescription: 'Bulk payouts approval for your team has been added successfully.',
          secondButtonActionIsTerminal: isSuccess
        };
        break;
    }

    return {
      size: 'md',
      close: () => {
        setType('addInitiator');
        close();
      },
      ...content,
      secondButtonDisable: setDisabled(type, initiatorState, approverState, otp)
    };
  };

  return {
    singlePayoutSettingsModal,
    bulkPayoutSettingsModal
  };
};

export default ApprovalWorkflowModalContent;
