/* eslint-disable react/jsx-props-no-spreading */

import Modal from '+dashboard/Shared/Modal';
import { ApprovalServices } from '+services/multilevel-approval-services';
import { RemoveApprovalWorkflowType } from '+types';

const RemoveApprovalWorkflowModal = ({
  close,
  type,
  removeApprovalDetails,
  refetchSingleInitiatorsWorkflow,
  refetchBulkInitiatorsWorkflow,
  refetchApprovalWorkflows,
  refetchSingleApproversWorkflow,
  refetchBulkApproversWorkflow
}: RemoveApprovalWorkflowType) => {
  if (!removeApprovalDetails) {
    return null;
  }

  const { mutateAsync: removeApprovalWorkflow } = ApprovalServices.useRemoveApprovalWorkflow({
    reference: removeApprovalDetails?.reference,
    showErrorMessage: true,
    showSuccessMessage: true
  });

  const handleRemoveInitiator = async () => {
    const initiatorIds = removeApprovalDetails?.data?.initiators?.map(user => user.user_id) || [];

    await removeApprovalWorkflow({
      userType: 'initiators',
      users: initiatorIds
    });
    refetchApprovalWorkflows?.();
    refetchSingleInitiatorsWorkflow?.();
    refetchBulkInitiatorsWorkflow?.();
  };

  const handleRemoveApprover = async () => {
    const approverIds = removeApprovalDetails?.data?.approvers?.map(user => user.user_id) || [];

    await removeApprovalWorkflow({
      userType: 'approvers',
      users: approverIds
    });
    refetchApprovalWorkflows?.();
    refetchSingleApproversWorkflow?.();
    refetchBulkApproversWorkflow?.();
  };

  const switchRoleType = (type: string | undefined) => {
    let content;
    switch (type) {
      case 'remove approver':
        content = {
          heading: 'Remove Approver',
          description:
            'Please confirm that you want to remove this team member as a payouts approver. They will no longer be able to approve payouts except they are re-invited.',
          firstButtonText: 'Go Back',
          secondButtonText: 'Yes, Remove',
          secondButtonColor: '#f32345',
          secondButtonAction: async () => {
            handleRemoveApprover();
          },
          seconButtonActionIsTerminal: true,
          completedHeading: 'Approver Removed',
          completedDescription: 'Your team member has been  removed as an approver successfully.'
        };
        break;

      default:
        content = {
          heading: 'Remove Initiator',
          description:
            'Please confirm that you want to remove this team member as a payouts initiator. They will no longer be able to initiate payouts except they are re-invited.',
          firstButtonText: 'Go Back',
          secondButtonText: 'Yes, Remove',
          secondButtonColor: '#f32345',
          secondButtonAction: async () => {
            handleRemoveInitiator();
          },
          seconButtonActionIsTerminal: true,
          completedHeading: 'Initiator Removed',
          completedDescription: 'Your team member has been  removed as an initiator successfully.'
        };
        break;
    }

    return {
      close: close,
      ...content
    };
  };
  const modalContent = switchRoleType(type);

  return <Modal {...modalContent} />;
};

export default RemoveApprovalWorkflowModal;
