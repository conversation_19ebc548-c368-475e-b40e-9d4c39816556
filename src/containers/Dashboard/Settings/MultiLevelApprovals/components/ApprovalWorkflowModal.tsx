/* eslint-disable react/jsx-props-no-spreading */
import { useEffect, useState } from 'react';

import Modal from '+dashboard/Shared/Modal';
import { actionType, IApprovalWorkflowModal } from '+types';

import ApprovalWorkflowModalContent from './ApprovalWorkflowModalContent';

const ApprovalWorkflowModal = ({
  close,
  visible,
  payoutType,
  initiatorState,
  approverState,
  setInitiatorState,
  setApproverState,
  handleSearch,
  handleSelectInitiator,
  handleSelectApprover,
  handleRemoveInitiator,
  handleRemoveApprover,
  handleOtp,
  view,
  triggerInitiateApprovalWorkflow,
  createApprovalWorkflow,
  isSuccess,
  authState
}: IApprovalWorkflowModal) => {
  const { singlePayoutSettingsModal, bulkPayoutSettingsModal } = ApprovalWorkflowModalContent({
    initiatorState,
    approverState,
    setInitiatorState,
    setApproverState,
    handleSearch,
    handleSelectInitiator,
    handleSelectApprover,
    handleRemoveInitiator,
    handleRemoveApprover,
    handleOtp,
    view,
    triggerInitiateApprovalWorkflow,
    createApprovalWorkflow,
    isSuccess,
    authState
  });

  const [type, setType] = useState<actionType>('addInitiator');

  useEffect(() => {
    if (!visible) {
      setType('addInitiator');
    }
  }, [visible]);

  const modalProps =
    payoutType === 'single_payout'
      ? singlePayoutSettingsModal(type, close ?? (() => {}), setType)
      : bulkPayoutSettingsModal(type, close ?? (() => {}), setType);

  return visible && <Modal {...modalProps} />;
};

export default ApprovalWorkflowModal;
