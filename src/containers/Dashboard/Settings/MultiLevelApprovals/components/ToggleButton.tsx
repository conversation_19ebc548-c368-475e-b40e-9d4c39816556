import React from 'react';

import { ToggleButtonProps } from '+types';

import Loader from '+assets/img/dashboard/kpy-loader.svg';

import './index.scss';

type Props = ToggleButtonProps & { canToggle?: boolean };

const ToggleButton: React.FC<Props> = ({ isOn, isLoading, onToggle, canToggle = true }) => {
  return (
    <button
      role="button"
      aria-label={isOn ? 'Turn off' : 'Turn on'}
      className={`os-toggler-w ${isOn ? 'on' : ''} ${!canToggle ? 'disabled' : ''}`}
      onClick={onToggle}
      disabled={isLoading}
      onMouseDown={e => {
        if (!canToggle) e.preventDefault();
      }}
    >
      <div className={`os-toggler-i ${!canToggle ? (isOn ? 'disabled-on' : 'disabled-off') : ''}`}>
        {isLoading ? (
          <img height={20} width={20} src={Loader} alt="Loader" aria-hidden className="live-button--loader" />
        ) : (
          <div className="os-toggler-pill"></div>
        )}
      </div>
    </button>
  );
};

export default ToggleButton;
