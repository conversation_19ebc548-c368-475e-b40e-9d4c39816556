/* eslint-disable react/jsx-props-no-spreading */
import { useEffect, useState } from 'react';

import { getOtpDescriptors } from '+containers/Dashboard/Issuing/utils';
import CountdownTimer from '+containers/Dashboard/Shared/CountdownTimer';
import Modal from '+dashboard/Shared/Modal';
import { useOTPAuth } from '+hooks';
import useStore from '+store';
import { IManageApprovalWorkflowModal } from '+types';
import { maskEmail } from '+utils';

const ManageApprovalWorkflowModal = ({
  close,
  visible,
  onToggle,
  actionType,
  authState,
  otp,
  setOtp,
  triggerInitiateManageApprovalWorkflow,
  authorizeManageApprovalWorkflow,
  handleOtpResend
}: IManageApprovalWorkflowModal) => {
  const [type, setType] = useState<string>('');
  const defaultMerchant = useStore(store => store.defaultMerchant);
  const { updateCountdownStatus, countdownIsCompleted, updateAuthState } = useOTPAuth();
  const { otpLabel, otpPlaceholder } = getOtpDescriptors(authState.two_factor_type);
  const merchantEmail = defaultMerchant?.email || '';

  const setDisabled = () => {
    switch (type) {
      case 'CONFIRM_ENABLE':
      case 'CONFIRM_DISABLE':
        if (otp) return false;
        return true;
      default:
        return false;
    }
  };

  useEffect(() => {
    setType(actionType);
  }, [actionType]);

  const getConfirmAction = () => {
    const MIN_LENGTH = 6;
    const MAX_LENGTH = 11;
    const message = {
      otp: `the OTP (one-time PIN) that was sent to your email (${maskEmail(merchantEmail)}).`,
      totp: 'the authentication code from your authenticator app',
      totp_recovery_code: 'a recovery code'
    };
    return (
      <div className="reserved-vcard-container stack-xl">
        <p>To proceed, enter {message[authState.two_factor_type] || ''}</p>
        <div className="stack-md">
          <label htmlFor="otp" className="rvc-label" style={{ fontSize: '0.8rem' }}>
            {otpLabel}
          </label>
          <input
            role="textbox"
            type="text"
            name="otp"
            id="otp"
            className="form-control"
            placeholder={otpPlaceholder}
            aria-label="otp"
            aria-describedby="max-spend-range"
            inputMode="numeric"
            autoComplete="one-time-code"
            minLength={MIN_LENGTH}
            maxLength={MAX_LENGTH}
            value={otp}
            onChange={e => setOtp(e.target.value)}
          />
          {authState.two_factor_type === 'totp' && (
            <div className="otp-cta">
              <span>Can&apos;t access authenticator app?</span>
              <button
                type="button"
                className="semibold btn btn-link"
                onClick={() => updateAuthState({ two_factor_type: 'totp_recovery_code' })}
              >
                Confirm using recovery codes
              </button>
            </div>
          )}
          {authState.two_factor_type === 'otp' && (
            <div className="otp-cta with-countdown">
              <span>You didn&apos;t receive a code?</span>
              {countdownIsCompleted ? (
                <button
                  disabled={true}
                  type="button"
                  className="semibold btn btn-link"
                  onClick={() => {
                    handleOtpResend;
                  }}
                >
                  Resend code.
                </button>
              ) : (
                <CountdownTimer targetTime={30} onCountdownEnd={() => updateCountdownStatus(true)} />
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  const switchModal = (key: string) => {
    let content;
    switch (key) {
      case 'CONFIRM_ENABLE':
        content = {
          heading: 'Authorize and Confirm',
          content: getConfirmAction(),
          firstButtonText: 'Cancel',
          secondButtonText: 'Confirm',
          secondButtonAction: async () => {
            setOtp('');
            await authorizeManageApprovalWorkflow({ enabled: true });
            onToggle();
          },
          completedHeading: 'Successful!',
          completedDescription: 'Dashboard payouts approval for your team has been enabled successfully.'
        };
        break;
      case 'CONFIRM_DISABLE':
        content = {
          heading: 'Authorize and Confirm',
          content: getConfirmAction(),
          firstButtonText: 'Cancel',
          secondButtonText: 'Confirm',
          secondButtonAction: async () => {
            setOtp('');
            await authorizeManageApprovalWorkflow({ enabled: false });
            onToggle();
          },
          completedHeading: 'Successful!',
          completedDescription: 'Dashboard payouts approval for your team has been disabled successfully.'
        };
        break;
      case 'DISABLE':
        content = {
          heading: 'Disable Payouts Approval',
          description:
            'Please confirm that you want to turn off payouts approval for your company. Your payouts will no longer require approval before processing.',
          firstButtonText: 'Go Back',
          secondButtonColor: '#f32345',
          secondButtonText: 'Yes Disable',
          secondButtonAction: async () => {
            await triggerInitiateManageApprovalWorkflow();
            setType('CONFIRM_DISABLE');
          },
          secondButtonActionIsTerminal: false
        };
        break;
      default:
        content = {
          heading: 'Enable Payouts Approval',
          description:
            'Please confirm that you want to turn on payouts approval for your company. You would now have the approver check before your payouts are processed.',
          firstButtonText: 'Go Back',
          secondButtonText: 'Yes Enable',
          secondButtonAction: async () => {
            await triggerInitiateManageApprovalWorkflow();
            setType('CONFIRM_ENABLE');
          },
          secondButtonActionIsTerminal: false
        };
        break;
    }
    return {
      close: () => {
        close();
        setType(actionType);
      },
      ...content,
      secondButtonDisable: setDisabled()
    };
  };

  return visible && <Modal {...switchModal(type)} />;
};

export default ManageApprovalWorkflowModal;
