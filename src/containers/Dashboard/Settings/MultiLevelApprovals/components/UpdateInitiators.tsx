/* eslint-disable react/jsx-props-no-spreading */
import React, { useState } from 'react';

import { getOtpDescriptors } from '+containers/Dashboard/Issuing/utils';
import { InfoIcon } from '+containers/Shared/Icons';
import Modal from '+dashboard/Shared/Modal';
import useStore from '+store';
import { IUpdateInitiatorsProps } from '+types';
import { maskEmail } from '+utils';

import { getProductWorkflowReference, productType } from '../utils';

import closeIcon from '+assets/img/dashboard/close-icon.svg';

const UpdateInitiatorsWorkflow = ({
  close = () => {},
  initiatorState,
  setInitiatorState,
  handleSearch,
  handleSelectInitiator,
  handleRemoveInitiator,
  handleOtp,
  view,
  initiateUpdateInitiators,
  handleUpdateInitiators,
  approvalWorkflows,
  handleUpdateProductRef,
  authState
}: IUpdateInitiatorsProps) => {
  const defaultMerchant = useStore(store => store.defaultMerchant);
  const merchantEmail = defaultMerchant?.email || '';
  const { otpLabel, otpPlaceholder } = authState
    ? getOtpDescriptors(authState.two_factor_type)
    : { otpLabel: '', otpPlaceholder: '' };

  const [type, setType] = useState<string>('');
  const [product, setProduct] = useState<string>('');
  const [otp, setOtp] = useState<string>('');

  const {
    searchTerm: initiatorSearchTerm,
    designatedInitiators,
    filteredMembers: filteredInitiatorMembers,
    showAll: showAllInitiators,
    loading: loadingInitiators,
    error: InitiatorSearchError
  } = initiatorState;

  const setDisabled = () => {
    switch (type) {
      case 'Review':
        if (otp) return false;
        break;
      default:
        if (loadingInitiators || InitiatorSearchError) return true;
        if (initiatorSearchTerm && loadingInitiators) return true;
        if (designatedInitiators.length > 0 && product) return false;
    }
    return true;
  };

  const handleAsssignProduct = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedProduct = e.target.value;
    setProduct(selectedProduct);

    const productReference = getProductWorkflowReference(selectedProduct, approvalWorkflows);
    if (productReference) {
      handleUpdateProductRef(productReference);
    }
  };

  const getAddInitiatorContent = () => {
    return (
      <>
        <div className="form-group">
          <label htmlFor="initiatorName" className="withdraw-label">
            <span className="dark">Team member&apos;s name</span>
          </label>
          <input
            type="text"
            className="form-control"
            id="initiatorName"
            aria-label="initiator name"
            data-testid="initiator"
            placeholder="Enter team member name"
            value={initiatorSearchTerm}
            onChange={e => handleSearch(e, 'initiator')}
          />
          {initiatorSearchTerm && !InitiatorSearchError && (
            <ul className="search-results">
              {loadingInitiators ? (
                <div className="m-2">
                  <span className="spinner-border spinner-border-sm" role="member status" aria-hidden="true" />
                  <small className="ml-2">{'Verifying team member...'}</small>
                </div>
              ) : (
                filteredInitiatorMembers.map(user => {
                  const isSelected = designatedInitiators.includes(`${user.firstname} ${user.lastname}`);
                  return (
                    <li
                      key={user.id}
                      onClick={isSelected ? undefined : () => handleSelectInitiator(`${user.firstname} ${user.lastname}`)}
                      style={isSelected ? { color: '#aabdce6b', cursor: 'default' } : { cursor: 'pointer' }}
                      aria-disabled={isSelected}
                    >
                      {user.firstname} {user.lastname}
                      {isSelected && <span style={{ marginLeft: 8, fontSize: 12, color: '#aabdce6b' }}>(Selected)</span>}
                    </li>
                  );
                })
              )}
            </ul>
          )}
          {InitiatorSearchError && <small className="text-danger">{InitiatorSearchError}</small>}
          <div className="selected-members">
            {designatedInitiators.map(member => (
              <div key={member} className="member-box">
                {member}
                <button className="" onClick={() => handleRemoveInitiator(member)}>
                  <img src={closeIcon} alt="remove member" width={9} height={9} />
                </button>
              </div>
            ))}
            {view.hiddenMembers.length > 0 && (
              <div className="more-members">
                <button
                  onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                    setInitiatorState(prevState => ({ ...prevState, showAll: !prevState.showAll }));
                  }}
                >
                  <i className="os-icon os-icon-plus icon" />
                  {view.hiddenMembers.length}
                </button>
                {showAllInitiators && (
                  <div className="notification-box">
                    {view.hiddenMembers.map(member => (
                      <div key={member} className="member-box">
                        {member}
                        <button onClick={() => handleRemoveInitiator(member)}>
                          <img src={closeIcon} alt="remove member" width={9} height={9} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        <div className="form-group">
          <label htmlFor="initiatorName" className="withdraw-label">
            <span className="dark">Assign product</span>
          </label>
          <select className="form-control" aria-label="product type" value={product} onChange={handleAsssignProduct}>
            <option value="" disabled>
              Select a product type
            </option>
            {productType.map(product => (
              <option key={product.key} value={product.value}>
                {product.label}
              </option>
            ))}
          </select>
        </div>
        <div className="action-info">
          <InfoIcon fill="#915200" width="16" height="16" />
          <p className="mt-3">
            Added team members can only <strong>initiate</strong> payouts.
          </p>
        </div>
      </>
    );
  };

  const getReviewInitiatorContent = () => {
    const MIN_LENGTH = 6;
    const MAX_LENGTH = 11;
    const message = {
      otp: `the OTP (one-time PIN) that was sent to your email (${maskEmail(merchantEmail)}).`,
      totp: 'the authentication code from your authenticator app',
      totp_recovery_code: 'a recovery code'
    };
    return (
      <>
        <div className="selected-members border-bottom mb-4 pb-4">
          <h5>Payout Initiators</h5>
          {designatedInitiators.map(initiator => (
            <div key={initiator} className="member-box">
              {initiator}
              <button onClick={() => handleRemoveInitiator(initiator)}>
                <img src={closeIcon} alt="remove member" width={9} height={9} />
              </button>
            </div>
          ))}
        </div>
        <div className="reserved-vcard-container stack-xl" style={{ marginTop: '1rem' }}>
          <h4>Authorize and confirm</h4>
          <p>To proceed, enter {message[authState?.two_factor_type] || ''}</p>
          <div className="stack-md">
            <label htmlFor="otp" className="rvc-label" style={{ fontSize: '0.8rem' }}>
              {otpLabel}
            </label>
            <input
              type="text"
              id="otp"
              className="form-control"
              placeholder={otpPlaceholder}
              aria-label="Enter otp"
              aria-describedby="max-spend-range"
              inputMode="numeric"
              autoComplete="one-time-code"
              minLength={MIN_LENGTH}
              maxLength={MAX_LENGTH}
              onChange={e => {
                handleOtp(e);
                setOtp(e.target.value);
              }}
            />
          </div>
        </div>
      </>
    );
  };

  const updateApprovalWorkflowModal = (key: string) => {
    let content;
    switch (key) {
      case 'Review':
        content = {
          heading: 'Review',
          content: getReviewInitiatorContent(),
          firstButtonText: 'Back',
          firstButtonAction: () => {
            setType('');
          },
          secondButtonText: 'Submit',
          secondButtonAction: async () => {
            setOtp('');
            await handleUpdateInitiators();
          },
          secondButtonActionIsTerminal: true,
          completedHeading: `Successful!`,
          completedDescription: 'You’ve added a new initiator(s). They can now initiate payouts'
        };
        break;

      default:
        content = {
          heading: 'Add Initiator(s) for Payouts',
          description:
            'Add an initiator(s) for your company payouts. This person would be able to initiate payouts that would then need to be approved.',
          content: getAddInitiatorContent(),
          firstButtonText: 'Cancel',
          secondButtonText: 'Continue to review',
          secondButtonAction: () => {
            initiateUpdateInitiators();
            setType('Review');
          },
          secondButtonActionIsTerminal: false
        };
        break;
    }
    return {
      close: () => {
        setType('');
        close();
      },
      ...content,
      secondButtonDisable: setDisabled()
    };
  };

  return <Modal {...updateApprovalWorkflowModal(type)} />;
};

export default UpdateInitiatorsWorkflow;
