/* eslint-disable no-unused-vars */
import { ReactNode, useState } from 'react';

import Accordion from '+containers/Dashboard/Shared/Accordion';
import Table from '+containers/Dashboard/Shared/Table';
import { Tab, TabList, TabPanel, TabPanels, Tabs } from '+containers/Shared/Tabs';
import { usePermissions, useSearchQuery } from '+hooks';
import { ApprovalServices } from '+services/multilevel-approval-services';
import { ApprovalRowProps, IApproverAccordionProps, IremoveApprovalDetails, SubtabKeyType } from '+types';
import { filteredOutObjectProperty, queriesParams } from '+utils';

import { createHandleTabChange } from '../utils';
import { ApproversRow, getTableProps } from './ApprovalWorkflowListHelpers';
import RemoveApprovalWorkflowModal from './RemoveApprovalWorkflowModal';
import UpdateApprovers from './UpdateApprovers';

import arrow from '+assets/img/dashboard/Arrow-down-dark.svg';

import './index.scss';

const subtabs: Array<{ label: string; key: SubtabKeyType; renderElement: (arg: any) => ReactNode }> = [
  {
    label: 'Single Payouts',
    key: 'single_payout',
    renderElement: ({ rowData, handleDetailsAction }: { rowData: ApprovalRowProps[]; handleDetailsAction: () => void }) => {
      return <ApproversRow rowData={rowData} handleDetailsAction={handleDetailsAction} />;
    }
  },
  {
    label: 'Bulk Payout',
    key: 'bulk_payout',
    renderElement: ({ rowData, handleDetailsAction }: { rowData: ApprovalRowProps[]; handleDetailsAction: () => void }) => {
      return <ApproversRow rowData={rowData} handleDetailsAction={handleDetailsAction} />;
    }
  }
];

const ApproversAccordion = ({
  approvalWorkflows,
  isLoading,
  refetchTableData,
  searchQueryValue,
  setQuery,
  isModalOpen,
  setIsModalOpen,
  approverState,
  setApproverState,
  handleSearch,
  handleSelectApprover,
  handleRemoveApprover,
  handleOtp,
  view,
  resetApproverState,
  initiateUpdateApprovers,
  authorizeUpdateApprovers,
  refetchApprovalWorkflows,
  handleUpdateProductRef,
  workflowReference,
  authState
}: IApproverAccordionProps) => {
  const userAccess = usePermissions('approval_workflow_configuration');
  const canEditApprovals = userAccess === 'manage';
  const [removeApprovalDetails, setRemoveApprovalDetails] = useState<IremoveApprovalDetails>();
  const [openRemoveApprovalModal, setOpenRemoveApprovalModal] = useState(false);
  const subtab: SubtabKeyType = (searchQueryValue?.subtab as SubtabKeyType) ?? subtabs[0].key;
  const [activeTab, setActiveTab] = useState<SubtabKeyType>(subtab);

  const searchQuery = useSearchQuery();
  const limit = searchQuery.value.limit || '5';
  const page = searchQuery.value.page || '1';

  const sortingParams = {
    ...filteredOutObjectProperty(searchQuery.value as unknown as Record<string, string[]>, [
      queriesParams.page,
      queriesParams.limit,
      queriesParams.sorterType,
      queriesParams.isFilterVisible
    ])
  };

  const { data: singleWorkflow, refetch: refetchSingleApproversWorkflow } = ApprovalServices.useGetApprovalWorkflowList({
    queryData: {
      workflowReference: workflowReference?.single_payout,
      params: {
        user_type: 'approvers',
        limit: limit,
        page: page,
        ...sortingParams
      }
    },
    showErrorMessage: true,
    showSuccessMessage: false,
    errorMessageBannerLevel: true
  });

  const { data: bulkWorkflow, refetch: refetchBulkApproversWorkflow } = ApprovalServices.useGetApprovalWorkflowList({
    queryData: {
      workflowReference: workflowReference?.bulk_payout,
      params: {
        user_type: 'approvers',
        limit: limit,
        page: page
      }
    },
    showErrorMessage: true,
    showSuccessMessage: false,
    errorMessageBannerLevel: true
  });

  const isWorkflowExist = approvalWorkflows?.data?.length > 0;

  const workflowData = {
    single_payout: {
      data: singleWorkflow?.data || [],
      paging: singleWorkflow?.data?.paging || {}
    },
    bulk_payout: {
      data: bulkWorkflow?.data || [],
      paging: bulkWorkflow?.data?.paging || {}
    }
  };

  const { headings, tableClassName, emptyStateHeading, emptyStateMessage } = getTableProps();

  const handleAddNewApprover = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    resetApproverState();
  };

  const handleCloseRemoveApprovalModal = () => setOpenRemoveApprovalModal(false);

  const handleDetailsAction = (removeApprovalDetails: IremoveApprovalDetails) => {
    setOpenRemoveApprovalModal(true);
    setRemoveApprovalDetails(removeApprovalDetails);
  };

  const handleUpdateApprovers = async () => {
    authorizeUpdateApprovers(refetchSingleApproversWorkflow, refetchBulkApproversWorkflow);
    refetchApprovalWorkflows();
  };

  const handleTabChange = createHandleTabChange(setActiveTab, query => setQuery({ tab: 'approver', subtab: query.subtab }), {
    ...searchQueryValue,
    tab: 'approver',
    subtab
  });

  return (
    <>
      {isWorkflowExist && (
        <Accordion
          className="payout-role-accordion"
          customToggle={<img className="toggler-arrow" src={arrow} alt="" role="presentation" />}
          title={
            <div key={null} className="initiator-box">
              <div>
                <p className="label text-capitalize">Approvers</p>
              </div>
              {canEditApprovals && (
                <div className="add-new-initiator">
                  <button className="btn" onClick={handleAddNewApprover} type="button" aria-label="Add new Approver">
                    <i className="os-icon os-icon-plus icon" aria-hidden="true" />
                    <span>Add new Approver</span>
                  </button>
                </div>
              )}
            </div>
          }
          content={
            <div>
              <Tabs defaultValue={activeTab} onChange={handleTabChange}>
                <div className="tab-list">
                  <TabList className="mt-4">
                    {subtabs.map(({ key, label }) => (
                      <Tab value={key} key={`${key}-tab`}>
                        {label}
                      </Tab>
                    ))}
                  </TabList>
                </div>
                <TabPanels>
                  {subtabs.map(({ key, renderElement }) => {
                    const { data, paging } = workflowData[key];
                    return (
                      <TabPanel key={`${key}-panel`} value={key} className="transaction_table_comp table-container">
                        <Table
                          tableClassName={tableClassName}
                          headings={headings}
                          hasPagination
                          borderedTable
                          loading={isLoading}
                          current={paging?.current}
                          limitAction={value => searchQuery.setQuery({ limit: String(value) })}
                          pageSize={paging?.page_size}
                          actionFn={value => searchQuery.setQuery({ page: String(value) })}
                          totalItems={paging?.total_items || 0}
                          emptyStateHeading={emptyStateHeading}
                          emptyStateMessage={
                            <>
                              <span>{emptyStateMessage}</span>
                              <button
                                type="button"
                                className="refetch-button"
                                onClick={() => refetchTableData()}
                                aria-label="Refresh table data"
                              >
                                <i className="os-icon os-icon-rotate-ccw" aria-hidden="true" />
                                Refresh
                              </button>
                            </>
                          }
                        >
                          {renderElement({ rowData: data?.data, handleDetailsAction: handleDetailsAction })}
                        </Table>
                      </TabPanel>
                    );
                  })}
                </TabPanels>
              </Tabs>
            </div>
          }
        />
      )}
      {isModalOpen && (
        <UpdateApprovers
          close={closeModal}
          approverState={approverState}
          setApproverState={setApproverState}
          handleSearch={handleSearch}
          handleSelectApprover={handleSelectApprover}
          handleRemoveApprover={handleRemoveApprover}
          handleOtp={handleOtp}
          view={view}
          initiateUpdateApprovers={initiateUpdateApprovers}
          handleUpdateApprovers={handleUpdateApprovers}
          approvalWorkflows={approvalWorkflows}
          handleUpdateProductRef={handleUpdateProductRef}
          authState={authState}
        />
      )}
      {openRemoveApprovalModal && (
        <RemoveApprovalWorkflowModal
          close={handleCloseRemoveApprovalModal}
          type="remove approver"
          removeApprovalDetails={removeApprovalDetails}
          refetchSingleApproversWorkflow={refetchSingleApproversWorkflow}
          refetchBulkApproversWorkflow={refetchBulkApproversWorkflow}
          refetchApprovalWorkflows={refetchApprovalWorkflows}
        />
      )}
    </>
  );
};

export default ApproversAccordion;
