import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import MultiLevelApprovals from '../index';

const MockeMultiLevelApprovals = () => {
  return (
    <MockIndex>
      <MultiLevelApprovals />
    </MockIndex>
  );
};

describe('MultiLevelApprovals', () => {
  test('has no accessibility violations', async () => {
    const { container } = render(<MockeMultiLevelApprovals />);

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('section headings are visible', () => {
    render(<MockeMultiLevelApprovals />);
    expect(screen.getByText('Approvals')).toBeInTheDocument();
    expect(screen.getByText('Payout Products')).toBeInTheDocument();
  });

  test('toggle buttons are displayed', () => {
    render(<MockeMultiLevelApprovals />);
    expect(screen.getByText('Single Dashboard Payout')).toBeInTheDocument();
    expect(screen.getByText('Bulk Dashboard Payout')).toBeInTheDocument();
  });

  test('single dashboard payout toggle button role', () => {
    render(<MockeMultiLevelApprovals />);
    const singlePayoutToggle = screen.getAllByRole('button')[0];
    expect(singlePayoutToggle).toBeInTheDocument();
  });

  test('bulk payout toggle button role', () => {
    render(<MockeMultiLevelApprovals />);
    const bulkPayoutToggle = screen.getAllByRole('button')[1];
    expect(bulkPayoutToggle).toBeInTheDocument();
  });

  test('single dashboard payout user interaction', async () => {
    render(<MockeMultiLevelApprovals />);
    const singlePayoutToggle = screen.getAllByRole('button')[0];
    await userEvent.click(singlePayoutToggle);
    expect(singlePayoutToggle).toHaveClass('os-toggler-w');
  });

  test('bulk payout toggle button user interaction', async () => {
    render(<MockeMultiLevelApprovals />);
    const bulkPayoutToggle = screen.getAllByRole('button')[1];
    await userEvent.click(bulkPayoutToggle);
    expect(bulkPayoutToggle).toHaveClass('os-toggler-w');
  });
});
