import { InfoIcon } from '+containers/Shared/Icons';
import {
  defaultApproversStateType,
  defaultInitiatorsStateType,
  GetAddApproverContentProps,
  GetAddInitiatorContentProps,
  GetReviewApproverContentProps,
  GetReviewInitiatorContentProps,
  SetDisabledType
} from '+types';
import { maskEmail } from '+utils';

import closeIcon from '+assets/img/dashboard/close-icon.svg';

export const setDisabled = (
  type: SetDisabledType,
  initiatorState: defaultInitiatorsStateType,
  approverState: defaultApproversStateType,
  otp: string
) => {
  const { searchTerm: initiatorSearchTerm, designatedInitiators, loading: loadingInitiators, error: errorInitiators } = initiatorState;
  const { searchTerm: approverSearchTerm, designatedApprovers, loading: loadingApprovers, error: errorApprovers } = approverState;

  switch (type) {
    case 'addInitiator':
      if (loadingInitiators || errorInitiators || (initiatorSearchTerm && loadingInitiators)) return true;
      if (designatedInitiators.length > 0) return false;
      break;
    case 'reviewInitiator':
      if (designatedInitiators.length > 0) return false;
      break;
    case 'addApprover':
      if (loadingApprovers || errorApprovers || (approverSearchTerm && loadingApprovers)) return true;
      if (designatedApprovers.length > 0) return false;
      break;
    case 'reviewApprover':
      if (otp) return false;
      break;
    default:
      return true;
  }

  return true;
};

export const getAddInitiatorContent = ({
  initiatorSearchTerm,
  errorInitiators,
  loadingInitiators,
  filteredInitiatorMembers,
  designatedInitiators,
  handleSearch,
  handleSelectInitiator,
  handleRemoveInitiator,
  view,
  setInitiatorState,
  showAllInitiators
}: GetAddInitiatorContentProps) => (
  <>
    <div className="form-group">
      <label htmlFor="initiatorName" className="withdraw-label">
        <span className="dark">Team member&apos;s name</span>
      </label>
      <input
        type="text"
        className="form-control"
        id="initiatorName"
        placeholder="Enter team member name"
        value={initiatorSearchTerm}
        onChange={e => handleSearch(e, 'initiator')}
      />
      {initiatorSearchTerm && !errorInitiators && (
        <ul className="search-results">
          {loadingInitiators ? (
            <div className="m-2">
              <span className="spinner-border spinner-border-sm" role="member status" aria-hidden="true" />
              <small className="ml-2">{'Verifying team member...'}</small>
            </div>
          ) : (
            filteredInitiatorMembers.map(user => {
              const isSelected = designatedInitiators.includes(`${user.firstname} ${user.lastname}`);
              return (
                <li
                  key={user.id}
                  onClick={isSelected ? undefined : () => handleSelectInitiator(`${user.firstname} ${user.lastname}`)}
                  style={isSelected ? { color: '#aabdce6b', cursor: 'default' } : { cursor: 'pointer' }}
                  aria-disabled={isSelected}
                >
                  {user.firstname} {user.lastname}
                  {isSelected && <span style={{ marginLeft: 8, fontSize: 12, color: '#aabdce6b' }}>(Selected)</span>}
                </li>
              );
            })
          )}
        </ul>
      )}
      {errorInitiators && <small className="text-danger">{errorInitiators}</small>}
      <div className="selected-members">
        {designatedInitiators.map(member => (
          <div key={member} className="member-box">
            {member}
            <button className="" onClick={() => handleRemoveInitiator(member)}>
              <img src={closeIcon} alt="remove member" width={9} height={9} />
            </button>
          </div>
        ))}
        {view.hiddenMembers.length > 0 && (
          <div className="more-members">
            <button
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                setInitiatorState(prevState => ({ ...prevState, showAll: !prevState.showAll }));
              }}
            >
              <i className="os-icon os-icon-plus icon" />
              {view.hiddenMembers.length}
            </button>
            {showAllInitiators && (
              <div className="notification-box">
                {view.hiddenMembers.map(member => (
                  <div key={member} className="member-box">
                    {member}
                    <button onClick={() => handleRemoveInitiator(member)}>
                      <img src={closeIcon} alt="remove member" width={9} height={9} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
      <small>You can add as many team members as you need.</small>
    </div>
    <div className="action-info">
      <InfoIcon fill="#915200" width="16" height="16" />
      <p className="mt-3">
        Added team members can only <strong>initiate</strong> payments
      </p>
    </div>
  </>
);

export const getReviewInitiatorContent = ({ designatedInitiators, handleRemoveInitiator }: GetReviewInitiatorContentProps) => (
  <div className="selected-members">
    <h5>Payout Initiators</h5>
    {designatedInitiators.map(initiator => (
      <div key={initiator} className="member-box">
        {initiator}
        <button onClick={() => handleRemoveInitiator(initiator)}>
          <img src={closeIcon} alt="remove member" width={9} height={9} />
        </button>
      </div>
    ))}
  </div>
);

export const getAddApproverContent = ({
  approverSearchTerm,
  errorApprovers,
  loadingApprovers,
  filteredApproverMembers,
  designatedApprovers,
  handleSearch,
  handleSelectApprover,
  handleRemoveApprover,
  view,
  setApproverState,
  showAllInitiators
}: GetAddApproverContentProps) => (
  <>
    <div className="form-group">
      <label htmlFor="approverName" className="withdraw-label">
        <span className="dark">Team member&apos;s name</span>
      </label>
      <input
        type="text"
        className="form-control"
        id="approverName"
        placeholder="Enter team member name"
        value={approverSearchTerm}
        onChange={e => handleSearch(e, 'approver')}
      />
      {approverSearchTerm && !errorApprovers && (
        <ul className="search-results">
          {loadingApprovers ? (
            <div className="m-2">
              <span className="spinner-border spinner-border-sm" role="member status" aria-hidden="true" />
              <small className="ml-2">{'Verifying team member...'}</small>
            </div>
          ) : (
            filteredApproverMembers.map(user => {
              const isSelected = designatedApprovers.includes(`${user.firstname} ${user.lastname}`);
              return (
                <li
                  key={user.id}
                  onClick={isSelected ? undefined : () => handleSelectApprover(`${user.firstname} ${user.lastname}`)}
                  style={isSelected ? { color: '#aaa', cursor: 'not-allowed' } : { cursor: 'pointer' }}
                  aria-disabled={isSelected}
                >
                  {user.firstname} {user.lastname}
                  {isSelected && <span style={{ marginLeft: 8, fontSize: 12, color: '#aaa' }}>(Selected)</span>}
                </li>
              );
            })
          )}
        </ul>
      )}
      {errorApprovers && <small className="text-danger">{errorApprovers}</small>}
      <div className="selected-members">
        {designatedApprovers.map(approver => (
          <div key={approver} className="member-box">
            {approver}
            <button className="" onClick={() => handleRemoveApprover(approver)}>
              <img src={closeIcon} alt="remove approver" width={9} height={9} />
            </button>
          </div>
        ))}
        {view.hiddenApprovers.length > 0 && (
          <div className="more-members">
            <button
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                setApproverState(prevState => ({ ...prevState, showAll: !prevState.showAll }));
              }}
            >
              <i className="os-icon os-icon-plus icon" />
              {view.hiddenApprovers.length}
            </button>
            {showAllInitiators && (
              <div className="notification-box">
                {view.hiddenApprovers.map(member => (
                  <div key={member} className="member-box">
                    {member}
                    <button onClick={() => handleRemoveApprover(member)}>
                      <img src={closeIcon} alt="remove member" width={9} height={9} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
    <div className="action-info">
      <InfoIcon fill="#915200" width="16" height="16" />
      <p className="mt-3">
        Added team members can only <strong>approve</strong> payments
      </p>
    </div>
  </>
);

export const getReviewApproverContent = ({
  designatedInitiators,
  designatedApprovers,
  handleRemoveInitiator,
  handleRemoveApprover,
  authState,
  otpLabel,
  otpPlaceholder,
  merchantEmail,
  handleOtp,
  setOtp
}: GetReviewApproverContentProps) => {
  const MIN_LENGTH = 6;
  const MAX_LENGTH = 11;
  const message = {
    otp: `the OTP (one-time PIN) that was sent to your email (${maskEmail(merchantEmail)}).`,
    totp: 'the authentication code from your authenticator app',
    totp_recovery_code: 'a recovery code'
  };
  return (
    <>
      <div className="selected-members">
        <h5>Payout Initiators</h5>
        {designatedInitiators.map(initiator => (
          <div key={initiator} className="member-box">
            {initiator}
            <button onClick={() => handleRemoveInitiator(initiator)}>
              <img src={closeIcon} alt="remove member" width={9} height={9} />
            </button>
          </div>
        ))}
      </div>
      <div className="selected-members border-bottom mb-4 pb-4">
        <h5>Payout Approvers</h5>
        {designatedApprovers.map(approver => (
          <div key={approver} className="member-box">
            {approver}
            <button onClick={() => handleRemoveApprover(approver)}>
              <img src={closeIcon} alt="remove approver" width={9} height={9} />
            </button>
          </div>
        ))}
      </div>
      <div className="reserved-vcard-container stack-xl" style={{ marginTop: '1rem' }}>
        <h4>Authorize and confirm</h4>
        <p>To proceed, enter {message[authState.two_factor_type as keyof typeof message] || ''}</p>
        <div className="stack-md">
          <label htmlFor="otp" className="rvc-label" style={{ fontSize: '0.8rem' }}>
            {otpLabel}
          </label>
          <input
            type="text"
            name="otp"
            id="otp"
            className="form-control"
            placeholder={otpPlaceholder}
            aria-describedby="max-spend-range"
            inputMode="numeric"
            autoComplete="one-time-code"
            minLength={MIN_LENGTH}
            maxLength={MAX_LENGTH}
            onChange={e => {
              handleOtp(e);
              setOtp(e.target.value);
            }}
          />
        </div>
      </div>
    </>
  );
};

export const approverInitiatorRolesDocumentationUrl = 'https://youtu.be/tBdewfSTOZY';
