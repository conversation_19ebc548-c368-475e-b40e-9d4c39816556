.referrals-heading-box {
  @media screen and (min-width: 767px) {
    display: flex;
    justify-content: space-between;
    padding: 1.2rem 0 0.3rem 0;
  }
}

.referral-info {
  background-color: #f9fbfd;
  border-radius: 1.5rem;
  padding-top: 1.5rem;

  .referral-info-header {
    border-bottom: 1px solid #dde2ec;
    align-items: center;
    text-align: center;
    padding: 1.5rem 0;

    > span {
      width: fit-content;
      background-color: #eaf2fe;
      padding: 0.4rem;
      border-radius: 0.2rem;
    }

    > h6 {
      font-size: medium;
      margin-top: 0.7rem;
    }

    > p {
      font-size: small;
      color: #a9afbc;
      max-width: 17.3rem;
      margin: auto;
      text-align: center;
    }
  }

  .referral-info-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    margin: 0 6.25rem;
    padding: 2rem 0;

    .referral-info-checkbox {
      width: 100%;
      justify-content: center;
      gap: 0.1rem;

      > input {
        width: 1rem;
        height: 1rem;
        margin-right: 0.1rem;
        margin-top: 0.2rem;
      }
    }

    .link--btn {
      margin: 0;
      text-decoration: underline;
      font-size: 0.8rem;
      font-weight: 500;
    }

    @media screen and (max-width: 804px) {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 0;

      > div {
        align-items: center;
        justify-content: center;
        text-align: center;
        margin: 0.51rem 0;
      }

      .referral-btn {
        padding: 0.4rem 0.2rem;
      }
    }

    > div > h6 {
      font-size: small;
      color: #94a7b7;
    }

    .referral-btn {
      background-color: #2376f3;
      color: #ffffff;
      border-radius: 0.36rem;
      padding: 0.4rem 1.5rem;
      font-size: small;
      font-weight: 500;
      cursor: pointer;
    }
  }

  .referral-info-desc {
    display: flex;
    flex-direction: row;
    margin: 1.5rem 6.25rem;
    gap: 1rem;

    @media screen and (max-width: 1357px) {
      flex-wrap: wrap;
    }

    @media screen and (max-width: 954px) {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 1.5rem 1rem;
    }

    .referral-info-card {
      background-color: #ffffff;
      padding: 1rem;
      width: 100%;
      max-width: 19rem;
      border: 3px solid #f1f6fa;
      border-radius: 0.76rem;

      > div {
        display: flex;
        flex-direction: column;
        gap: 0.4rem;
        align-items: left;
        margin-bottom: 0.6rem;

        > h6 {
          margin: 0;
          font-size: small;
          margin-top: 0.6rem;
        }
      }
      > p {
        font-size: 0.8rem;
        width: 16rem;
        margin-bottom: 0;
        max-width: 100%;
      }
    }
  }
}

.referrals-heading-content {
  padding: 1.5rem 0;
}

.referral-link {
  font-size: small;

  > p {
    color: #3E4B5B;
  }

  .referrals-cont-copy {
    display: flex;
    flex-direction: column;
    color: #8c91a5;
    font-weight: 500;
    font-size: small;
    background-color: #F1F6FA;
    width: fit-content;
    justify-content: space-between;
    padding: 0.2rem 0.5rem;
    border: 1px solid #DDE2EC;
    border-radius: 0.3rem;
    margin-top: 0.5rem;

    .referrals-btn-copy {
      margin-left: 2rem;
      color: #2376F3;
      background-color: #ffffff;
      width: 6.16rem;
      border-radius: 3rem;
      padding: 0.2rem 0.3rem;
      font-weight: 500;
    }
  }

  .referrals-cont-count {
    display: flex;
    flex-direction: row;
    color: #000000;
    font-weight: 400;
    min-width: fit-content;
    font-size: small;
    background-color: #48ceb01a;
    width: 27%;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin-top: 0.5rem;

    > span {
      margin-left: 1rem;
      font-weight: 500;
      color: #009a49;
    }
  }
}

.referrals-spinner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding-top: 3rem;

  > p {
    margin-top: 1rem;
    font-size: small;
    color: #000000;
  }

  svg {
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

.referral-btn-disabled {
  background-color: #2376F3 !important;
  color: #ffffff !important;
  opacity: 0.5;
  cursor:not-allowed;
  pointer-events: none;
}
