import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { expect } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import Referrals from '../../index';
import ReferralsInfo from '../ReferralsInfo';

const Component = () => (
  <MockIndex>
    <Referrals />
  </MockIndex>
);

describe('Referrals', () => {
  it('should render Referrals', async () => {
    render(<Component />);

    await waitFor(() => {
      expect(screen.getByText('Referrals')).toBeInTheDocument();
    });
  });

  it('should be accessible', async () => {
    const { container } = render(<Component />);

    const result = await axe(container);
    expect(result).toHaveNoViolations();
  });

  it('should render Referrals with stage info', async () => {
    render(<Component />);

    await waitFor(() => {
      const linkComponentHeader = screen.queryByText('Referral Link');
      expect(linkComponentHeader).not.toBeInTheDocument();
    });
    await waitFor(() => {
      const infoComponentHeader = screen.getByText('Refer and earn!');
      expect(infoComponentHeader).toBeInTheDocument();
    });
  });

  it('should render Referrals with stage link when the generate button is clicked', async () => {
    let shouldFail = true;
    server.use(
      http.get('http://localhost:3000/api/referral/link', () =>
        HttpResponse.json(
          {
            data: {
              link: 'https://merchant.koraapi.com/auth/signup?referral_code=TFkr8J6rD6',
              count: 0
            }
          },
          { status: shouldFail ? 400 : 200 }
        )
      )
    );
    render(<Component />);
    const checkbox = await screen.findByTestId('terms-referral');
    await userEvent.click(checkbox);
    const generateLinkButton = await screen.findByText('Generate Link');
    shouldFail = false;
    await userEvent.click(generateLinkButton);
    const code = await screen.findByLabelText('Copy https://merchant.koraapi.com/auth/signup?referral_code=TFkr8J6rD6');
    expect(code).toBeInTheDocument();
  });

  it('should enable the "Generate Link" button only when the terms checkbox is checked', async () => {
    const mockSetStage = vi.fn();
    render(<ReferralsInfo setStage={mockSetStage} />);
    const checkbox = screen.getByRole('checkbox', { name: /terms/i });
    expect((checkbox as HTMLInputElement).checked).toBe(false);
    const generateLinkButton = screen.getByRole('button', { name: /generate link/i });
    expect(generateLinkButton).toBeDisabled();

    await userEvent.click(checkbox);
    expect((checkbox as HTMLInputElement).checked).toBe(true);
    expect(generateLinkButton).not.toBeDisabled();
    await userEvent.click(checkbox);
    expect((checkbox as HTMLInputElement).checked).toBe(false);
    expect(generateLinkButton).toBeDisabled();
  });
});
