import React, { useState } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import { IReferralsInfoCards } from '+types';

import { ReferralsInfoCards } from './ReferralsInfoCards';

const ReferralsInfo = ({ setStage }: { setStage: (value: 'info' | 'link') => void }) => {
  const onSave = () => {
    setStage('link');
  };

  const [hasAgreed, setHasAgreed] = useState(false);

  const handleAgreementChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHasAgreed(e.target.checked);
  };

  const generateLink: Array<IReferralsInfoCards> = [
    {
      title: 'Generate link',
      description: 'Generate your exclusive link and send to your business partners telling them about <PERSON>ra.',
      icon: 'linkHook'
    },
    {
      title: 'Registration',
      description: 'Have other businesses  sign up and begin making transactions on Kora.',
      icon: 'userProfile'
    },
    {
      title: 'Earn Amazing Commissions!',
      description: 'Start earning! Share your link with everyone who needs a reliable payment partner.',
      icon: 'briefcase'
    }
  ];
  return (
    <section className="referral-info">
      <div className="referral-info-header">
        <span>
          <Icon name="thumbsUp" width={20} height={20} fill={'#2376F3'} />
        </span>
        <h6>Refer and earn!</h6>
        <p>Follow the steps below to start earning awesome commissions. </p>
      </div>
      <div className="referral-info-desc">
        {generateLink.map((item, index) => (
          <ReferralsInfoCards key={index} title={item.title} description={item.description} icon={item.icon} />
        ))}
      </div>

      <div className="referral-info-footer">
        <div className="auth-check mt-3 referral-info-checkbox">
          <input
            checked={hasAgreed}
            onChange={handleAgreementChange}
            type="checkbox"
            aria-label="terms"
            required
            data-testid="terms-referral"
          />
          <div className="check-label ml-2">
            I have read and understand the terms
            <span className="link-text">
              {' '}
              <a className="link--btn" href="https://www.korahq.com/referral-terms" target="_blank" rel="noopener noreferrer">
                Kora&#39;s&nbsp;Referral&nbsp;Policy.
              </a>
            </span>
          </div>
        </div>
        <button
          type="button"
          onClick={onSave}
          className={`referral-btn small mt-3 ${!hasAgreed ? 'referral-btn-disabled' : ''}`}
          disabled={!hasAgreed}
        >
          Generate Link
        </button>
      </div>
    </section>
  );
};

export default ReferralsInfo;
