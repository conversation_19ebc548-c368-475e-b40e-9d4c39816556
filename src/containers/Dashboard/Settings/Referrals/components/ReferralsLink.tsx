import { useEffect } from 'react';

import Copyable from '+containers/Dashboard/Shared/Copyable';
import Icon from '+containers/Dashboard/Shared/Icons';
import { MerchantServices } from '+services/merchant-services';
import useStore from '+store';
import { IReferralsData } from '+types';
import { truncateString } from '+utils';

const ReferralsLink = ({
  setStage,
  data,
  refetchLink
}: {
  setStage: (value: 'info' | 'link') => void;
  data?: IReferralsData | null;
  refetchLink: () => void;
}) => {
  const profile = useStore(state => state.profile);
  const { mutate, isLoading } = MerchantServices.useGenerateLink({
    onSuccess: () => {
      refetchLink();
    },
    onError: () => {
      setStage('info');
    }
  });

  useEffect(() => {
    if (!data) mutate({ email: profile?.email });
  }, []);

  return isLoading ? (
    <div className="referrals-spinner">
      <Icon name="circleSpinner" width={20} height={20} />
      <p>Generating Link...</p>
    </div>
  ) : (
    <>
      <div className="referrals-heading-content">
        <div className="referral-link-box">
          <div className="referral-link">
            <p>Referral Link</p>
            <div className="referrals-cont-copy">
              <span>
                {!isLoading && (
                  <Copyable
                    text={data?.data?.link ?? ''}
                    textModifier={text => truncateString(text, 49)}
                    buttonClassName="referrals-btn-copy"
                    label="Copy Link"
                    spanClassName="p-2"
                    showCopyText
                  />
                )}
              </span>
            </div>
          </div>
        </div>
      </div>
      {(data?.data?.count ?? 0) >= 0 ? (
        <div className="referrals-heading-content">
          <div>
            <h6>Total Referrals</h6>
            <div className="form-desc" style={{ fontSize: '0.86rem', border: 'none', marginBottom: '0', maxWidth: '30rem' }}>
              Below, you will find the total number of successful referrals you have made.
            </div>
          </div>
          <div className="referral-link">
            <p className="referrals-cont-count">
              Successful Referrals:
              <span>{data?.data?.count}</span>
            </p>
          </div>
        </div>
      ) : null}
    </>
  );
};

export default ReferralsLink;
