import { cleanup, render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import Teams from '../index';

afterEach(cleanup);
afterEach(() => vi.clearAllMocks());
expect.extend(toHaveNoViolations);

const MockedTeams = () => {
  return (
    <MockIndex>
      <Teams />
    </MockIndex>
  );
};

describe('Teams', () => {
  // Default tests for all components/pages

  test('Renders Teams', () => {
    render(<MockedTeams />);
  });

  test('Teams is accessible', async () => {
    const { container } = render(<MockedTeams />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
