.conversions {
  padding-bottom: 1.5rem;
  .settlement-currency {
    &__action {
      align-items: flex-start;
      display: flex;
      gap: 2rem;
      justify-content: space-between;
      margin: 2.5rem 0 0;

      h5 {
        align-items: center;
        display: flex;
        gap: 0.5rem;
      }

      p {
        max-width: 440px;
      }
    }

    .toggle {
      align-items: center;
      display: flex;
      gap: 0.4rem;

      input {
        width: 1rem;
        height: 1rem;
      }
    }
  }

  .conversions-markup {
    background-color: #f9fbfd;
    font-size: 400;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    margin: 2rem 0 0;
    opacity: 0;
    padding: 1.5rem 0rem 0 0rem;
    transition: opacity 1s ease-in-out;

    &__edit {
      align-items: center;
      display: flex;
      flex-wrap: wrap;
      gap: 2rem 4rem;
      justify-content: space-between;
      padding: 0rem 1.5rem 0 1.5rem;
      background-color: #f9fbfd;

      &-markup {
        width: 60%;
        p {
          margin: 0.5rem 0 0;
        }
      }
      &-wrapper {
        border: 2px solid #dde2ec;
        border-radius: 5px;
        display: flex;
        outline: none;
        position: relative;
        width: 20rem;

        input,
        select {
          border: 0;
          outline: 0;
          padding: 0.5rem 1rem;
          width: 100%;

          &::placeholder {
            color: #94a7b7;
          }
        }

        option[disabled] {
          color: red;
        }

        svg {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
        }

        .select-wrapper {
          border-right: 2px solid #dde2ec;
          position: relative;
          width: 60px;
        }
      }
    }

    &__markup-rate {
      background: #fff7ed;
      border-radius: 10px;
      padding: 16px 24px 16px 24px;
      font-size: 14px;
      align-items: flex-start;
      display: flex;
      gap: 1rem;

      p {
        margin: 0;
        font-weight: 400;
        color: #3e4b5b;
        font-size: 14px;
      }
    }

    &__disabled-account {
      background: #eaf2fe;
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      border-radius: 10px;
      opacity: 0px;
      padding: 10px;
      margin-top: 20px;

      p {
        margin: 0;
        font-weight: 400;
        color: #3e4b5b;
        font-size: 14px;

        a {
          color: #3e4b5b;
          text-decoration: underline;
          font-weight: 600;
        }
      }
    }
  }

  .btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 1rem 1rem;
  }

  .btn-green {
    align-items: center;
    background-color: #24b314;
    display: flex;
    color: #fff;
    gap: 0.5rem;
    padding: 0.5rem 1rem;

    &:hover:disabled {
      color: #fff;
    }
  }

  .modal-ui {
    .activate-conversions {
      background-color: #f3f4f8;

      border-radius: 10px;
      margin: 0 0 1rem 0;

      &-active-currency {
        border-bottom: 1px solid rgba(128, 128, 128, 0.202);

        span {
          display: block;
          padding: 1rem;
        }
      }

      ul {
        all: unset;
        display: block;
        list-style-type: none;
        padding: 0 1rem 0.5rem;

        li {
          display: flex;
          justify-content: space-between;
          font-weight: 500;
          margin: 0.75rem 0;

          span:nth-of-type(1) {
            color: #94a7b7;
          }
        }
      }
    }

    .alert {
      align-items: flex-start;
      background: #fff7ed;
      border: 1px solid #fff7ed;
      border-radius: 10px;
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
      padding: 1rem;

      img {
        margin-top: 2px;
      }

      strong {
        font-weight: 500;
        color: #292b2c;
        font-size: 14px;
      }
    }

    &.settlement-destination {
      .select-wrapper {
        margin: 0 0 1rem;
        position: relative;

        select {
          border: 2px solid #dde2ec;
          cursor: pointer;
          outline: none;
          padding: 0.5rem 1rem;
          width: 100%;
        }

        svg {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }

  .conversion-success {
    text-align: center;

    .success-icon {
      margin: 1rem 0;
    }

    &__text {
      margin: 0 0;
    }
    &__dismiss {
      all: unset;
      color: #2376f3;
      cursor: pointer;
      font-weight: 600;
      margin: 2rem 0;
    }
  }
}

@media (min-width: 768px) {
  .conversions {
    .conversions-markup {
      &__info {
        &-alert {
          align-items: center;
        }
      }
    }
  }
}

.conversions-markup.visible {
  opacity: 1;
}
