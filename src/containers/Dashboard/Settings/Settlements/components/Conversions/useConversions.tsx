import { useEffect, useRef } from 'react';

import { useReducerState } from '+hooks';
import { SettlementServices } from '+services/settlement-services';
import { IModalProps } from '+types';
import { ConversionsPayloadType, TConversionsState, TCurrencyMarkup, TCurrencyStructure } from '+types/conversions';

import { conversionRateSample, errorText, modalContent, successText } from './data';

export default function useConversions(activeCurrency: string) {
  const [state, setState] = useReducerState<TConversionsState>({
    activeModal: '',
    settlementCurrency: '',
    currencyList: [],
    rateMarkup: '',
    enabled: false,
    activated: false
  });

  const currencySettings = useRef<{
    [key: string]: {
      enabled: boolean;
      activated: boolean;
      rate: string;
      limit: string;
    };
  }>({});

  const { isFetching } = SettlementServices.useGetCurrencyConversionsSettlementConfig({
    params: { currency: activeCurrency },
    errorMessage: 'There has been an error getting currency conversion status',
    onSuccess: data => {
      let currencyStructure = {} as TCurrencyStructure;
      let activatedCurrency: keyof TCurrencyStructure = '';
      let enabledCurrency = false;

      Object.entries((data.data || {}) as TCurrencyMarkup).forEach(([key, value]) => {
        if (value.activated) activatedCurrency = key;
        if (value.enabled) enabledCurrency = value.enabled;

        currencyStructure = {
          ...currencyStructure,
          [key]: {
            activated: value.activated,
            enabled: value.enabled,
            rate: (value.markup.merchant.value || 0).toFixed(2),
            limit: (value.markup.merchant.limit || 0).toFixed(2)
          }
        };
      });

      const isAnyCurrencyActivated = Object.values(data.data).some((currency: any) => currency.activated);

      currencySettings.current = currencyStructure;
      setState({ currencyList: Object.keys(currencyStructure) });

      if (!activatedCurrency) {
        setState({ settlementCurrency: '', rateMarkup: '', enabled: enabledCurrency, activated: isAnyCurrencyActivated });
        return;
      }

      setState({
        settlementCurrency: activatedCurrency,
        rateMarkup: currencyStructure[activatedCurrency].rate,
        enabled: enabledCurrency,
        activated: isAnyCurrencyActivated
      });
    }
  });

  const { mutateAsync: updateConversionsAsync } = SettlementServices.useUpdateConversionsSettlementConfig({
    bannerLevel: true,
    currency: activeCurrency,
    reInvalidate: true
  });

  const updateConversions = async (arg: ConversionsPayloadType) =>
    await updateConversionsAsync({ from_currency: activeCurrency, to_currency: state.settlementCurrency, ...arg });

  const disableSettlementBtn = () => {
    if (!currencySettings.current[state.settlementCurrency]) return true;
    const { rate, activated } = currencySettings.current[state.settlementCurrency];

    return !!(+rate === +state.rateMarkup && activated);
  };

  const toggleConversions = () => {
    if (!state.currencyList.length) return;
    if (!state.activated) return;
    if (!state.enabled) {
      setState({ enabled: true });
      return;
    }

    if (currencySettings.current[state.settlementCurrency]?.enabled) {
      setState({ activeModal: 'disableConversions' });
    } else setState({ enabled: false });
  };

  const modalDetails = {
    enableConversion: {
      ...modalContent.enableConversion,
      content: modalContent.enableConversion.content({
        defaultCurrency: activeCurrency,
        rateMarkup: state.rateMarkup,
        currency: state.settlementCurrency
      }),
      secondButtonAction: async () => {
        const data = await updateConversions({
          enable: true,
          markup: +state.rateMarkup || 0
        });
        if (data) setState({ activeModal: 'enableConversionSuccess' });
      }
    },

    enableConversionSuccess: {
      ...modalContent.enableConversionSuccess,
      content: modalContent.enableConversionSuccess.content({
        close: () => setState({ activeModal: '' }),
        text: successText({ fromCurrency: state.settlementCurrency, toCurrency: activeCurrency })
      })
    },

    disableConversions: {
      ...modalContent.disableConversions,
      content: modalContent.disableConversions.content(activeCurrency),
      secondButtonAction: async () => {
        await updateConversions({ enable: false });
        setState({ activeModal: 'disableConversionSuccess' });
      }
    },

    disableConversionSuccess: {
      ...modalContent.disableConversionSuccess,
      content: modalContent.enableConversionSuccess.content({ close: () => setState({ activeModal: '' }), text: errorText(activeCurrency) })
    }
  }[state.activeModal] as unknown as IModalProps;

  useEffect(() => {
    if (!currencySettings.current[state.settlementCurrency]) return;
    setState({ rateMarkup: currencySettings.current[state.settlementCurrency].rate });
  }, [state.settlementCurrency]);

  return {
    ...state,
    rateSample: conversionRateSample(state.settlementCurrency + activeCurrency, state.rateMarkup),
    setState,
    modalDetails,
    disableSettlementBtn,
    updateConversions,
    isFetching,
    toggleConversions
  };
}
