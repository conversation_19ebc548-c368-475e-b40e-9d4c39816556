import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';

import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import Conversions from '..';

const noConversionData = {
  data: {
    USD: {
      activated: false,
      enabled: false,
      markup: {
        kora: {
          value: 10.22
        },
        merchant: {
          limit: 10
        }
      }
    }
  }
};

const enabledConversionData = {
  data: {
    USD: {
      activated: true,
      enabled: true,
      markup: {
        kora: {
          value: 4
        },
        merchant: {
          value: 2,
          limit: 10
        }
      }
    }
  }
};

const MockedConversions = ({ currency = 'NGN' }: { currency?: string }) => {
  return (
    <MockIndex>
      <Conversions activeCurrency={currency} />
    </MockIndex>
  );
};

describe('Test for conversions', () => {
  describe('Basic functionality', () => {
    it('should render Conversions', async () => {
      render(<MockedConversions />);

      await waitFor(() => {
        expect(screen.getByTestId('conversions')).toBeInTheDocument();
      });
    });

    it('should be accessible', async () => {
      const { container } = render(<MockedConversions />);

      const result = await axe(container);
      expect(result).toHaveNoViolations();
    });

    it('The active Currency should be rendered in the title', async () => {
      render(<MockedConversions currency="USD" />);

      await waitFor(() => {
        expect(screen.getByText(/swap usd settlements/i)).toBeInTheDocument();
      });
    });

    it('Renders subtitle texts', async () => {
      render(<MockedConversions />);

      await waitFor(() => {
        expect(screen.getByTestId('subtitle')).toBeInTheDocument();
      });
    });

    it('On page render, Fill form field with conversions settings if conversion has been enabled for a currency', async () => {
      render(<MockedConversions />);

      await screen.findByText(/Swap NGN Settlements/i);

      await waitFor(async () => expect(await screen.findByTestId('conversions-checkbox')).toBeChecked());
      await waitFor(async () => expect(await screen.findByTestId('conversions-currency-select')).toHaveValue('USD'));
      await waitFor(async () => expect(await screen.findByTestId('rate-markup-input')).toHaveValue('10.22'));
    });

    it('checkbox should not be checked for a currency that is not activated for conversions', async () => {
      render(<MockedConversions currency="KES" />);

      await waitFor(() => {
        expect(screen.getByTestId('conversions-checkbox')).not.toBeChecked();
      });
    });
  });

  describe('Currency conversion enable success flow', () => {
    it('should render the conversions form ui when the checkbox is clicked for a currency that has no conversion', async () => {
      server.use(http.get('/settings/settlement/conversions', () => HttpResponse.json(noConversionData, { status: 200 })));
      render(<MockedConversions />);

      await waitFor(() => {
        expect(screen.queryByTestId('conversions-form')).not.toBeInTheDocument();
      });

      const checkbox = screen.getByTestId('conversions-checkbox');
      await userEvent.click(checkbox);

      await waitFor(() => {
        expect(screen.getByTestId('conversions-checkbox')).toBeChecked();
      });
      await waitFor(() => {
        expect(screen.getByTestId('conversions-form')).toBeInTheDocument();
      });
    });

    it('when the checkout box is clicked, the activate button should be rendered but it should be disabled', async () => {
      server.use(http.get('/settings/settlement/conversions', () => HttpResponse.json(noConversionData, { status: 200 })));
      render(<MockedConversions />);

      await waitFor(() => {
        expect(screen.queryByTestId('update-conversions-btn')).not.toBeInTheDocument();
      });

      const checkbox = screen.getByTestId('conversions-checkbox');
      await userEvent.click(checkbox);
      const btn = await screen.findByTestId('update-conversions-btn');
      await waitFor(() => {
        expect(btn).toBeInTheDocument();
      });
      await waitFor(() => {
        expect(btn).toBeDisabled();
      });
    });

    it('activate button should be enabled when any of the conversion form field changes', async () => {
      server.use(http.get('/settings/settlement/conversions', () => HttpResponse.json(noConversionData)));
      render(<MockedConversions />);

      await waitFor(() => {
        expect(screen.queryByTestId('update-conversions-btn')).not.toBeInTheDocument();
      });

      const checkbox = screen.getByTestId('conversions-checkbox');
      await userEvent.click(checkbox);

      await screen.findByTestId('conversions-currency-select');

      const select = screen.getByTestId('conversions-currency-select');
      await userEvent.selectOptions(select, 'USD');

      const limitInput = await screen.findByTestId('rate-markup-input');
      await userEvent.type(limitInput, '100');

      await waitFor(() => {
        const btn = screen.getByTestId('update-conversions-btn');
        expect(btn).not.toBeDisabled();
      });
    });

    it('render the confirmation modal for updating conversions when the activate-conversions-button is clicked', async () => {
      render(<MockedConversions />);

      const limitInput = await screen.findByTestId('rate-markup-input');
      await userEvent.type(limitInput, '10');

      const btn = screen.getByTestId('update-conversions-btn');
      await userEvent.click(btn);
      await waitFor(() => {
        const response = screen.getByText(/Refunds and chargebacks will be converted from the settlement/i);
        expect(response).toBeInTheDocument();
      });
    });

    it('render the success modal when conversions update is successful and close modal when dismiss is clicked', async () => {
      render(<MockedConversions />);

      const limitInput = await screen.findByTestId('rate-markup-input');
      await userEvent.type(limitInput, '100');

      const btn = await screen.findByTestId('update-conversions-btn');
      await userEvent.click(btn);

      const confirmBtn = await screen.findByTestId('second-button');
      await userEvent.click(confirmBtn);

      await waitFor(() => {
        expect(screen.getByTestId('conversions-update-feedback')).toBeInTheDocument();
      });
    });
  });

  describe('Currency conversion disable success flow', () => {
    it('On render, activation checkbox should be checked for a currency that is enabled for conversion', async () => {
      server.use(
        http.get('http://localhost:3000/api/settings/settlement/conversions', () =>
          HttpResponse.json(enabledConversionData, { status: 200 })
        )
      );
      render(<MockedConversions />);

      const checkbox = await screen.findByTestId('conversions-checkbox');
      await waitFor(() => expect(checkbox).toBeChecked());
    });

    it('for a currency that is enabled for conversions, Unchecking the conversions checkbox should render the disable conversions confirmation modal', async () => {
      server.use(
        http.get('http://localhost:3000/api/settings/settlement/conversions', () =>
          HttpResponse.json(enabledConversionData, { status: 200 })
        )
      );
      render(<MockedConversions />);

      const checkbox = await screen.findByTestId('conversions-checkbox');
      await userEvent.click(checkbox);

      await waitFor(
        () => {
          expect(screen.getByTestId('confirm-disable-text')).toBeInTheDocument();
        },
        { timeout: 5000 }
      );
    });

    it('A currency enabled for conversions should be disabled successfully', async () => {
      server.use(
        http.get('http://localhost:3000/api/settings/settlement/conversions', () =>
          HttpResponse.json(enabledConversionData, { status: 200 })
        ),
        http.patch('http://localhost:3000/api/settings/settlement/conversions', () =>
          HttpResponse.json({ data: { message: 'Conversion disabled successfully' } }, { status: 200 })
        )
      );
      render(<MockedConversions />);

      const checkbox = await screen.findByTestId('conversions-checkbox');
      await userEvent.click(checkbox);

      await waitFor(
        () => {
          expect(screen.getByTestId('confirm-disable-text')).toBeInTheDocument();
        },
        { timeout: 5000 }
      );

      const confirm = screen.getByTestId('second-button');
      await userEvent.click(confirm);

      await waitFor(
        () => {
          expect(screen.getByTestId('conversions-update-feedback')).toBeInTheDocument();
        },
        { timeout: 5000 }
      );
    });
  });
});
