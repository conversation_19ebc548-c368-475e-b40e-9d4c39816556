import { ReactNode, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

import HeaderTabs from '+containers/Dashboard/Shared/HeaderTabs';
import Icon from '+containers/Dashboard/Shared/Icons';
import Table from '+containers/Dashboard/Shared/Table';
import ToolTip from '+containers/Dashboard/Shared/Tooltip';
import { TabPanel, TabPanels, Tabs } from '+containers/Shared/Tabs';
import { useClickOutside, useSearchQuery } from '+hooks';
import { BalanceServices } from '+services/balance-services';
import { CardIssuanceServices } from '+services/card-issuance-services';
import useStore from '+store';
import {
  AllBalanceHistoryResponseType,
  AllFundingDepositsResponseType,
  BalanceSubtabKeyType,
  CardFeesType,
  CommonSearchQueriesType,
  CurrencyType,
  IssuingTabKeyType
} from '+types';
import { formatAmount } from '+utils';

import Filter from '../components/Filter/Filter';
import FundIssuingWalletModal from '../components/FundIssuingWalletModal';
import Summary from '../components/Summary';
import { balanceSubtabsTableProps, computeTableTitle, exportActionOptions, getBalanceSubtabsFilterProps } from '../constants';
import { useFetchIssuanceTables, useFetchIssuingExport } from '../hooks';
import { getIssuingPermissions } from '../utils';
import FundingDepositsRow from './components/FundingDepositsRow';
import IssuingHistoryRow from './components/IssuingHistoryRow';
import LimitThresholdConfigModal from './components/LimitThresholdConfigModal';

const subtabs: Array<{
  label: string;
  key: BalanceSubtabKeyType;
  renderElement: (arg: {
    rowData: AllBalanceHistoryResponseType[] | AllFundingDepositsResponseType[];
    currency: CurrencyType;
  }) => ReactNode;
}> = [
  {
    label: 'Issuing Balance History',
    key: 'issuing_balance_history',
    renderElement: ({ rowData, currency }) => <IssuingHistoryRow rowData={rowData as AllBalanceHistoryResponseType[]} currency={currency} />
  },
  {
    label: 'Funding Deposits',
    key: 'funding_deposits',
    renderElement: ({ rowData, currency }) => (
      <FundingDepositsRow rowData={rowData as AllFundingDepositsResponseType[]} currency={currency} />
    )
  }
];

const IssuingBalance = () => {
  const { value: searchQueryValue, setQuery } = useSearchQuery<
    {
      tab: IssuingTabKeyType;
      subtab: BalanceSubtabKeyType;
    } & CommonSearchQueriesType
  >();
  const limit = searchQueryValue?.limit ?? '10';
  const page = searchQueryValue?.page ?? '1';
  const { walletBalance, addCardFees } = useStore(useShallow(store => store));
  const subtab = searchQueryValue?.subtab ?? subtabs[0].key;
  const currency = searchQueryValue?.currency ?? 'USD';
  const exportAction = useFetchIssuingExport({ resourceType: `All-${subtab}`, exportFn: exportActionOptions[subtab], currency });
  const [recordIsFiltered, setRecordIsFiltered] = useState(false);
  const [showWalletFunding, setShowWalletFunding] = useState(false);
  const [showLimitConfig, setShowLimitConfig] = useState(false);
  const { canViewWalletHistory, canFundWallet, canConfigureLimitThreshold } = getIssuingPermissions();
  const [openMenu, setOpenMenu] = useState(false);
  const [isNotificationDismissed, setIsNotificationDismissed] = useState(false);

  const availableBalance = walletBalance?.[currency]?.available_balance ?? 0;
  const lowBalanceThreshold = walletBalance?.[currency]?.low_balance_threshold;
  const isLowBalance = lowBalanceThreshold == null ? false : availableBalance <= lowBalanceThreshold;
  const isNotificationVisible = isLowBalance && !isNotificationDismissed;

  const {
    tableData,
    isFetching: isFetchingTableData,
    refetch: refetchTableData
  } = useFetchIssuanceTables({
    currency,
    page,
    limit,
    tab: subtab,
    queryValue: searchQueryValue
  });

  const { data: balances } = BalanceServices.useGetBalances({
    enabled: canFundWallet,
    showErrorMessage: false,
    refetchOnCloseFeedbackError: true,
    errorMessage: 'There has been an error in getting your balance'
  });

  CardIssuanceServices.useFetchSubscriptionFees({
    onSuccess: (data: { data: CardFeesType }) => addCardFees(data.data),
    refetchOnCloseFeedbackError: true,
    errorMessage: 'There has been an error in getting issuing subscription fees'
  });

  const paging = tableData?.data?.paging;
  const tableProps = balanceSubtabsTableProps[subtab];
  const filterProps = getBalanceSubtabsFilterProps()[subtab];
  const popoverRef = useClickOutside<HTMLUListElement>(() => setOpenMenu(false));

  const handleRefetch = () => {
    void refetchTableData();
  };

  return (
    <div>
      <div className="issuing-heading">
        <div className="flex-grow-1 info-summary-container">
          <Summary
            label={`Issuing Balance (${currency})`}
            value={availableBalance ?? 0}
            valueFormatter={formatAmount}
            valueCustomStyles={`${isLowBalance && 'text-danger'}`}
            description="For card issuing and transactions"
          />
        </div>

        <div>
          <div className="page-action-btns">
            <button onClick={() => setShowWalletFunding(true)} className="btn btn-primary" type="button">
              <span className="os-icon os-icon-plus" />
              <span>Add Funds</span>
            </button>
            {canConfigureLimitThreshold && (
              <div className="position-relative">
                <ToolTip position="bottom" message="Set Low Limit Threshold">
                  <button onClick={() => setOpenMenu(true)} style={{ padding: '10px' }} className="btn btn-neutral">
                    <Icon name="gear" width={18} height={18} />
                  </button>
                </ToolTip>
                {openMenu && (
                  <ul ref={popoverRef} className="mb-0 mt-2 balance-actions-menu">
                    <li>
                      <button onClick={() => setShowLimitConfig(true)} className="btn balance-actions-menu__item">
                        Set Low Limit Threshold
                      </button>
                    </li>
                  </ul>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      {isNotificationVisible && (
        <div className="fade-in my-3 low-threshold-notif">
          <div className="low-threshold-notif__description">
            <Icon width={14} height={14} name="warningInfoRounded" className="mt-1 mr-3" />
            <span>
              You have reached your pre-set threshold limit. Top up your issuing balance to avoid any disruptions to your Card Issuance
              Services.
            </span>
          </div>
          <button onClick={() => setIsNotificationDismissed(true)} aria-label="Close notification" className="low-threshold-notif__close">
            <Icon name="x" width={16} height={16} />
          </button>
        </div>
      )}
      <Tabs defaultValue={subtab} onChange={subtabValue => setQuery({ tab: searchQueryValue.tab, subtab: subtabValue })}>
        <div className="os-tabs-controls os-tabs-complex settlement-history__tabs">
          <HeaderTabs
            tabs={subtabs.map(tab => tab.key)}
            activeTab={subtab}
            onClick={subtabValue => setQuery({ tab: searchQueryValue.tab, subtab: subtabValue as BalanceSubtabKeyType })}
            className="small-text"
          />
        </div>

        <TabPanels>
          {subtabs.map(({ key, renderElement }) => {
            return (
              <TabPanel key={`${key}-panel`} value={key} className="transaction_table_comp table-container">
                <Filter
                  totalItems={Number(paging?.total_items)}
                  title={computeTableTitle({
                    filtered: recordIsFiltered,
                    activeTab: subtab as string,
                    pageItemCount: Number(paging?.total_items)
                  })}
                  actions={filterProps.actions}
                  filterModalHeading={filterProps.filterModalHeading}
                  filterModalDescription={filterProps.filterModalDescription}
                  filterFields={filterProps.filterFields}
                  exportType={filterProps.exportType}
                  exportHeading={filterProps.exportHeading}
                  exportDescription={filterProps.exportHeading}
                  onChangeIsFiltered={setRecordIsFiltered}
                  isFiltered={recordIsFiltered}
                  exportAction={exportAction}
                />

                <Table
                  tableClassName={`--history-table ${tableProps.tableClassName}`}
                  headings={tableProps.headings}
                  hasPagination
                  borderedTable
                  loading={isFetchingTableData}
                  current={paging?.current}
                  limitAction={value => setQuery({ limit: String(value) })}
                  pageSize={paging?.page_size}
                  actionFn={value => setQuery({ page: String(value) })}
                  totalItems={paging?.total_items || 0}
                  emptyStateHeading={tableProps.emptyStateHeading}
                  emptyStateMessage={
                    <>
                      <span>{tableProps.emptyStateMessage}</span>

                      {canViewWalletHistory && (
                        <button type="button" className="refetch-button" onClick={handleRefetch}>
                          <i className="os-icon os-icon-rotate-ccw" style={{ marginRight: '5px' }} />
                          Refresh
                        </button>
                      )}
                    </>
                  }
                >
                  {renderElement({
                    rowData: tableData?.data?.data as AllBalanceHistoryResponseType[] | AllFundingDepositsResponseType[],
                    currency
                  })}
                </Table>
              </TabPanel>
            );
          })}
        </TabPanels>
      </Tabs>

      {showWalletFunding && (
        <FundIssuingWalletModal balances={balances?.data} currency={currency} onClose={() => setShowWalletFunding(false)} />
      )}

      {showLimitConfig && <LimitThresholdConfigModal currency={currency} onClose={() => setShowLimitConfig(false)} />}
    </div>
  );
};

export default IssuingBalance;
