import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';

import Icon from '+containers/Dashboard/Shared/Icons';
import Modal from '+containers/Dashboard/Shared/Modal';
import { feedbackInit } from '+hooks/feedbackHandler';
import { CardIssuanceServices } from '+services/card-issuance-services';
import useStore from '+store';
import { CurrencyType } from '+types';
import { backwardAmountInput, maskEmail, stripNonNumeric } from '+utils';

type LimitThresholdConfigModalProps = {
  onClose: () => void;
  currency: CurrencyType;
};

export default function LimitThresholdConfigModal(props: LimitThresholdConfigModalProps) {
  const { profile, walletBalance } = useStore();
  const currentLowBalanceThreshold = walletBalance?.[props.currency]?.low_balance_threshold;
  const queryClient = useQueryClient();
  const handleMutationError = (e: Error & { response?: { data: { message: string } } }) => {
    const message = e.response?.data.message || 'There has been an error';
    feedbackInit({
      message,
      type: 'danger',
      componentLevel: true
    });
  };

  const { mutateAsync: mutateUseLowBalanceLimit } = CardIssuanceServices.useSetLowBalanceLimit({
    onSuccess: () => {
      void queryClient.invalidateQueries({ predicate: query => query.queryKey.includes('/wallets/card-issuance/balances') });
      cleanUpForm();
    },
    onError: handleMutationError
  });

  const initiateCardFunding = async () => {
    await mutateUseLowBalanceLimit({
      currency: props.currency,
      threshold: Number(formik.values.amount)
    });
  };

  const formik = useFormik({
    initialValues: { amount: currentLowBalanceThreshold ? String(currentLowBalanceThreshold) : '' },
    onSubmit: async () => {
      await initiateCardFunding();
    },
    validate: values => {
      const errors: { amount?: string } = {};
      if (!values.amount) {
        errors.amount = 'Amount is required';
      } else if (isNaN(Number(values.amount))) {
        errors.amount = 'Amount must be a number';
      } else if (Number(values.amount) <= 0) {
        errors.amount = 'Amount must be greater than zero';
      } else if (currentLowBalanceThreshold != null && Number(values.amount) === Number(currentLowBalanceThreshold)) {
        errors.amount = 'Amount cannot be the same as current threshold';
      }
      return errors;
    }
  });

  const cleanUpForm = () => {
    formik.resetForm();
  };

  const getCompletedDescription = () => (
    <div>
      <p>You have successfully configured your issuing balance low threshold limit.</p>
      <p className="mt-3">We&apos;ll notify you at your registered email address whenever your balance hits the specified limit.</p>
    </div>
  );

  const modalContent = () => {
    return (
      <div>
        <div>
          <label htmlFor="amount" style={{ fontWeight: 500, fontSize: '14px' }}>
            Amount ({props.currency})
          </label>
          <input
            type="text"
            id="amount"
            name="amount"
            value={formik.values.amount ?? ''}
            onChange={e => {
              const v = backwardAmountInput(stripNonNumeric(e.target.value));
              void formik.setFieldValue('amount', v);
            }}
            onBlur={formik.handleBlur}
            className="form-control"
            placeholder="Enter amount"
          />
          {formik.errors.amount && formik.touched.amount && <p style={{ fontSize: '14px', color: 'red' }}>{formik.errors.amount}</p>}
        </div>
        {formik.values.amount && !formik.errors.amount && (
          <p className="fade-in d-flex p-3 rounded-lg align-items-start mb-0" style={{ backgroundColor: '#F1F6FA', marginTop: '1.5rem' }}>
            <Icon name="cautionRounded" className="mr-2" />
            <span style={{ fontSize: '13px', color: '#414F5F' }}>
              You will be notified at your registered email <strong>{maskEmail(profile?.email || '')}</strong> once the threshold is reached
            </span>
          </p>
        )}
      </div>
    );
  };

  return (
    <Modal
      close={props.onClose}
      secondButtonActionIsTerminal
      secondButtonAction={async () => {
        await formik.submitForm();
      }}
      secondButtonDisable={!formik.isValid || !formik.dirty}
      heading="Set low balance threshold limit"
      description="Enter the amount below to get notified when your issuing balance is low."
      content={modalContent()}
      completedDescription={getCompletedDescription()}
    />
  );
}
