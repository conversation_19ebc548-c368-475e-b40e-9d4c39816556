import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import IssuingBalance from '+containers/Dashboard/Issuing/Balance';
import MockIndex from '+mock/MockIndex';
import useStore from '+store';

describe('Issuing low-balance notification & configuration flow (integration)', () => {
  beforeEach(() => {
    useStore.setState({
      profile: { email: '<EMAIL>' },
      walletBalance: { USD: { available_balance: 500, low_balance_threshold: null } },
      permissions: { low_balance_threshold_configuration: 'manage' }
    });
  });

  it('shows no notification initially, allows configuring a threshold and then shows notification when threshold is higher than balance', async () => {
    const user = userEvent.setup();

    render(
      <MockIndex>
        <IssuingBalance />
      </MockIndex>
    );

    expect(screen.queryByText(/You have reached your pre-set threshold limit/i)).not.toBeInTheDocument();

    const allButtons = screen.getAllByRole('button');
    const gearBtn = allButtons.find(b => b.classList.contains('btn-neutral'));
    expect(gearBtn).toBeDefined();
    await user.click(gearBtn as Element);

    const menuItem = await screen.findByRole('button', { name: /Set Low Limit Threshold/i });
    await user.click(menuItem);

    const modal = await screen.findByRole('dialog', { hidden: true });
    const modalWithin = within(modal);
    expect(modal).toBeInTheDocument();

    expect(modalWithin.getByRole('heading', { name: /Set low balance threshold limit/i, hidden: true })).toBeInTheDocument();

    const amountInput = modalWithin.getByRole('textbox', { name: /Amount/i, hidden: true });
    await user.type(amountInput, '1000');

    expect(await modalWithin.findByText(/You will be notified at your registered email/i)).toBeInTheDocument();

    const continueBtn = modalWithin.getByRole('button', { name: /continue/i, hidden: true });
    await user.click(continueBtn);

    expect(await modalWithin.findByText(/You have successfully configured your issuing balance low threshold limit./i)).toBeInTheDocument();

    useStore.setState({ walletBalance: { USD: { available_balance: 500, low_balance_threshold: 1000 } } });

    expect(await screen.findByText(/You have reached your pre-set threshold limit/i)).toBeInTheDocument();

    const closeNotif = screen.getByLabelText(/Close notification/i);
    await user.click(closeNotif);

    await waitFor(() => {
      expect(screen.queryByText(/You have reached your pre-set threshold limit/i)).not.toBeInTheDocument();
    });
  });

  it('shows validation errors for invalid inputs in the modal (unhappy path)', async () => {
    const user = userEvent.setup();

    useStore.setState({
      profile: { email: '<EMAIL>' },
      walletBalance: { USD: { available_balance: 500, low_balance_threshold: 100 } },
      permissions: { low_balance_threshold_configuration: 'manage' }
    });

    render(
      <MockIndex>
        <IssuingBalance />
      </MockIndex>
    );

    const allButtons = screen.getAllByRole('button');
    const gearBtn = allButtons.find(b => b.classList.contains('btn-neutral'));
    expect(gearBtn).toBeDefined();
    await user.click(gearBtn as Element);

    const menuItem = await screen.findByRole('button', { name: /Set Low Limit Threshold/i });
    await user.click(menuItem);

    const modal = await screen.findByRole('dialog', { hidden: true });
    const modalWithin = within(modal);

    const amountInput = modalWithin.getByRole('textbox', { name: /Amount/i, hidden: true });

    await user.clear(amountInput);
    await user.tab();
    expect(await modalWithin.findByText(/Amount is required/i)).toBeInTheDocument();

    await user.clear(amountInput);
    await user.type(amountInput, 'abc');
    await user.tab();
    expect(await modalWithin.findByText(/Amount is required/i)).toBeInTheDocument();

    await user.clear(amountInput);
    await user.type(amountInput, '0');
    await user.tab();
    expect(await modalWithin.findByText(/Amount must be greater than zero/i)).toBeInTheDocument();
  });
});
