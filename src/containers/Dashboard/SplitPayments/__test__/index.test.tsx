import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import SplitPayment from '../index';

const MockedSplitPayment = ({ route = '/split-payments' }: { route?: string }) => {
  const baseRoute = '/dashboard/split-payments' + route;

  return (
    <MockIndexWithRoute route={baseRoute} initialEntries={[baseRoute]}>
      <SplitPayment />
    </MockIndexWithRoute>
  );
};

describe('SplitPaymentComponent', () => {
  test('SplitPayment is accessible', async () => {
    const { container } = render(<MockedSplitPayment />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Renders SplitPayment Component', () => {
    render(<MockedSplitPayment />);

    expect(screen.getByText('Manage Split Payments')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Take control of your pay-ins by directing funds to multiple settlement destinations. Simplify VAT payments, manage subsidiaries, and streamline revenue-sharing effortlessly.'
      )
    ).toBeInTheDocument();
    expect(screen.getByText(/Explore Split Payments By/i)).toBeInTheDocument();
  });

  test('Renders split accounts if url ends with /split-payments/accounts', async () => {
    render(<MockedSplitPayment route="/accounts" />);
    expect(await screen.findByText('Split Accounts (3)')).toBeInTheDocument();
  });

  test('Renders split accounts summary if url ends with /split-payments/accounts/:id', async () => {
    render(<MockedSplitPayment route="/accounts/test" />);

    expect(await screen.findByText('Account Summary')).toBeInTheDocument();

    expect(screen.getByText('Total Settlement')).toBeInTheDocument();
    expect(screen.getByText(/0.00 NGN/i)).toBeInTheDocument();

    expect(screen.getByText('Account Name')).toBeInTheDocument();
    expect(screen.getByText(/test1/i)).toBeInTheDocument();

    expect(screen.getByText('Account Number')).toBeInTheDocument();
    expect(screen.getByText('Bank')).toBeInTheDocument();
    expect(screen.getByText('Date Added')).toBeInTheDocument();
    expect(screen.getByText('Last Settlement Date')).toBeInTheDocument();
  });

  test('Renders split payouts if url ends with /split-payments/payouts', async () => {
    render(<MockedSplitPayment route="/payouts" />);
    expect(await screen.findByText('Payouts (2)')).toBeInTheDocument();
  });

  test('Renders split payouts in the table', async () => {
    render(<MockedSplitPayment />);

    expect(await screen.findByText('Payouts (2)')).toBeInTheDocument();

    expect(screen.getByText('Payout ID')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getAllByText(/Recipient Name/i)).toHaveLength(3);
    expect(screen.getAllByText(/Date and Time/i)).toHaveLength(3);
    expect(screen.getAllByText(/Amount/i)).toHaveLength(3);
  });
});
