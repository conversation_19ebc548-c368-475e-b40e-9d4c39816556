import { useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import { useSearchQuery } from '+hooks';
import { closeFeedback, feedbackInit } from '+hooks/feedbackHandler';
import { SplitPaymentServices } from '+services/split-payment-services';
import useStore from '+store';
import { APIDownload, filteredOutObjectProperty, getDate, queriesParams } from '+utils';

import { routesInfoArray, SplitPaymentsExportParamStateType } from './splitPaymentsHelpers';

export default function useSplitPayments() {
  const { profile } = useStore();
  const { id } = useParams<{ id: string }>();

  const activeRoute = routesInfoArray.find(info => info.route === useLocation().pathname.split('/').slice(-1)[0]) || routesInfoArray[0];
  const activeTab = activeRoute?.label;

  const searchQuery = useSearchQuery<{
    [key in 'page' | 'limit' | 'status' | 'accountReference' | 'currency' | 'accountNumber']: string;
  }>();
  const currency = searchQuery.value.currency ?? 'NGN';

  const accountNumber = searchQuery.value.accountNumber ?? '';
  const paginationPage = searchQuery.value.page ?? '1';
  const status = searchQuery.value.status ?? '';

  const [openExportModal, setOpenExportModal] = useState(false);
  const [exportParams, setExportParams] = useState<SplitPaymentsExportParamStateType | null>(null);
  const [openLargeExportModal, setOpenLargeExportModal] = useState(false);

  const sortingPaymentsParams = {
    ...filteredOutObjectProperty(searchQuery.value, [queriesParams.accountReference])
  };

  const { data: settings } = SplitPaymentServices.useGetSplitSettings({ params: { currency } });

  const { data: splitAccountsSummary, isFetching: fetchingSplitAccountsSummary } = SplitPaymentServices.useGetAllSplitAccountsSummary({
    params: {
      currency,
      ...filteredOutObjectProperty(searchQuery.value, [queriesParams.status, queriesParams.accountReference])
    },
    enabled: activeTab === 'Accounts'
  });

  const { data: singleSplitAccountSummary, isFetching: fetchingSingleSplitAccountSummary } =
    SplitPaymentServices.useGetSingleSplitAccountSummary({ account: id, enabled: !!id });

  const idAccountNumber = (splitAccountsSummary?.data?.data?.find(account => account.reference === id) ?? singleSplitAccountSummary?.data)
    ?.account_number;

  const { data: splitPayments, isFetching: fetchingSplitPayments } = SplitPaymentServices.useGetSplitPayments({
    params: {
      ...sortingPaymentsParams,
      ...((accountNumber || idAccountNumber) && { accountNumber: idAccountNumber ?? accountNumber })
    },
    enabled: id ? !!idAccountNumber : activeTab === 'Payouts'
  });

  const isFetching = fetchingSplitAccountsSummary || fetchingSplitPayments || fetchingSingleSplitAccountSummary;

  const tableData = (activeTab === 'Accounts' ? splitAccountsSummary : splitPayments)?.data;

  const exportFile = (format: string, fieldsToExport: string | string[]) => {
    const fields =
      typeof fieldsToExport === 'string'
        ? `&fieldsToExport[]=${fieldsToExport}`
        : fieldsToExport.map(encodeURIComponent).join(`&fieldsToExport[]=`);

    setExportParams({ format, fields, status, ...sortingPaymentsParams, currency, fieldsToExport: fields });
  };

  SplitPaymentServices.useGetSplitPayments({
    isExport: !!exportParams,
    enabled: !!exportParams,
    params: exportParams as SplitPaymentsExportParamStateType,
    errorMessage: 'There has been an error exporting your split payouts',
    onSuccess: res => {
      if ((res as unknown as { status: number })?.status === 202) {
        setOpenLargeExportModal(true);
      } else {
        const type = exportParams?.format === 'csv' ? 'csv' : 'xlsx';
        APIDownload(res, `Split payout at ${getDate(Date.now())}`, type);
        feedbackInit({
          message: `Split payout successfully downloaded`,
          type: 'success'
        });

        setTimeout(() => closeFeedback(), 3000);
      }
      setOpenExportModal(false);
      setExportParams(null);
    },

    onError: () => {
      setOpenExportModal(false);
      setExportParams(null);
    }
  });

  return {
    profile,
    paginationPage,
    openExportModal,
    openLargeExportModal,
    settings,
    isFetching,
    tableData,
    exportFile,
    activeTab,
    id,
    fetchingSingleSplitAccountSummary,
    singleSplitAccountSummary,
    currency,
    searchQuery,
    setOpenExportModal,
    setOpenLargeExportModal,
    activeRoute,
    splitAccountsSummary,
    splitPayments
  };
}
