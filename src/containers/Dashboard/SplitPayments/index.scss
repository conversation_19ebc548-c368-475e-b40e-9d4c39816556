@import 'styles/base/variables';

.split-payment {
  .goback-btn {
    margin: 0 0 2rem;
    padding: 0;
  }
  .__heading {
    margin-bottom: 3rem;

    h4 {
      font-size: 1.3rem;
      color: #414f5f;
      font-weight: 500;
    }

    p {
      color: #94a7b7;
      font-weight: 400;
      margin-top: 0.75rem;
      letter-spacing: -0.025em;
      max-width: 500px;
    }
  }
  .content-details-head {
    margin: 0;
    padding: 0 0 1.5rem;
    border-bottom: 0;
    align-items: center;

    .content-details-title {
      align-items: center;
      gap: 1rem;

      img {
        width: 4rem;
        aspect-ratio: 1;
      }
    }

    hr {
      background-color: #dde2ec;
    }

    .cd-action {
      color: '#2376F3';
      font-weight: 600;
      margin: -0.75rem 0 0;
    }
  }
  .__nav {
    .currency-tabs {
      margin: 1.5rem 0;
    }

    .nav-tabs {
      position: relative;

      .btn-export {
        background: none;
        border: none;
        color: #2376f3;
        font-weight: 600;
        padding: 0;
        position: absolute;
        right: 0;
        top: 30%;

        &:hover {
          opacity: 0.7;
        }
      }
    }
  }

  &-text-blue {
    color: #007bff;
    font-weight: 500;
    text-transform: capitalize;
  }
}

.split-payment-table {
  .empty-split-payouts-placeholder {
    .btn {
      border-radius: 8px;
      font-weight: 500;
      margin: 1rem 0 0;
      padding: 0.5rem 1rem;
    }
  }
}

@media (min-width: 1420px) {
  .split-payments-status-field {
    max-width: 15% !important;
    min-width: 0 !important;
  }

  .split-payments-search-account-input {
    max-width: 15% !important;
    min-width: 0 !important;
  }
}
