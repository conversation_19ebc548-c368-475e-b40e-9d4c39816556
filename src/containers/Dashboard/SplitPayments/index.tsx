import { Redirect, Route, Switch } from 'react-router-dom';

import AdvanceExportModal from '+containers/Dashboard/Shared/AdvanceExportModal';
import ExportFilterModal from '+containers/Dashboard/Shared/ExportFilterModal';
import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceholder';
import SecondaryDetails from '+containers/Dashboard/Shared/SecondaryDetails';
import TabSwitch from '+containers/Dashboard/Shared/TabSwitch';
import { history } from '+utils';

import Table from '../Shared/Table';
import CreateSplitPaymentAccount from './components/CreateSplitPaymentAccount';
import SplitPaymentIntro from './components/SplitPaymentIntro';
import SplitPaymentsFilter from './components/SplitPaymentsFilters';
import SplitPaymentsRules from './components/SplitPaymentsRules';
import SplitPaymentsSettlementDetails from './SplitPaymentsDetails';
import {
  getSingleAccountSummaryFn,
  getSplitPaymentTableHeaders,
  routesInfoArray,
  splitAccountsTableContent,
  splitPaymentsTableContent
} from './splitPaymentsHelpers';
import useSplitPayments from './useSplitPayments';

import BusinessAvatar from '+assets/img/dashboard/customer-avatar.svg';

import './index.scss';

const SplitPaymentsComponent = () => {
  const {
    profile,
    paginationPage,
    openExportModal,
    openLargeExportModal,
    settings,
    isFetching,
    tableData,
    exportFile,
    activeTab,
    id,
    fetchingSingleSplitAccountSummary,
    singleSplitAccountSummary,
    currency,
    searchQuery,
    setOpenExportModal,
    setOpenLargeExportModal,
    activeRoute,
    splitAccountsSummary,
    splitPayments
  } = useSplitPayments();

  return (
    <>
      <section className="split-payment" data-testid="split-payment">
        {id && (
          <button type="button" className="btn btn-link goback-btn" onClick={() => history.goBack()}>
            <i className="os-icon os-icon-arrow-left7" />
            <span>Go Back</span>
          </button>
        )}
        {id ? (
          <div className="content-details-box">
            <div className="content-details-head">
              <div className="content-details-title">
                <img alt="business-icon" src={BusinessAvatar} />

                <h5 className="cd-maintitle">{id}</h5>
              </div>
            </div>
            <div className="detail-box">
              {fetchingSingleSplitAccountSummary ? (
                <LoadingPlaceholder type="text" content={4} />
              ) : (
                <>
                  <SecondaryDetails
                    useShowMore={false}
                    title="Account Summary"
                    data={getSingleAccountSummaryFn(singleSplitAccountSummary?.data, currency)}
                  />
                  <hr />
                </>
              )}
            </div>
          </div>
        ) : (
          <>
            <div className="__heading">
              <h4>Manage Split Payments</h4>

              <p>
                Take control of your pay-ins by directing funds to multiple settlement destinations. Simplify VAT payments, manage
                subsidiaries, and streamline revenue-sharing effortlessly. <SplitPaymentIntro enabled={settings?.data?.enabled} />
              </p>
            </div>
            <div className="__nav">
              <div>
                <strong>Explore Split Payments By:</strong>
                <TabSwitch
                  options={routesInfoArray.map(route => route.label)}
                  activeTab={activeTab}
                  setTab={value => {
                    const tab = routesInfoArray.find(info => info.label === value)!;
                    history.push(`/dashboard/split-payments/${tab.route}`);
                  }}
                  className="ml-3"
                />
              </div>

              <div className="content-i currency-tabs">
                <section className="os-tabs-w">
                  <div className="os-tabs-controls os-tabs-complex">
                    <ul className="nav nav-tabs split-payment-currency-tab">
                      {['NGN']?.map(arg => (
                        <li className="nav-item" key={arg}>
                          <button
                            type="button"
                            className={`nav-link ${arg === currency ? 'active' : ''}`}
                            onClick={() => {
                              searchQuery.setQuery({ currency: arg }, true);
                            }}
                          >
                            {arg}
                          </button>
                        </li>
                      ))}
                      {activeTab === 'Payouts' && (
                        <li>
                          <button type="button" className="btn btn-export" onClick={() => setOpenExportModal(true)}>
                            <i className="os-icon os-icon-arrow-up-right" />
                            <span>Export Transactions</span>
                          </button>
                        </li>
                      )}
                    </ul>
                  </div>
                </section>
              </div>
            </div>
          </>
        )}
      </section>

      {activeTab === 'Split Rules' && <SplitPaymentsRules settings={settings?.data} />}
      {activeTab === 'Accounts' && <CreateSplitPaymentAccount settings={settings?.data} />}

      {['Accounts', 'Payouts'].includes(activeTab) && (
        <>
          <SplitPaymentsFilter activeTab={activeTab} totalCount={tableData?.paging?.total_items} />
          {openExportModal && (
            <AdvanceExportModal
              openExport={openExportModal}
              setOpenExport={setOpenExportModal}
              exportAction={exportFile}
              type="split-payments"
              showSuccessModal={false}
            />
          )}
          <ExportFilterModal close={() => setOpenLargeExportModal(false)} email={profile.email} visible={openLargeExportModal} />
          <section className="element-box-tp mt-3">
            <div className="split-payment-table nav-content active">
              <Table
                tableClassName={activeRoute?.tableClassName}
                headings={getSplitPaymentTableHeaders(activeTab, id)}
                hasPagination={!isFetching}
                loading={isFetching}
                current={parseInt(paginationPage, 10)}
                totalItems={tableData?.paging?.total_items || 0}
                pageSize={tableData?.paging?.page_size || 0}
                actionFn={current => searchQuery.setQuery({ page: String(current) })}
                limitAction={limit => searchQuery.setQuery({ limit: String(limit) })}
                annotation={activeRoute?.label}
                emptyStateHeading={
                  activeRoute.route === 'payouts'
                    ? 'No Payout transactions yet'
                    : `No ${id ? 'payouts to this account' : activeRoute?.label} yet.`
                }
                emptyStateMessage={
                  activeRoute.route === 'payouts' ? (
                    <div className="empty-split-payouts-placeholder">
                      <p>To see your payouts, set up an active split rule and begin processing transactions.</p>
                      <button
                        className="btn btn-primary"
                        onClick={() => {
                          history.push(`/dashboard/split-payments/rules`);
                        }}
                        data-testid="create-split-rule-btn"
                      >
                        Add Split Rule
                      </button>
                    </div>
                  ) : (
                    <span role="button" tabIndex={0}>
                      {activeRoute?.label} will appear here when you have {activeRoute?.label.toLowerCase()}.
                    </span>
                  )
                }
              >
                {activeTab === 'Accounts'
                  ? splitAccountsTableContent(splitAccountsSummary?.data?.data, activeTab, currency)
                  : splitPaymentsTableContent(splitPayments?.data?.data, activeTab, currency)}
              </Table>
            </div>
          </section>
        </>
      )}
    </>
  );
};

export default function SplitPayments() {
  return (
    <div>
      <Switch>
        <Route exact path="/dashboard/split-payments">
          <Redirect to="/dashboard/split-payments/payouts" />
        </Route>
        <Route path="/dashboard/split-payments/payouts/:id">
          <SplitPaymentsSettlementDetails />
        </Route>
        <Route
          path={[
            '/dashboard/split-payments/rules',
            '/dashboard/split-payments/accounts/:id?',
            '/dashboard/split-payments/payouts',
            '/dashboard/split-payments'
          ]}
        >
          <SplitPaymentsComponent />
        </Route>
      </Switch>
    </div>
  );
}
