import TransactionDetails from '+containers/Dashboard/TransactionDetailsNew';
import { formatAmount } from '+utils';

import RelatedSplitPaymentsTransactions from './RelatedSplitPaymentsTransactions';
import {
  splitPaymentsDetailsHeaderContent,
  splitPaymentsDetailsMoreTrxDetailsContent,
  splitPaymentsDetailsRecipientInformationContent
} from './splitPaymentsDetailsHelpers';
import useSplitPaymentsDetails from './useSplitPaymentsDetails';

const SplitPaymentsDetails = () => {
  const { trxnStatusObj, isLoading, data } = useSplitPaymentsDetails();

  return (
    <TransactionDetails loaderCount={4} isLoading={isLoading || !data}>
      <TransactionDetails.Header
        heading={formatAmount(data?.amount_charged)}
        currency={data?.settlement.currency}
        statusLabels={[{ status: trxnStatusObj.name, statusBg: trxnStatusObj.backgroundColor, statusColor: trxnStatusObj.color }]}
        summaries={splitPaymentsDetailsHeaderContent(data)}
      />
      <TransactionDetails.Section
        heading="More Transaction Details"
        dataTestId="split-payment-settlement-more-transaction-details"
        summaries={splitPaymentsDetailsMoreTrxDetailsContent(data)}
      />
      <TransactionDetails.Section
        heading="Recipient Information"
        dataTestId="recipient-information-section"
        summaries={splitPaymentsDetailsRecipientInformationContent(data)}
      />

      <RelatedSplitPaymentsTransactions
        currency={data?.settlement.currency}
        settlementReference={data?.settlement.reference}
        settlementPayoutReference={data?.reference}
      />
    </TransactionDetails>
  );
};

export default SplitPaymentsDetails;
