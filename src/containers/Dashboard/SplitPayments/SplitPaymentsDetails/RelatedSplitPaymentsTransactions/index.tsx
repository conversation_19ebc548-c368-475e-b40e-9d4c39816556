import { Link } from 'react-router-dom';

import TransactionDetails from '+containers/Dashboard/TransactionDetailsNew';
import { SplitPaymentServices } from '+services/split-payment-services';
import { capitalizeRemovedash, formatAmount, getDate, getTime, switchStatus } from '+utils';

import './index.scss';

export const RelatedSplitPaymentsTransactions = ({
  settlementReference,
  settlementPayoutReference,
  currency
}: {
  settlementPayoutReference: string;
  settlementReference: string;
  currency: string;
}) => {
  const { data, isLoading } = SplitPaymentServices.useGetSplitPayments({
    params: { currency, settlementReference, settlementPayoutReference, limit: '3' },
    enabled: !!(settlementPayoutReference && settlementReference)
  });

  const isMultiple = data?.data.data.length > 1;

  return data || !isLoading ? (
    <TransactionDetails.Section
      heading={
        <div aria-label="related transactions" className="related-split-payments-trxns-heading">
          Related Settlement{isMultiple && 's'}
          <Link to={`/dashboard/split-payments/payouts?settlementReference=${settlementReference}`} className="section-heading-link">
            See {isMultiple && 'All'} Related Settlement{isMultiple && 's'} <i className="os-icon os-icon-arrow-up-right" />
          </Link>
        </div>
      }
      dataTestId="related-settlemnts-section"
    >
      <ul className="related-split-payments-trxns__list">
        {data?.data.data.map(trx => (
          <li key={trx.settlement_payout_reference} className="related-split-payments-trxns__list-item">
            <div>
              {getDate(trx.transaction_date)} {getTime(trx.transaction_date)}
            </div>
            <div>
              <span>{trx.settlement_payout_reference}</span>
              <span>{settlementReference}</span>
            </div>
            <div>
              <span>
                <i className={`status-pill smaller ${switchStatus(trx.status)}`} />
                {capitalizeRemovedash(trx.status)}
              </span>
            </div>
            <div>
              {formatAmount(trx.settlement_payout_amount)} {currency}
            </div>
          </li>
        ))}
      </ul>
    </TransactionDetails.Section>
  ) : null;
};

export default RelatedSplitPaymentsTransactions;
