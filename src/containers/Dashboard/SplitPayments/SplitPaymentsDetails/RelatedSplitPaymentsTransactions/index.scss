.related-split-payments-trxns {
  font-size: 0.9rem;

  &-heading {
    display: flex;
    justify-content: space-between;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    &-item {
      border: 1px solid #1026493d !important;
      border-radius: 5px;
      display: flex;
      flex-direction: column;
      font-weight: 500;
      gap: 1rem;
      padding: 1rem !important;

      div:nth-of-type(1) {
        color: #a9afbc;
      }

      div:nth-of-type(2) {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        flex-shrink: 0;
        gap: 0.5rem;

        span:nth-of-type(1) {
          font-size: 0.8rem;
          text-transform: uppercase;
        }
        span:nth-of-type(2) {
          color: #a9afbc;
          text-transform: capitalize;
        }
      }

      div:nth-of-type(3) {
        align-items: center;
        display: flex;
        gap: 0.5rem;
      }
    }
  }
}

@media (min-width: 1280px) {
  .related-split-payments-trxns {
    .more-trxn-heading {
      display: flex;
      justify-content: space-between;
      margin: 1.875rem 0 1.25rem;
    }

    &__list {
      &-item {
        align-items: center;
        border: 0 !important;
        border-radius: 0;
        flex-direction: row;
        justify-content: space-between;
        gap: 1rem;
        padding: 0 !important;

        div:nth-of-type(1) {
          flex-basis: 14%;
        }

        div:nth-of-type(2) {
          flex-basis: 48%;
        }

        div:nth-of-type(3) {
          align-items: center;
          display: flex;
          gap: 0.5rem;
          flex-basis: 20%;

          span:nth-of-type(1) {
            display: flex;
            align-items: center;
          }
        }

        div:nth-of-type(4) {
          flex-basis: 10%;
          text-align: right;
        }
      }
      &-conversions {
        div:nth-of-type(4) {
          color: #a9afbc;
          flex-basis: 18%;
          text-align: right;
        }
      }
    }
  }
}

@media (min-width: 1560px) {
  .related-split-payments-trxns {
    &__list {
      &-item {
        gap: 2rem;

        div:nth-of-type(2) {
          gap: 0.5rem;

          span:nth-of-type(1) {
            font-size: 1rem;
          }
        }

        div:nth-of-type(3) {
          gap: 1.5rem;
        }

        div:nth-of-type(4) {
          font-size: 1rem;
        }
      }
    }
  }
}
