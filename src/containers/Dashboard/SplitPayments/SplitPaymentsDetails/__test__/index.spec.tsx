import { render, screen, within } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import SplitPaymentsSettlementDetails from '../index';

const MockSplitPaymentsSettlementDetails = () => {
  return (
    <MockIndexWithRoute route="/:id" initialEntries={['/test']}>
      <SplitPaymentsSettlementDetails />
    </MockIndexWithRoute>
  );
};

describe('SplitPaymentsSettlementDetails Test', () => {
  test('SplitPaymentsSettlementDetails is accessible', async () => {
    const { container } = render(<MockSplitPaymentsSettlementDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  describe('Test for SplitPaymentsSettlementDetails Heading Summary', () => {
    test('Renders SplitPaymentsSettlementDetails Heading Summary', async () => {
      render(<MockSplitPaymentsSettlementDetails />);

      expect(await screen.findByTestId('transaction-details-header')).toBeInTheDocument();

      const heading = within(screen.getByTestId('transaction-details-header'));

      expect(await heading.findByText(/2,000.00/i)).toBeInTheDocument();
      expect(heading.getAllByText(/NGN/i)).toHaveLength(3);
      expect(heading.getByText(/transaction in progress.../i)).toBeInTheDocument();

      expect(heading.getAllByText(/net amount/i)).toHaveLength(1);
      expect(heading.getByText(/100.00 NGN/i)).toBeInTheDocument();

      expect(heading.getAllByText(/fee/i)).toHaveLength(1);
      expect(heading.getByText(/22.00 NGN/i)).toBeInTheDocument();

      expect(heading.getByText(/date completed/i)).toBeInTheDocument();
      expect(heading.getByText(/5 May 2025, 10:00 AM/i)).toBeInTheDocument();

      expect(heading.getByText(/customer name/i)).toBeInTheDocument();
      expect(heading.getByText(/test1/i)).toBeInTheDocument();

      expect(heading.getByText(/transaction id/i)).toBeInTheDocument();
      expect(await heading.findByText(/ref-1/i)).toBeInTheDocument();
    });
  });

  describe('Test for More Transaction Details Section in SplitPaymentsSettlementDetails', () => {
    test('Renders More Transaction Details Section in SplitPaymentsSettlementDetails', async () => {
      render(<MockSplitPaymentsSettlementDetails />);
      expect(await screen.findByTestId('split-payment-settlement-more-transaction-details')).toBeInTheDocument();
    });

    test('Renders More Transaction Details Section in SplitPaymentsSettlementDetails', async () => {
      render(<MockSplitPaymentsSettlementDetails />);
      const moreDetails = within(await screen.findByTestId('split-payment-settlement-more-transaction-details'));

      expect(moreDetails.getAllByText(/more transaction details/i)).toHaveLength(2);

      expect(moreDetails.getByText(/status/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/processing/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/trace id/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/Not available/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/amount charged/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/2,000.00 NGN/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/channel/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/card/i)).toBeInTheDocument();

      expect(moreDetails.getByText('Currency')).toBeInTheDocument();
      expect(moreDetails.getByText('Naira (NGN)')).toBeInTheDocument();

      expect(moreDetails.getByText(/date created/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/5 may 2025, 9:00 AM/i)).toBeInTheDocument();

      expect(moreDetails.getByText(/date completed/i)).toBeInTheDocument();
      expect(moreDetails.getByText(/5 may 2025, 10:00 AM/i)).toBeInTheDocument();
    });
  });

  describe('Test for Recipient Information Section in SplitPaymentsSettlementDetails', () => {
    test('Renders Recipient Information Section in SplitPaymentsSettlementDetails', async () => {
      render(<MockSplitPaymentsSettlementDetails />);
      expect(await screen.findByTestId('recipient-information-section')).toBeInTheDocument();
    });

    test('Renders Recipient Information Section in SplitPaymentsSettlementDetails', async () => {
      render(<MockSplitPaymentsSettlementDetails />);
      const info = within(await screen.findByTestId('recipient-information-section'));

      expect(info.getByText(/payment method/i)).toBeInTheDocument();
      expect(info.getByText(/card/i)).toBeInTheDocument();

      expect(info.getByText('Bank')).toBeInTheDocument();
      expect(info.getByText(/Access Bank/i)).toBeInTheDocument();

      expect(info.getByText(/account number/i)).toBeInTheDocument();
      expect(info.getByText(/**********/i)).toBeInTheDocument();

      expect(info.getByText(/account name/i)).toBeInTheDocument();
      expect(info.getByText(/test1/i)).toBeInTheDocument();
    });
  });

  describe('Test for Related Settlements Section in SplitPaymentsSettlementDetails', () => {
    test('Renders Related Settlements in SplitPaymentsSettlementDetails', async () => {
      render(<MockSplitPaymentsSettlementDetails />);
      expect(await screen.findByTestId('related-settlemnts-section')).toBeInTheDocument();
    });

    test('Renders Related Settlement Data in SplitPaymentsSettlementDetails', async () => {
      render(<MockSplitPaymentsSettlementDetails />);
      const relatedSettlementSection = within(await screen.findByTestId('related-settlemnts-section'));

      expect(relatedSettlementSection.getByText(/5 May 2025 9:00 AM/i)).toBeInTheDocument();
      expect(relatedSettlementSection.getByText(/ref1/i)).toBeInTheDocument();

      expect(relatedSettlementSection.getByText(/6 Jun 2025 8:00 AM/i)).toBeInTheDocument();
      expect(relatedSettlementSection.getByText(/ref2/i)).toBeInTheDocument();
      expect(relatedSettlementSection.getAllByText(/settlement-ref-1/i)).toHaveLength(2);
    });
  });
});
