import Copyable from '+dashboard/Shared/Copyable';
import ToolTip from '+dashboard/Shared/Tooltip';
import { Channel, CurrencyType, SingleSplitPaymentResponseType, StatusType } from '+types';
import {
  capitalize,
  capitalizeRemovedash,
  formatAmount,
  getDate,
  getTime,
  paymentMethodsMap,
  switchBank,
  switchChannel,
  switchCurrency,
  switchStatus
} from '+utils';

import infoIcon from '+assets/img/dashboard/information-button.svg';

export const splitPaymentsDetailsHeaderContent = (trx?: SingleSplitPaymentResponseType) => {
  return [
    {
      label: (
        <>
          Net Amount
          <ToolTip
            type="net_amount"
            image={infoIcon}
            message={
              <em>
                Net Amount <br /> This is the amount less fee
              </em>
            }
          />
        </>
      ),
      value: `${formatAmount(trx?.amount)} ${trx?.settlement.currency}`
    },

    {
      label: (
        <>
          Fee
          <ToolTip type="fee" image={infoIcon} message={<em>Total charges incurred while processing this transaction</em>} />
        </>
      ),
      value: [!!trx?.fee, !!trx?.vat, !!trx?.settlement.currency].includes(false)
        ? 'N/A'
        : `${formatAmount(parseFloat(trx?.fee ?? '') + parseFloat(trx?.vat ?? ''))} ${trx?.settlement.currency}`
    },
    { label: 'Date Completed', value: trx?.completed_at ? `${getDate(trx.completed_at)}, ${getTime(trx.completed_at)}` : 'Not Available' },
    {
      label: 'Customer Name',
      value: capitalize(trx?.recipient_information.account_name ?? 'Not Available')
    },
    {
      label: 'Transaction ID',
      value: <Copyable text={trx?.reference ?? ''} textModifier={text => text?.toUpperCase()} />
    }
  ];
};

export const splitPaymentsDetailsMoreTrxDetailsContent = (trx?: SingleSplitPaymentResponseType) => [
  {
    label: 'Status',
    value: (
      <>
        <span
          className={`status-pill smaller more-details-opt ${
            (trx?.status as StatusType) === 'requires_auth' ? 'yellow' : switchStatus(trx?.status ?? '')
          }`}
        />
        <>{capitalizeRemovedash(trx?.status ?? 'Noy Available')}</>
      </>
    )
  },

  ...(trx?.status === 'failed' ? [{ label: 'Reason for Failure', value: trx?.description ?? 'Not Available' }] : []),

  { label: 'Trace ID', value: trx?.trace_id ?? 'Not Available' },

  { label: 'Currency', value: switchCurrency[trx?.settlement.currency as CurrencyType] },

  { label: 'Amount Charged', value: `${formatAmount(trx?.amount_charged ?? 0)} ${trx?.settlement.currency}` },

  { label: 'Channel', value: switchChannel(trx?.settlement.channel as Channel) },

  { label: 'Date Created', value: trx?.created_at ? `${getDate(trx.created_at)}, ${getTime(trx.created_at)}` : 'Not Available' },

  { label: 'Date Completed', value: trx?.completed_at ? `${getDate(trx.completed_at)}, ${getTime(trx.completed_at)}` : 'Not Available' }
];

export const splitPaymentsDetailsRecipientInformationContent = (trx: SingleSplitPaymentResponseType) => [
  { label: 'Payment Method', value: paymentMethodsMap[trx?.recipient_information.payment_method] ?? 'Not Available' },

  {
    label: 'Bank',
    value: (
      <>
        <img
          id="bank-icon"
          src={switchBank(trx?.recipient_information?.bank_name)}
          alt="bank icon"
          style={{ marginLeft: trx?.recipient_information?.bank_name ? 0 : '' }}
        />
        <span style={{ display: 'inline', marginLeft: 8, color: '#414f5f' }}>
          {capitalize(trx?.recipient_information?.bank_name ?? 'Not Available')}
        </span>
      </>
    )
  },

  ...(trx?.recipient_information?.account_number
    ? [
        {
          label: 'Account Number',
          value: trx?.recipient_information.account_number
        }
      ]
    : []),

  {
    label: 'Account Name',
    value: capitalize(trx?.recipient_information.account_name || 'Not Available')
  }
];
