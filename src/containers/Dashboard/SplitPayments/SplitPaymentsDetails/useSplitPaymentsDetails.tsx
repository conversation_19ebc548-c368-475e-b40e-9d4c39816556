import { useParams } from 'react-router-dom';

import { SplitPaymentServices } from '+services/split-payment-services';
import { history, switchTrxnMessage } from '+utils';

const useSplitPaymentsDetails = () => {
  const { id } = useParams<{ id: string }>();

  const { data, isLoading } = SplitPaymentServices.useGetSingleSplitPayment({
    onError: () => history.goBack(),
    id
  });

  const trxnStatusObj = switchTrxnMessage[data?.data?.status ?? ('processing' as keyof typeof switchTrxnMessage)];

  return { trxnStatusObj, isLoading, data: data?.data };
};

export default useSplitPaymentsDetails;
