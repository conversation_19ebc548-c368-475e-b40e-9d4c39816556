import { ReactElement } from 'react';

import Copyable from '+containers/Dashboard/Shared/Copyable';
import { SingleSplitAccountSummary, SplitAccountsSummaryType, SplitPaymentsType } from '+types';
import { capitalize, capitalizeRemovedash, formatAmount, getDate, getTime, history, logBreadCrumb, switchStatus } from '+utils';
import { breadCrumbEvents } from '+utils/bugsnag-events';

export type SplitPaymentsExportParamStateType = {
  format: string;
  fields: string;
  status: string | string[];
  fieldsToExport: string;
  currency: string;
};

export const routesInfoArray = [
  {
    route: 'payouts',
    label: 'Payouts',
    tableClassName: '--split-payments'
  },
  {
    route: 'rules',
    label: 'Split Rules',
    tableClassName: ''
  },

  {
    route: 'accounts',
    label: 'Accounts',
    tableClassName: '--split-payments-accounts'
  }
] as const;

export const getSplitPaymentTableHeaders = (tab: string, account: string) => {
  if (tab === 'Accounts' && !account) {
    return [
      {
        value: 'Account Name'
      },
      {
        value: 'Account Number'
      },
      {
        value: 'Bank'
      },
      {
        value: 'Total Settlement'
      },
      {
        value: 'Last Settlement Date'
      }
    ];
  }

  return [
    {
      value: 'Status'
    },
    {
      value: 'Payout ID'
    },
    {
      value: 'Recipient Name'
    },
    {
      value: 'Date and Time'
    },
    {
      value: 'Amount'
    }
  ];
};

export const getSingleAccountSummaryFn = (arg: SingleSplitAccountSummary | undefined, currency: string) => {
  const settlementDetails: { [key: string]: string | ReactElement }[] = [
    {
      'Total Settlement': `${formatAmount(arg?.total_settlement ?? 0)} ${currency}`,
      'Account Name': capitalize(arg?.account_name ?? 'Not Available'),
      'Account Number': arg?.account_number ?? 'Not Available'
    },
    {
      Bank: capitalize(arg?.bank_name ?? 'Not Available'),
      'Date Added': arg ? `${getDate(arg.created_at)} ${getTime(arg.created_at)}` : 'Not Available',
      'Last Settlement Date': arg?.last_settlement_date
        ? `${getDate(arg.last_settlement_date)} ${getTime(arg.last_settlement_date)}`
        : 'Not Available'
    }
  ];

  return settlementDetails;
};

export const splitAccountsTableContent = (data: SplitAccountsSummaryType[] | undefined, activeTab: string, activeCurrency: string) => {
  return data?.map(each => {
    return (
      <div
        key={each.account_number}
        className="div-table --row --split-payments-accounts"
        onClick={() => {
          history.push(`/dashboard/split-payments/accounts/${each?.reference}`);
          logBreadCrumb({
            event: breadCrumbEvents.disputes.tableClicked(activeTab),
            data: { tab: activeTab, reference: each.account_number }
          });
        }}
        role="button"
        tabIndex={0}
      >
        <div>
          <span className="body-row-header">Account Name</span>
          <span className="split-payment-text-blue">{each.account_name}</span>
        </div>

        <div>
          <span className="body-row-header">Account Number</span>

          <span onClick={e => e.stopPropagation()} style={{ color: '#414F5F' }}>
            <Copyable text={each.account_number} />
          </span>
        </div>

        <div>
          <span className="body-row-header">Bank:</span>
          {capitalizeRemovedash(each.bank_name)}
        </div>

        <div>
          <span className="body-row-header">Total Settlement:</span>
          <span className="trxn-id" style={{ fontWeight: '500' }}>
            {formatAmount(each.total_settlement)} {activeCurrency}
          </span>
        </div>

        <div>
          <span className="body-row-header">Last Settlement Date:</span>
          {each.last_settlement_date ? (
            <>
              <span>{getDate(each.last_settlement_date)}</span>
              <span className="annotation" style={{ marginLeft: '5px' }}>
                {getTime(each.last_settlement_date)}
              </span>
            </>
          ) : (
            <span style={{ opacity: 0.7 }}>Not Available</span>
          )}
        </div>
      </div>
    );
  });
};

export const splitPaymentsTableContent = (data: SplitPaymentsType[] | undefined, activeTab: string, activeCurrency: string) => {
  return data?.map(each => {
    return (
      <div
        key={each.settlement_payout_reference + each.recipient_number}
        className="div-table --row --split-payments"
        onClick={() => {
          history.push(`/dashboard/split-payments/payouts/${each.settlement_payout_reference}`);
          logBreadCrumb({
            event: breadCrumbEvents.disputes.tableClicked(activeTab),
            data: { tab: activeTab, reference: each.settlement_payout_reference }
          });
        }}
        role="button"
        tabIndex={0}
      >
        {activeTab === 'Payouts' && (
          <>
            <div>
              <span className="body-row-header">Status:</span>
              <span className={`status-pill smaller ${switchStatus(each.status)}`} />
              <span>{capitalizeRemovedash(each.status)}</span>
            </div>

            <div>
              <span className="body-row-header">Payout ID:</span>
              <span className="split-payment-text-blue">{each.settlement_payout_reference.toUpperCase()}</span>
            </div>

            <div>
              <span className="body-row-header">Recipient Name:</span>
              <span>{each.recipient_name}</span>
            </div>

            <div>
              <span className="body-row-header">Date and Time:</span>
              <span>{getDate(each.transaction_date)}</span>
              <span className="annotation" style={{ marginLeft: '5px' }}>
                {getTime(each.transaction_date)}
              </span>
            </div>

            <div>
              <span className="body-row-header">Amount:</span>
              <span style={{ fontWeight: '500' }}>
                {formatAmount(each.settlement_payout_amount)} {activeCurrency}
              </span>
            </div>
          </>
        )}
      </div>
    );
  });
};
