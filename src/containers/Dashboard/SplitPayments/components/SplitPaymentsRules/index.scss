.split-payment-rules {
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .__header {
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem 2rem;
    justify-content: space-between;
    margin: 2.5rem 0 0;

    & > div {
      h5 {
        align-items: center;
        display: flex;
        gap: 0.5rem;
      }

      p {
        max-width: 350px;

        @media (max-width: 1024px) {
          max-width: unset;
        }
      }
    }
    .header-btn {
      align-items: center;
      background-color: #2376f3;
      border-radius: 8px;
      font-weight: 500;
      padding: 0.5rem 1rem;
      color: #fff;
      display: flex;
      gap: 0.2rem;
      margin-bottom: 1rem;

      &:hover:disabled {
        color: #fff;
      }
    }
  }
}
