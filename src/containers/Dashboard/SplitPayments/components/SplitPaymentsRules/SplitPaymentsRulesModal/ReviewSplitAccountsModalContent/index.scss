.review-split-accounts-modal-content {
  .reviews-wrapper {
    background: #f3f4f8;
    border-radius: 8px;
    padding: 1rem;

    h6 {
      color: #94a7b7;
      font-weight: 400;
      line-height: 1.5rem;
      letter-spacing: 0.02rem;
    }

    .reviews {
      display: grid;
      gap: 1.5rem;
      margin-top: 2rem;
      max-height: 300px;
      overflow-y: auto;

      .review {
        display: flex;
        gap: 0.5rem;
        align-items: center;

        .account-info {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          border-radius: 5px;
          flex-grow: 1;

          &:focus {
            border-color: #2376f3;
          }

          strong {
            font-weight: 500;
          }
        }
      }
    }
  }
}
