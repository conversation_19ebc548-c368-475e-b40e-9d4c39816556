import { CreateSplitAccountResponseType, SplitRulePayloadType } from '+types';
import { capitalize, formatAmount } from '+utils';

import './index.scss';

const ReviewSplitAccountsModalContent = ({
  splitPayment,
  accounts
}: {
  splitPayment: SplitRulePayloadType;
  accounts: CreateSplitAccountResponseType[] | undefined;
}) => {
  const formatSplitValue = (arg: string | number) => (splitPayment.split_type === 'flat' ? 'NGN' + formatAmount(arg) : arg + '%');

  return (
    <div className="review-split-accounts-modal-content">
      <div className="reviews-wrapper">
        <h6>Review and confirm the selected split accounts for {capitalize(splitPayment.split_name)} settlements:</h6>

        <ul className="reviews">
          {splitPayment.split_accounts?.map(split => {
            const account = accounts?.find(acc => acc?.reference === split.split_account_reference);

            return (
              <li className="review" key={account?.account_number}>
                <div className="account-info">
                  <strong>{capitalize(account?.account_name ?? '')}</strong>
                  <span className="bank-name">
                    {capitalize(account?.bank_name ?? '')} ({account?.account_number})
                  </span>
                </div>
                <span className="value">{formatSplitValue(split.value)}</span>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default ReviewSplitAccountsModalContent;
