import { Dispatch } from 'react';
import { convertToWords } from 'react-number-to-words';

import BasicSelect from '+containers/Dashboard/Shared/BasicSelect';
import Icon from '+containers/Dashboard/Shared/Icons';
import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceholder';
import { CreateSplitAccountResponseType, SplitRulePayloadType, SplitSettingsResponseType } from '+types';
import { backwardAmountInput, capitalize, formatAmount, stripNonNumeric } from '+utils';

import SplitPaymentInfoMessage from '../../../SplitPaymentInfoMessage';
import { SplitPaymentModalStateType } from '../../splitPaymentRulesHelpers';
import useAddSplitRuleModalContent from './useAddSplitAccountModalContent';

import emptyStateImg from '+assets/img/dashboard/empty-state.png';

import './index.scss';

export default function AddSplitRuleModalContent({
  setSplitPayment,
  accounts = [],
  settings,
  currency,
  fetchingAccounts,
  state,
  setState
}: {
  currency: string;
  setSplitPayment: Dispatch<Partial<SplitRulePayloadType>>;
  accounts?: CreateSplitAccountResponseType[];
  settings: SplitSettingsResponseType;
  fetchingAccounts: boolean;
  state: SplitPaymentModalStateType;
  setState: Dispatch<Partial<SplitPaymentModalStateType>>;
}) {
  const {
    isFlat,
    dropdown,
    setDropdown,
    splitValueRemainder,
    maxSplitReached,
    accountAlreadyAdded,
    addSplitAccount,
    activeField,
    setActiveField,
    handleDeleteSplit
  } = useAddSplitRuleModalContent({
    setSplitPayment,
    settings,
    currency,
    state,
    setState
  });

  return (
    <div className="add-split-rule-modal-content">
      <div className="banner-wrapper">
        {isFlat ? (
          <SplitPaymentInfoMessage
            message={
              <span>
                This rule will only apply if the total split amount is less than the total amount to be settled.{' '}
                <button className="learn-more-btn" onClick={() => window.open('https://developers.korapay.com/', '_blank')}>
                  Learn more
                </button>
              </span>
            }
          />
        ) : (
          <div className="percentage-split-banner">
            <button onClick={() => setDropdown(!dropdown)} data-testid="split-left-dropdown-btn">
              <Icon
                className="caret-down"
                name="caretDown"
                stroke="currentColor"
                width={12}
                height={12}
                style={{ transform: dropdown ? 'rotate(180deg)' : '' }}
              />
              <span data-testid="split-remainder-text">You have {splitValueRemainder}% left to be split for this rule</span>
            </button>
            {dropdown && (
              <p data-testid="split-left-info-text">The remainder after the split would be settled into your default payment destination</p>
            )}
          </div>
        )}
      </div>
      <div className="add-account">
        <span className="select-account-label">Select Split Account</span>
        <div className="select-account">
          <BasicSelect
            dataTestId="select-account"
            setValue={(arg: string) => arg}
            placeholder="Select Account"
            disabled={maxSplitReached || !!state.error || !accounts.length || splitValueRemainder <= 0}
            resetOnSelect
            options={accounts.map(account => {
              const added = accountAlreadyAdded(account.reference);
              return {
                label: (
                  <button
                    disabled={added}
                    className={`account-option ${added ? 'option-disabled' : ''}`}
                    onClick={() => addSplitAccount(account)}
                  >
                    {capitalize(account.account_name)} ({account.account_number})
                  </button>
                ),
                value: ''
              };
            })}
          />
        </div>

        <small {...(maxSplitReached ? { className: 'error' } : {})}>
          {maxSplitReached ? (
            <>You&apos;ve added the maximum ({settings.split_account.max_count}) number of bank accounts.</>
          ) : (
            <>
              You can add up to{' '}
              <strong>
                {convertToWords(settings.split_account.max_count).toLowerCase()} ({settings.split_account.max_count}) split accounts
              </strong>
            </>
          )}
        </small>
      </div>
      <div className="split-group">
        {fetchingAccounts ? (
          <LoadingPlaceholder type="text" />
        ) : accounts.length ? (
          state.splitPayment.split_accounts?.length ? (
            state.splitPayment.split_accounts.map((split, i) => {
              const account = accounts.find(acc => acc?.reference === split.split_account_reference);

              const isError = state.error && activeField === i;

              const updateAccountSplitValue = (value: string) => {
                const updateSplit = state.splitPayment.split_accounts!.map((obj, index) => {
                  if (index === i) {
                    if (activeField !== i) setActiveField(i);
                    const formattedValue = stripNonNumeric(value.replace(/,/g, '').trim());
                    return { ...obj, value: isFlat ? backwardAmountInput(formattedValue) || formattedValue : formattedValue };
                  }

                  return obj;
                });

                setSplitPayment({ split_accounts: updateSplit });
              };

              return (
                <div className="split-card-wrapper" key={split.split_account_reference}>
                  <div data-testid="split-account-card" className="split-card">
                    <div className={`account-info ${isError ? 'split-error' : ''}`}>
                      <strong>{account?.account_name.toUpperCase()}</strong>
                      <span>
                        {account?.bank_name} ({account?.account_number})
                      </span>
                    </div>

                    <div className={`split-value-input-wrapper ${isError ? 'split-error' : ''}`}>
                      {state.splitPayment.split_type === 'percentage' && <span className="percent">%</span>}
                      <input
                        maxLength={isFlat ? 10 : 3}
                        onChange={e => updateAccountSplitValue(e.target.value)}
                        value={state.splitPayment.split_accounts![i].value}
                        data-testid="split-value-input"
                        placeholder={isFlat ? `${currency} ${formatAmount(0)}` : '0'}
                        type="text"
                        className="split-value-input"
                        name="destinationName"
                      />
                    </div>

                    <button
                      className="delete-btn"
                      data-testid="delete-account-card"
                      onClick={() => handleDeleteSplit(account?.reference ?? '')}
                    >
                      <Icon name="circledTrashOutline" width={32} height={32} />
                    </button>
                  </div>
                  {isError && (
                    <small className="split-account-error" data-testid="split-account-error">
                      {state.error}
                    </small>
                  )}
                </div>
              );
            })
          ) : (
            <div className="empty-split-placeholder">
              <img alt="Nothing" src={emptyStateImg} loading="lazy" /> <p>No split account added yet</p>
            </div>
          )
        ) : (
          <div className="empty-split-account-placeholder">
            <img alt="Nothing" src={emptyStateImg} loading="lazy" />
            <p>It looks like you haven&apos;t added any split accounts yet. To get started, go to &apos;Accounts&apos; page.</p>
          </div>
        )}
      </div>
    </div>
  );
}
