import { Dispatch, useEffect, useState } from 'react';

import { CreateSplitAccountResponseType, SplitRulePayloadType, SplitSettingsResponseType } from '+types';
import { formatAmount } from '+utils';

import { SplitPaymentModalStateType } from '../../splitPaymentRulesHelpers';

export default function useAddSplitRuleModalContent({
  setSplitPayment,
  settings,
  currency,

  state,
  setState
}: {
  currency: string;
  setSplitPayment: Dispatch<Partial<SplitRulePayloadType>>;
  settings: SplitSettingsResponseType;
  state: SplitPaymentModalStateType;
  setState: Dispatch<Partial<SplitPaymentModalStateType>>;
}) {
  const [dropdown, setDropdown] = useState(false);
  const [activeField, setActiveField] = useState<number | null>(null);
  const [splitValueRemainder, setSplitValueRemainder] = useState(settings.split_account.max_total_rate);
  const splitValue = state.splitPayment.split_accounts?.reduce((acc, curr) => acc + Number(curr.value), 0) || 0;

  const isFlat = state.splitPayment.split_type === 'flat';
  const maxSplitReached = state.splitPayment.split_accounts?.length === settings.split_account.max_count;

  const formatSplitValue = (arg: string | number) => (isFlat ? currency + ' ' + formatAmount(arg) : arg + '%');

  const handleDeleteSplit = (ref: string) => {
    if (state.splitPayment.split_accounts?.length === 1) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { split_accounts, ...splitPaymentCopy } = state.splitPayment;

      setState({ splitPayment: splitPaymentCopy });
      return;
    }
    setSplitPayment({
      split_accounts: state.splitPayment.split_accounts?.filter(split => split.split_account_reference !== ref)
    });
  };

  const addSplitAccount = (account: CreateSplitAccountResponseType) => {
    setSplitPayment({
      split_accounts: [
        { value: '', split_destination_type: 'bank_account', split_account_reference: account?.reference ?? '' },
        ...(state.splitPayment.split_accounts ?? [])
      ]
    });
  };

  const accountAlreadyAdded = (ref: string) => {
    return !!state.splitPayment.split_accounts?.find(split => split.split_account_reference === ref);
  };

  useEffect(() => {
    if (!isFlat) {
      const remainder = settings.split_account.max_total_rate - splitValue;
      setSplitValueRemainder(remainder < 0 ? 0 : remainder);

      if (remainder < 0) {
        setState({ error: `Split limit exceeded by ${formatSplitValue(Math.abs(remainder))}` });
      } else {
        setState({ error: '' });
      }
    }
  }, [splitValue]);

  return {
    isFlat,
    dropdown,
    setDropdown,
    splitValueRemainder,
    maxSplitReached,
    accountAlreadyAdded,
    addSplitAccount,
    activeField,
    setActiveField,
    handleDeleteSplit
  };
}
