.add-split-rule-modal-content {
  .banner-wrapper {
    padding: 0 0 1rem;
    border-bottom: 1px solid #dde2ec;

    .learn-more-btn {
      all: unset;
      color: #2376f3;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .percentage-split-banner {
      background-color: #fff7ed;
      border-radius: 8px;
      padding: 0.5rem 1rem;

      button {
        align-items: center;
        color: #915200;
        display: flex;
        font-weight: 500;
        gap: 0.5rem;
        padding: 0;

        &:focus {
          border: 0;
          box-shadow: none;
          outline: 0;
        }

        .caret-down {
          transition: all 0.3s ease;
        }
      }

      p {
        animation: fadeIn 0.3s ease-in;
        margin: 0.25rem 0;
      }
    }
  }

  .add-account {
    margin: 1rem 0 0;
    padding: 0 0 1rem;

    .select-account-label {
      font-size: 0.9rem;
      font-weight: 500;
    }

    .select-account {
      margin: 0.2rem 0;

      .add-new-account-option {
        all: unset;
        align-items: center;
        color: #2376f3;
        display: flex;
        gap: 0.5rem;
        padding: 0.75rem;
        text-align: left;
        width: 100%;
      }

      .account-option {
        all: unset;
        display: flex;
        padding: 0.75rem;
        width: 100%;
      }

      .option-disabled {
        color: #a9afbc;
        cursor: not-allowed;
      }
    }

    .error {
      color: #f32345;
    }
  }

  .split-group {
    border-top: 1px solid #dde2ec;
    margin: 1rem 0 0;
    padding: 0.75rem 0 1rem;
    display: grid;
    gap: 2rem;

    .empty-split-placeholder {
      background: #f9fbfd;
      align-items: center;
      display: flex;
      padding: 1rem;
      border-radius: 8px;
      justify-content: center;
      margin: 1rem 0;
      gap: 0.5rem;

      img {
        width: 40px;
      }

      p {
        color: #414f5f;
        margin: 0;
      }
    }

    .empty-split-account-placeholder {
      background: #f9fbfd;
      border-radius: 10px;
      display: grid;
      gap: 1rem;
      padding: 1rem;
      justify-items: center;

      img {
        width: 30px;
      }

      p {
        max-width: 80%;
        color: #414f5f;
        font-size: 0.9rem;
        line-height: 1.5rem;
        margin: 0 auto;
        text-align: center;
      }
    }

    .split-card-wrapper {
      position: relative;

      .split-card {
        animation: fadeIn 0.3s ease-in;
        display: flex;
        gap: 0.1rem 0.75rem;
        justify-content: space-between;

        .account-info {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
          background: #f9fbfd;
          border: 2.5px solid #dde2ec;
          border-radius: 5px;

          padding: 0.25rem 0.75rem;
          flex-grow: 1;

          &:focus {
            border-color: #2376f3;
          }

          strong {
            font-size: 0.85rem;
            font-weight: 500;
          }
          span {
            font-size: 0.85rem;
          }
        }

        .split-value-input-wrapper {
          align-items: center;
          border: 2px solid #dde2ec;
          border-radius: 5px;
          display: flex;
          flex-shrink: 0;
          width: 100px;

          &:focus-within {
            border-color: #2376f3;
          }

          .split-value-input {
            border: 0;
            box-shadow: none;
            display: block;
            flex-grow: 1;
            padding: 0.25rem 0;
            text-align: center;
          }

          .percent {
            color: #a9afbc;
            margin: 0 5px 0 8px;
          }
        }
        .split-error,
        .split-error:focus-within {
          border: 2px solid #f32345;
        }

        .delete-btn {
          flex-shrink: 0;
          align-items: center;
          flex-direction: row;
          align-self: center;
          padding: 0;
          margin: 0;
        }
      }

      .split-account-error {
        color: #f32345;
        display: flex;
        font-size: 0.9rem;
        letter-spacing: 0.03rem;
        margin: 0.3rem 0 0;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
      }
    }
  }
}
