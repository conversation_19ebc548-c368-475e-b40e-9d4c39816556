import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import MockIndex from '+mock/MockIndex';
import { SingleSplitRuleType, SplitSettingsResponseType } from '+types';

import SplitPaymentModal from '..';

const closeModal = vi.fn();
const setConfigToModify = vi.fn();

const paymentSettings = {
  enabled: true,
  split_account: { max_count: 5, max_total_rate: 70, min_settlement_amount: 1000 }
};

const MockSplitPaymentModal = ({
  type = '',
  settings = paymentSettings,
  configToModify = null
}: {
  type?: string;
  settings?: SplitSettingsResponseType;
  configToModify?: SingleSplitRuleType | null;
}) => (
  <MockIndex>
    <SplitPaymentModal
      settings={settings}
      configToModify={configToModify}
      currency="NGN"
      setConfigToModify={setConfigToModify}
      modalType={type}
      setModalType={closeModal}
    />
  </MockIndex>
);

describe('Test for split payment modals', () => {
  describe('Terms and Conditions Modal test', () => {
    it('renders Terms and Conditions Modal correctly', async () => {
      render(<MockSplitPaymentModal type="termsAndConditions" />);

      expect(screen.getByText(/Kora Split Payment Terms and Conditions/i)).toBeInTheDocument();

      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      await userEvent.click(screen.getByTestId('terms-agreement-checkbox'));
      expect(screen.getByTestId('modal-second-btn')).not.toBeDisabled();
    });
  });

  describe('Add Split Rule Modal test', () => {
    it('renders Add Split Rule Modal correctly', () => {
      render(<MockSplitPaymentModal type="rule" />);

      expect(screen.getByText(/Add Split payment Rule/i)).toBeInTheDocument();
      expect(screen.getByTestId('split-name-input')).toHaveValue('');
      expect(screen.getByTestId('fixed-split')).not.toBeChecked();
      expect(screen.getByTestId('percentage-split')).not.toBeChecked();
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      expect(screen.getByTestId('modal-first-btn')).not.toBeDisabled();
    });

    it('Closes the modal when the cancel button is clicked', async () => {
      render(<MockSplitPaymentModal type="rule" />);

      const user = userEvent.setup();

      await user.click(screen.getByTestId('modal-first-btn'));

      expect(closeModal).toHaveBeenCalled();
    });

    it('Enable the continue button if split name field is filled, fixed percentage option is selected and reason for split is added', async () => {
      render(<MockSplitPaymentModal type="rule" />);

      const user = userEvent.setup();

      await user.type(screen.getByTestId('split-name-input'), 'Test Split');
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();

      await user.click(screen.getByTestId('percentage-split'));
      expect(screen.getByTestId('fixed-split')).not.toBeChecked();
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();

      await user.type(screen.getByTestId('reason-for-split-input'), 'Test Reason');
      expect(screen.getByTestId('modal-second-btn')).not.toBeDisabled();
    });
  });

  describe('Review Split Rule Modal test', () => {
    it('renders Confirm Split Rule Modal correctly', () => {
      render(<MockSplitPaymentModal type="confirmRule" />);

      expect(screen.getByText(/Review Split Rule/i)).toBeInTheDocument();
      expect(screen.getByText(/Split Rule Name/i)).toBeInTheDocument();
      expect(screen.getByText(/Split Type/i)).toBeInTheDocument();
      expect(screen.getByText(/Reason/i)).toBeInTheDocument();
      expect(screen.getByTestId('modal-second-btn')).not.toBeDisabled();
      expect(screen.getByTestId('modal-first-btn')).not.toBeDisabled();
    });

    it('Goes to previous modal when the go back button is clicked', async () => {
      render(<MockSplitPaymentModal type="confirmRule" />);

      const user = userEvent.setup();

      await user.click(screen.getByTestId('modal-first-btn'));

      expect(closeModal).toHaveBeenCalled();
    });
  });

  describe('Add Split Destination Modal test', () => {
    it('renders Add Split Destination Modal correctly', async () => {
      render(<MockSplitPaymentModal type="addAccounts" />);

      expect(screen.getByTestId('select-account')).toBeInTheDocument();

      expect(screen.getByText(/Add Split Account to Rule/i)).toBeInTheDocument();
      expect(screen.getByText(/You have 70% left to be split for this rule/i)).toBeInTheDocument();
      expect(await screen.findByText(/No split account added yet/i)).toBeInTheDocument();
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      expect(screen.getByTestId('modal-first-btn')).not.toBeDisabled();
    });

    it('renders account lists and add account option when the "select Account" dropdown is clicked', async () => {
      render(<MockSplitPaymentModal type="addAccounts" />);

      await waitFor(() => {
        expect(screen.getByTestId('select-account')).toBeEnabled();
      });

      await userEvent.click(screen.getByTestId('select-account'));

      expect(await screen.findByText('First (**********)')).toBeInTheDocument();
      expect(screen.getByText('Second (**********)')).toBeInTheDocument();
      expect(screen.getByText('Third (**********)')).toBeInTheDocument();
    });

    test(' add account flow on the modal', async () => {
      render(<MockSplitPaymentModal type="addAccounts" />);

      await waitFor(() => {
        expect(screen.getByTestId('select-account')).toBeEnabled();
      });

      await userEvent.click(screen.getByTestId('select-account'));

      await userEvent.click(await screen.findByText('First (**********)'));

      expect(screen.getByTestId('split-value-input')).toBeInTheDocument();
      expect(screen.getByText('FIRST')).toBeInTheDocument();
      expect(screen.getByText('UBA (**********)')).toBeInTheDocument();

      await userEvent.type(screen.getByTestId('split-value-input'), '80');
      expect(screen.getByTestId('split-account-error')).toBeInTheDocument();
      expect(screen.getByText('Split limit exceeded by 10%')).toBeInTheDocument();

      await userEvent.clear(screen.getByTestId('split-value-input'));

      await userEvent.type(screen.getByTestId('split-value-input'), '50');
      expect(screen.getByTestId('modal-second-btn')).not.toBeDisabled();
    });

    it('an existing account number should not be selectable', async () => {
      render(<MockSplitPaymentModal type="addAccounts" />);

      await waitFor(() => {
        expect(screen.getByTestId('select-account')).toBeEnabled();
      });

      await userEvent.click(screen.getByTestId('select-account'));

      await userEvent.click(await screen.findByText('First (**********)'));

      await userEvent.click(screen.getByTestId('select-account'));

      expect(await screen.findByText('First (**********)')).toBeDisabled();
    });

    it('an account number can be deleted', async () => {
      render(<MockSplitPaymentModal type="addAccounts" />);

      await waitFor(() => {
        expect(screen.getByTestId('select-account')).toBeEnabled();
      });

      await userEvent.click(screen.getByTestId('select-account'));

      await userEvent.click(await screen.findByText('First (**********)'));
      expect(screen.getByText('FIRST')).toBeInTheDocument();
      expect(screen.getByText('UBA (**********)')).toBeInTheDocument();

      await userEvent.click(screen.getByTestId('delete-account-card'));
      expect(screen.queryByText('FIRST')).not.toBeInTheDocument();
      expect(screen.queryByText('GTBank Plc (**********)')).not.toBeInTheDocument();
    });
  });
});
