import { Dispatch, ReactNode, SetStateAction } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';

import './index.scss';

const SuccessSplitPaymentModalContent = ({
  iconType = 'circledCheck',
  setModalType,
  heading,
  subheading
}: {
  setModalType: Dispatch<SetStateAction<string>>;
  iconType?: 'circledCheck' | 'circledTrash';
  heading: string;
  subheading: ReactNode;
}) => {
  return (
    <div className="success-split-payment-modal-content">
      {<Icon name={iconType} height={80} width={80} />}
      <h5>{heading}</h5>
      <p>{subheading}</p>
      <button className="btn" onClick={() => setModalType('')}>
        Dismiss
      </button>
    </div>
  );
};

export default SuccessSplitPaymentModalContent;
