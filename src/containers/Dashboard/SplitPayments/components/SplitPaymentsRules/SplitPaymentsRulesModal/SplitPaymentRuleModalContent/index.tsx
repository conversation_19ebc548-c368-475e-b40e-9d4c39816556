import { Dispatch, useEffect } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import ToolTip from '+containers/Dashboard/Shared/Tooltip';
import { SingleSplitRuleType, SplitRulePayloadType, SplitType } from '+types';
import { capitalizeFirst, cleanInput } from '+utils';

import { SplitPaymentModalStateType } from '../../splitPaymentRulesHelpers';

import './index.scss';

export default function SplitPaymentRuleModalContent({
  setSplitPayment,
  rules,
  state,
  setState,
  configToModify
}: {
  setSplitPayment: Dispatch<Partial<SplitRulePayloadType>>;
  rules?: SingleSplitRuleType[];
  configToModify: SingleSplitRuleType | null;
  state: SplitPaymentModalStateType;
  setState: Dispatch<Partial<SplitPaymentModalStateType>>;
}) {
  useEffect(() => {
    const prevSplitNames = rules?.map(config => config.split_name.trim().toLowerCase());
    const nameAlreadyExists =
      configToModify?.split_name.toLowerCase() === state.splitPayment.split_name.toLowerCase()
        ? false
        : prevSplitNames?.includes(state.splitPayment.split_name.toLowerCase().trim());

    const error = nameAlreadyExists ? 'Name already exists' : '';

    if (error !== state.error) setState({ error });
  }, [state.splitPayment.split_name]);

  return (
    <div className="split-rule-modal-content">
      <div className="info-group">
        <Icon name="infoIcon" fill="#FA9500" width={24} height={24} />
        <p>If the split operation fails for any reason, your pay-in will be settled to your default settlement destination.</p>
      </div>
      <div className="form-group">
        <label htmlFor="splitRuleName">Give split rule a name</label>
        <input
          placeholder="Enter name here"
          maxLength={30}
          data-testid="split-name-input"
          value={capitalizeFirst(state.splitPayment.split_name)}
          onChange={e => setSplitPayment({ split_name: cleanInput(e.target.value) })}
          type="text"
          id="splitRuleName"
          name="splitRuleName"
        />
        {state.error && <small>{state.error}</small>}
      </div>

      <div className="form-radio-group">
        <div className="form-radio-group__heading">What type of rule?</div>

        <div className="form-radio-wrapper">
          <div className="form-radio">
            <div className="radio-wrapper">
              <input
                disabled={!!configToModify}
                value="percentage"
                checked={state.splitPayment.split_type === 'percentage'}
                onChange={e => setSplitPayment({ split_type: e.target.value as SplitType })}
                type="radio"
                id="percentage-split"
                data-testid="percentage-split"
                name="percentageSplitRuleType"
              />
            </div>
            <div>
              <div className="label-wrapper">
                <label htmlFor="percentage-split">Percentage Split</label>{' '}
                <ToolTip
                  classname="split-rule-tooltip"
                  message="The percentage you set determines the amount sent to the bank accounts linked to this split rule"
                />
              </div>

              <p className="radio-text">A percentage goes to your chosen destination.</p>
            </div>
          </div>
          <div className="form-radio">
            <div className="radio-wrapper">
              <input
                disabled={!!configToModify}
                value="flat"
                checked={state.splitPayment.split_type === 'flat'}
                data-testid="fixed-split"
                onChange={e => setSplitPayment({ split_type: e.target.value as SplitType })}
                type="radio"
                id="fixed-split"
                name="fixedSplitRuleType"
              />
            </div>
            <div>
              <div className="label-wrapper">
                <label htmlFor="fixed-split">Fixed Split</label>{' '}
                <ToolTip
                  positionTop
                  classname="split-rule-tooltip"
                  message="The fixed amount you define will be sent to each bank account you've linked to this split rule."
                />
              </div>
              <p className="radio-text">A fixed portion goes to your chosen destination.</p>
            </div>
          </div>
        </div>
      </div>

      <div className="form-group split-rule-reason">
        <label htmlFor="splitRuleReason">Reason for split?</label>
        <textarea
          disabled={!!configToModify}
          placeholder="E.g For distribution of student fees"
          maxLength={50}
          data-testid="reason-for-split-input"
          value={capitalizeFirst(state.splitPayment.split_description)}
          onChange={e => setSplitPayment({ split_description: cleanInput(e.target.value) })}
          id="splitRuleReason"
          name="splitRuleReason"
        />
      </div>
    </div>
  );
}
