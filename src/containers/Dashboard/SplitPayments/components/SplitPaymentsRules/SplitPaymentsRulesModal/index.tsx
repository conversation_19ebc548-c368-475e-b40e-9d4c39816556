import { Dispatch, SetStateAction, useEffect } from 'react';

import Modal from '+containers/Shared/Modal';
import { useReducerState } from '+hooks';
import { SplitPaymentServices } from '+services/split-payment-services';
import { SingleSplitRuleType, SplitPaymentAccountsArrayType, SplitRulePayloadType, SplitSettingsResponseType, SplitType } from '+types';

import { getSplitPaymentModalState } from '../splitPaymentRulesHelpers';
import AddSplitAccountModalContent from './AddSplitAccountModalContent';
import ConfirmSplitRuleModalContent from './ConfirmSplitRuleModalContent';
import ReviewSplitAccountsModalContent from './ReviewSplitAccountsModalContent';
import SplitPaymentRuleModalContent from './SplitPaymentRuleModalContent';
import SplitRuleCreatedModalContent from './SplitRuleCreatedModalContent';
import SuccessSplitPaymentModalContent from './SuccessSplitPaymentModalContent';
import TermsAndConditionsModalContent from './TermsAndConditionsModalContent';

import './index.scss';

const SplitPaymentModal = ({
  settings,
  modalType,
  setModalType,
  currency,
  configToModify,
  setConfigToModify,
  rules
}: {
  currency: string;
  settings: SplitSettingsResponseType;
  modalType: string;
  setModalType: Dispatch<SetStateAction<string>>;
  configToModify: SingleSplitRuleType | null;
  setConfigToModify: Dispatch<SetStateAction<SingleSplitRuleType | null>>;
  rules?: SingleSplitRuleType[];
}) => {
  const [state, setState] = useReducerState(getSplitPaymentModalState(currency));

  const isMultipleAccounts = (state.splitPayment.split_accounts?.length || 0) > 1;
  const splitRef = configToModify?.split_reference ?? state.createdSplitRef;

  const { data: accounts, isLoading: fetchingAccounts } = SplitPaymentServices.useGetSplitAccounts({
    params: { currency }
  });

  const { mutateAsync: createSplitRuleFn } = SplitPaymentServices.useCreateSplitRule({
    onSuccess: data => {
      setState({ createdSplitRef: data?.data?.reference });
      setModalType('ruleCreated');
    }
  });

  const { mutateAsync: updateSplitRuleFn } = SplitPaymentServices.useUpdateSplitRule({
    id: splitRef,

    onSuccess: () => setModalType(configToModify ? 'updateSuccessful' : 'addAccountsSuccessful')
  });

  const { mutateAsync: deleteSplitRuleFn } = SplitPaymentServices.useDeleteSplitRule({
    id: configToModify?.split_reference ?? '',
    onSuccess: () => setModalType('deleteSuccessful')
  });

  const setSplitPayment = (splitPayment: Partial<SplitRulePayloadType>) => {
    setState({
      splitPayment: {
        ...state.splitPayment,
        ...splitPayment
      }
    });
  };

  useEffect(() => {
    if (configToModify) {
      setState({
        splitPayment: {
          currency,
          split_name: configToModify.split_name,
          split_description: configToModify.split_description,
          split_type: configToModify.split_type,
          split_accounts: configToModify.split_accounts?.map(split => ({
            value: split.value,
            split_destination_type: split.split_destination_type,
            split_account_reference: split.split_destination.reference
          }))
        }
      });
    }
    return () => setConfigToModify(null);
  }, []);

  const getUpdatedSplitPaymentPayload = () => {
    if (state.createdSplitRef) {
      return {
        split_accounts: state.splitPayment.split_accounts
      };
    }

    return Object.keys(state.splitPayment).reduce(
      (acc, key) => {
        const typedKey = key as keyof typeof state.splitPayment;
        const value = state.splitPayment[typedKey];

        if (JSON.stringify(value) !== JSON.stringify(configToModify?.[typedKey])) {
          acc[typedKey] = value as string & ((SplitPaymentAccountsArrayType[] | undefined) & SplitType);
        }
        return acc;
      },
      {} as typeof state.splitPayment
    );
  };

  const modal = {
    termsAndConditions: {
      content: <TermsAndConditionsModalContent state={state} setState={setState} />,
      firstButtonText: 'Cancel',
      firstButtonAction: () => setModalType(''),
      secondButtonText: 'Continue',
      secondButtonAction: () => setModalType('rule'),
      secondButtonDisable: !state.acceptTerms
    },

    rule: {
      heading: `${configToModify ? 'Update' : 'Add'} Split Payment Rule`,
      description:
        'Split payment rules determine how each of your transactions (Pay-ins) will be settled - what portion of the transaction goes to your settlement destination.',
      content: (
        <SplitPaymentRuleModalContent
          configToModify={configToModify}
          rules={rules}
          state={state}
          setState={setState}
          setSplitPayment={setSplitPayment}
        />
      ),
      firstButtonText: configToModify ? 'Cancel' : 'Back',
      firstButtonAction: () => setModalType(configToModify ? '' : 'termsAndConditions'),
      secondButtonText: 'Continue',
      secondButtonAction: () => setModalType(configToModify ? 'addAccounts' : 'confirmRule'),
      secondButtonDisable:
        !state.splitPayment.split_type || !state.splitPayment.split_name || !state.splitPayment.split_description || !!state.error,
      size: 'smd'
    },

    confirmRule: {
      heading: `Review Split Rule`,
      description: "Please confirm the details of the split rule you're about to create.",
      content: <ConfirmSplitRuleModalContent state={state} />,
      firstButtonText: 'Back',
      size: 'md',
      firstButtonAction: () => setModalType('rule'),
      secondButtonText: `Yes, Create`,
      secondButtonAction: async () =>
        configToModify ? updateSplitRuleFn(getUpdatedSplitPaymentPayload()) : createSplitRuleFn(state.splitPayment),
      secondButtonDisable: false
    },

    ruleCreated: {
      content: <SplitRuleCreatedModalContent />,
      firstButtonText: 'Done',
      size: 'md',
      firstButtonAction: () => setModalType(''),
      secondButtonText: `Add Split Accounts`,
      secondButtonAction: () => setModalType('addAccounts'),
      secondButtonDisable: false
    },

    addAccounts: {
      heading: configToModify ? 'Update Split Account for Rule' : 'Add Split Account to Rule',
      description: `Specify the ${state.splitPayment.split_type === 'flat' ? 'fixed amount' : 'percentage'} and corresponding destination for each split payment.`,
      content: (
        <AddSplitAccountModalContent
          state={state}
          setState={setState}
          settings={settings}
          accounts={accounts?.data}
          setSplitPayment={setSplitPayment}
          currency={currency}
          fetchingAccounts={fetchingAccounts}
        />
      ),
      firstButtonText: configToModify ? 'Back' : 'Cancel',
      firstButtonAction: () => setModalType(configToModify ? 'rule' : ''),
      secondButtonText: `${configToModify ? 'Update' : 'Add'} Split Account${isMultipleAccounts ? 's' : ''}`,
      secondButtonAction: () => setModalType('reviewAccounts'),
      secondButtonDisable:
        !state.splitPayment.split_accounts?.length || !!state.error || state.splitPayment.split_accounts.some(account => !+account.value)
    },

    reviewAccounts: {
      heading: `Review Split Account${isMultipleAccounts ? 's' : ''}`,
      description: `Please confirm the details of the split account${isMultipleAccounts ? 's' : ''} you are adding to this split rule.`,
      content: <ReviewSplitAccountsModalContent splitPayment={state.splitPayment} accounts={accounts?.data} />,
      firstButtonText: 'Back',
      size: 'md',
      firstButtonAction: () => setModalType('addAccounts'),
      secondButtonText: `Yes, ${configToModify ? 'Update' : ` Add Account${isMultipleAccounts ? 's' : ''}`}`,
      secondButtonAction: async () =>
        splitRef ? updateSplitRuleFn(getUpdatedSplitPaymentPayload()) : createSplitRuleFn(state.splitPayment),
      secondButtonDisable: false
    },

    addAccountsSuccessful: {
      description: (
        <SuccessSplitPaymentModalContent
          heading={`${state.splitPayment.split_accounts?.length || 0} Split Account${isMultipleAccounts ? 's' : ''} Added`}
          subheading="You can now use these accounts in your split payment rules."
          setModalType={setModalType}
        />
      ),
      showButtons: false,
      size: 'sm'
    },

    confirmDelete: {
      heading: <h4 className="delete-split-heading">Delete Split Rule?</h4>,
      description: (
        <p className="delete-split-description">
          Are you sure you want to <strong>delete</strong> this split rule? This action cannot be undone.
        </p>
      ),
      firstButtonAction: () => setModalType(''),
      firstButtonText: 'Dismiss',
      secondButtonText: 'Yes, Delete',
      secondButtonAction: async () => deleteSplitRuleFn({}),
      secondButtonColor: '#F32345',
      size: 'sm'
    },

    updateSuccessful: {
      description: (
        <SuccessSplitPaymentModalContent
          heading="Split Rule Updated"
          subheading={
            <>
              <strong>Your {state.splitPayment.split_name}</strong> has been updated successfully
            </>
          }
          setModalType={setModalType}
        />
      ),
      showButtons: false,
      size: 'sm'
    },

    deleteSuccessful: {
      description: (
        <SuccessSplitPaymentModalContent heading="Deleted" subheading="The split rule has been deleted." setModalType={setModalType} />
      ),
      showButtons: false,
      size: 'sm'
    }
  }[modalType];

  return (
    <section className="split-payment-modal">
      <Modal
        close={() => setModalType('')}
        secondButtonDisable={modal?.secondButtonDisable}
        heading={modal?.heading}
        description={modal?.description}
        content={modal?.content}
        firstButtonText={modal?.firstButtonText}
        firstButtonAction={modal?.firstButtonAction}
        secondButtonText={modal?.secondButtonText}
        secondButtonAction={modal?.secondButtonAction}
        secondButtonActionIsTerminal={false}
        showButtons={modal?.showButtons}
        size={modal?.size}
        secondButtonColor={modal?.secondButtonColor}
      />
    </section>
  );
};

export default SplitPaymentModal;
