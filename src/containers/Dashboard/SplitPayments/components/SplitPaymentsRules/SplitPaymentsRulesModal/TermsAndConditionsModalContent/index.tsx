import Icon from '+containers/Dashboard/Shared/Icons';

import { SplitPaymentModalStatePropsType } from '../../splitPaymentRulesHelpers';

import './index.scss';

export default function TermsAndConditionsModalContent({ state, setState }: SplitPaymentModalStatePropsType) {
  return (
    <div className="split-payment-modal__terms-and-conditions" aria-labelledby="terms-and-conditions-modal-title">
      <Icon name="circledCaution" className="terms-icon" height={80} width={80} />
      <h1 id="terms-and-conditions-modal-title" className="heading">
        Kora Split Payment Terms and Conditions
      </h1>
      <p className="subheading">Before using the Split Payments feature, you must agree to our terms and conditions.</p>
      <div className="terms-wrapper">
        <div className="terms">
          <div className="terms-content">
            <p className="terms-text">
              These Split Payment Terms and Conditions (“Terms”) apply to your use of the Split Payment feature (“Feature”) provided by
              Korapay (“<PERSON><PERSON>,” “we,” “us,” or “our”). By accessing or using the Feature, you agree to be bound by these Terms.
            </p>

            <p className="terms-text">
              These Terms are supplemental to, and incorporate by reference, the Kora Terms of Use, available at{' '}
              <a className="link" href="https://www.korahq.com/terms-of-use" target="_blank" rel="noopener noreferrer">
                https://www.korahq.com/terms-of-use
              </a>{' '}
              (the “Main Terms of Use”). Your use of the Feature is subject to both these Terms and the Main Terms of Use. In the event of
              any conflict or inconsistency between these Terms and the Main Terms of Use, these Terms shall prevail solely with respect to
              your use of the Split Payment Feature.
            </p>
          </div>
        </div>
        <div className="terms" aria-labelledby="split-payment-service">
          <h3 id="split-payment-service" className="terms-heading">
            Split Payment Service
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              The Split Payment Feature enables you to automatically divide incoming payments (“Pay-ins”) across multiple pre-configured
              bank accounts. This functionality streamlines your financial operations by facilitating the automated distribution of funds
              for various designated purposes, including but not limited to VAT payments, commission payouts, or allocations to different
              subsidiary accounts.
            </p>
            <ol>
              <li>
                <p className="terms-text">
                  <strong>Percentage Split:</strong> A defined percentage of the total transaction amount is directed to a chosen
                  destination account.
                </p>
              </li>
              <li>
                <p className="terms-text">
                  <strong>Fixed Split:</strong> A specific monetary amount, fixed in value, is directed to a chosen destination account from
                  the total transaction.
                </p>
              </li>
            </ol>
          </div>
        </div>

        <div className="terms">
          <div className="terms-content">
            <p className="terms-text">
              Upon receiving an incoming payment, Kora will automatically execute the Split Rules you have configured. This execution occurs
              in real-time or near real-time at the point of transaction settlement. Our system is designed to ensure that funds are not
              unduly held, and a fallback mechanism is in place to manage unforeseen issues.{' '}
            </p>

            <p className="terms-text">
              A mandatory portion of each incoming Pay-in will be retained and will not be subject to the split. This retained amount is
              automatically settled to your designated default primary settlement destination. This ensures that you always receive a base
              portion of each transaction, irrespective of any configured Split Rules.{' '}
            </p>
            <p className="terms-text">
              In the event that a split operation fails for any reason, such as an issue with a designated destination account, the entire
              incoming payment will be automatically settled to your default primary settlement destination. This mechanism prevents loss of
              funds or funds being held in an unresolved state.
            </p>
            <p className="terms-text">
              Kora provides you with tools to audit your Split payments, including an “Export Transactions” feature for generating reports,
              accessing payment history, and viewing beneficiaries.
            </p>
          </div>
        </div>

        <div className="terms" aria-labelledby="obligations-of-the-merchant">
          <h3 id="obligations-of-the-merchant" className="terms-heading">
            Obligations of the Merchant
          </h3>
          <div className="terms-content">
            <p className="terms-text">You are solely responsible for:</p>
            <ol>
              <li>
                <p className="terms-text">
                  Configuring Split Rules directly via your merchant dashboard or API. This includes setting up payment rules and connecting
                  bank accounts (which must be done via the merchant dashboard).
                </p>
              </li>
              <li>
                <p className="terms-text">
                  Ensuring the accuracy of all destination account details provided for beneficiaries, including bank account numbers,
                  names, and associated details. Kora bears no liability for any financial loss or misdirection of funds resulting from
                  inaccurate or fraudulent bank account details supplied by you.
                </p>
              </li>
              <li>
                <p className="terms-text">
                  Updating or deleting existing Split Rules and bank account connections through the merchant dashboard. All modifications
                  or deletions are subject to an explicit Two-Factor Authentication (2FA) approval process. Such changes will become
                  effective immediately upon your successful completion of the 2FA process.{' '}
                </p>
              </li>
              <li>
                <p className="terms-text">
                  Reconciling your Split payments with your internal accounting records and with your beneficiaries.{' '}
                </p>
              </li>
            </ol>
          </div>
        </div>

        <div className="terms" aria-labelledby="misappropriation-and-funds-disputes">
          <h3 id="misappropriation-and-funds-disputes" className="terms-heading">
            Misappropriation and Funds Disputes
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              You acknowledge that Kora operates strictly as an automated distribution mechanism based on the rules you configure.
              Consequently, Kora is not liable for:
            </p>
            <ol>
              <li>
                <p className="terms-text">Funds routed to incorrect or unauthorized accounts due to your input.</p>
              </li>
              <li>
                <p className="terms-text">
                  Disputes arising between you and your beneficiaries concerning the distribution of funds. You are solely responsible for
                  resolving any such disputes.
                </p>
              </li>
            </ol>
          </div>
        </div>

        <div className="terms" aria-labelledby="feature-limitations">
          <h3 id="feature-limitations" className="terms-heading">
            Feature Limitations
          </h3>
          <div className="terms-content">
            <p className="terms-text">Your use of the Feature is subject to the following limitations:</p>
            <ol>
              <li>
                <p className="terms-text">You may add a maximum of five (5) payment destinations per individual Split Rule.</p>
              </li>
              <li>
                <p className="terms-text">
                  The bank account connection functionality primarily supports major Nigerian banks. While the Kora platform supports
                  transactions in multiple currencies (NGN, USD, GBP, EUR), the application of Split Rules to transactions in these
                  currencies is dependent on the settlement methods available for those currencies.
                </p>
              </li>
              <li>
                <p className="terms-text">
                  All transaction limits, whether per transaction or per day, for the Split Payment Feature are determined by and adhere to
                  the limits established for the specific tier of your merchant account, as further detailed in the Main Terms of Use or
                  other applicable agreements with Kora.
                </p>
              </li>
            </ol>
          </div>
        </div>

        <div className="terms" aria-labelledby="kyc-aml-compliance">
          <h3 id="kyc-aml-compliance" className="terms-heading">
            KYC/AML Compliance
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              While your own connected bank accounts undergo an account name verification process and require 2FA for addition, Kora does
              not perform direct Know Your Customer (KYC) or Anti-Money Laundering (AML) checks on the ultimate beneficiaries of the split
              if they are not your own accounts. You are solely responsible for ensuring that any beneficiaries to whom you route funds
              comply with their own KYC/AML obligations and all applicable local and international regulations, including but not limited to
              the Proceeds of Crime (Money Laundering) and Terrorist Financing Act (Canada) and similar legislation in other relevant
              jurisdictions.
            </p>
          </div>
        </div>

        <div className="terms" aria-labelledby="modification-and-termination">
          <h3 id="modification-and-termination" className="terms-heading">
            Modification and Termination
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              Kora reserves the right to suspend or terminate your access to the Split Payment Feature immediately and without prior notice
              if any misuse, fraud, or violation of these Terms or the Main Terms of Use is detected.
            </p>

            <p className="terms-text">
              Kora reserves the right to update, modify, or deprecate the Split Payment Feature. We will provide you with prior notice of
              any such changes.
            </p>
          </div>
        </div>

        <div className="terms" aria-labelledby="disclaimer-of-warranties">
          <h3 id="disclaimer-of-warranties" className="terms-heading">
            Disclaimer of Warranties
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              THE KORA SPLIT PAYMENT FEATURE IS PROVIDED ON AN “AS IS” AND “AS AVAILABLE” BASIS, WITHOUT ANY WARRANTIES OF ANY KIND, EITHER
              EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR
              NON-INFRINGEMENT. KORA DOES NOT WARRANT THAT THE FEATURE WILL BE UNINTERRUPTED, ERROR-FREE, OR SECURE. FOR A COMPREHENSIVE
              UNDERSTANDING OF DISCLAIMERS AND WARRANTIES APPLICABLE TO ALL KORA SERVICES, PLEASE REFER TO THE RELEVANT SECTIONS WITHIN THE
              MAIN TERMS OF USE.
            </p>
          </div>
        </div>

        <div className="terms" aria-labelledby="limitation-of-liability">
          <h3 id="limitation-of-liability" className="terms-heading">
            Limitation of Liability
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              TO THE FULLEST EXTENT PERMITTED BY LAW, KORA SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR
              PUNITIVE DAMAGES, OR ANY LOSS OF PROFITS OR REVENUES, WHETHER INCURRED DIRECTLY OR INDIRECTLY, OR ANY LOSS OF DATA, USE,
              GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM (A) YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE FEATURE;
              (B) ANY CONDUCT OR CONTENT OF ANY THIRD PARTY ON THE FEATURE; OR (C) UNAUTHORIZED ACCESS, USE, OR ALTERATION OF YOUR
              TRANSMISSIONS OR CONTENT. THIS LIMITATION OF LIABILITY IS SUBJECT TO AND SUPPLEMENTED BY THE LIMITATIONS OF LIABILITY SET
              FORTH IN THE MAIN TERMS OF USE.
            </p>
          </div>
        </div>

        <div className="terms" aria-labelledby="governing-law-and-jurisdiction">
          <h3 id="governing-law-and-jurisdiction" className="terms-heading">
            Governing Law and Jurisdiction
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              These Terms shall be governed by and construed in accordance with the governing law as stipulated in the{' '}
              <strong>Main Terms of Use</strong>. Any disputes arising out of or in connection with these Terms or the Feature shall be
              subject to the exclusive jurisdiction as set forth in the Main Terms of Use
            </p>
          </div>
        </div>

        <div className="terms" aria-labelledby="amendments">
          <h3 id="amendments" className="terms-heading">
            Amendments
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              Kora reserves the right to modify or revise these Terms at any time. We will provide notice of any changes by posting the
              updated Terms on our website or through other appropriate communication channels. Your continued use of the Feature after such
              modifications constitutes your acceptance of the revised Terms. Any amendments to these Terms will also be subject to the
              amendment provisions outlined in the Main Terms of Use.
            </p>
          </div>
        </div>

        <div className="terms" aria-labelledby="entire-agreement">
          <h3 id="entire-agreement" className="terms-heading">
            Entire Agreement
          </h3>
          <div className="terms-content">
            <p className="terms-text">
              These Terms, together with the Main Terms of Use, constitute the entire agreement between you and Kora regarding the Split
              Payment Feature and supersede all prior and contemporaneous understandings, agreements, representations, and warranties, both
              written and oral, regarding the Feature.
            </p>
            <p className="terms-text">
              By using the Split Payment Feature, you acknowledge that you have read, understood, and agree to be bound by these Terms and
              the Main Terms of Use.
            </p>
          </div>
        </div>
      </div>

      <div className="terms-agreement">
        <input
          onChange={e => setState({ acceptTerms: e.target.checked })}
          checked={state.acceptTerms}
          type="checkbox"
          id="terms-agreement"
          data-testid="terms-agreement-checkbox"
        />
        <label htmlFor="terms-agreement">I have read and agree to the Terms and Conditions.</label>
      </div>
    </div>
  );
}
