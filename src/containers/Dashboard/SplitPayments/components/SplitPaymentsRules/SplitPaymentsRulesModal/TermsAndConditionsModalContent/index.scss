.split-payment-modal__terms-and-conditions {
  max-height: 70vh;
  overflow: hidden;
  p {
    color: #414f5f;
  }
  .terms-icon {
    display: block;
    margin: 0 auto;
  }

  .heading {
    color: #292b2c;
    font-weight: 500;
    font-size: 1.3rem;
    text-align: center;
    line-height: 2rem;
    margin: 0.25rem 0 0;
  }

  .subheading {
    font-weight: 300;
    font-size: 1rem;
    text-align: center;
    line-height: 1.5rem;
    margin: 0.1rem 0 0;
  }

  .terms-wrapper {
    background: #f9fbfd;
    border-radius: 8px;
    display: grid;
    gap: 1.5rem;
    padding: 1rem;
    margin: 1rem 0;
    max-height: 35vh;
    overflow-y: auto;

    .terms {
      color: #292b2c;

      .terms-heading {
        font-weight: 500;
        font-size: 0.9rem;
      }

      .terms-content {
        display: grid;
        gap: 1rem;

        ol {
          list-style-type: lower-alpha;
          padding-left: 1rem;
          margin: 0;
          display: grid;
        }

        .terms-text,
        strong {
          font-weight: 400;
          font-size: 0.9rem;
          margin: 0;
        }

        strong {
          font-weight: 500;
        }
      }
    }
  }

  .terms-agreement {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin: 0rem 0 0;

    input[type='checkbox'] {
      width: 12px;
      height: 12px;
      cursor: pointer;
      margin: 3px 0 0;
    }

    label {
      font-size: 0.9rem;
      color: #414f5f;
      cursor: pointer;
    }
  }
}
