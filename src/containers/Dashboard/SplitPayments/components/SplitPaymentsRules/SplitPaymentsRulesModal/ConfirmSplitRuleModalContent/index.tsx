import { capitalize, capitalizeFirst } from '+utils';

import { SplitPaymentModalStateType } from '../../splitPaymentRulesHelpers';

import './index.scss';

export default function ConfirmSplitRuleModalContent({ state }: { state: SplitPaymentModalStateType }) {
  return (
    <div className="confirm-split-rule-modal-content">
      <div className="rules">
        <div className="rules-wrapper">
          <ul>
            <li>
              <span className="key">Split Rule Name</span>
              <span>{capitalize(state.splitPayment.split_name)} Split</span>
            </li>
            <li>
              <span className="key">Split Type</span>
              <span>{capitalize(state.splitPayment.split_type)} Split</span>
            </li>
            <li>
              <span className="key">Reason</span>
              <span>{capitalizeFirst(state.splitPayment.split_description)}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
