import { SplitPaymentAccountsArrayType, SplitType } from '+types';

export type SplitPaymentModalStateType = {
  acceptTerms: boolean;
  error: string;
  createdSplitRef: string;

  splitPayment: {
    currency: string;
    split_name: string;
    split_description: string;
    split_type: SplitType;
    split_accounts?: SplitPaymentAccountsArrayType[];
  };
};

export const getSplitPaymentModalState = (currency: string): SplitPaymentModalStateType => ({
  acceptTerms: false,
  error: '',
  createdSplitRef: '',

  splitPayment: {
    currency,
    split_name: '',
    split_description: '',
    split_type: '' as SplitType
  }
});

export type SplitPaymentModalStatePropsType = {
  state: SplitPaymentModalStateType;
  setState: (state: Partial<SplitPaymentModalStateType>) => void;
};

export const initialSplitState = { bankName: '', accountNumber: '', bankCode: '' };
