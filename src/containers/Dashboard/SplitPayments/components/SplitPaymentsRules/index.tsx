import { useState } from 'react';

import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceholder';
import { useSearchQuery } from '+hooks';
import { SplitPaymentServices } from '+services/split-payment-services';
import { SingleSplitRuleType, SplitSettingsResponseType } from '+types';

import SplitPaymentsRulesModal from './SplitPaymentsRulesModal';
import SplitRulesList from './SplitRulesList';

import './index.scss';

const SplitPaymentsRules = ({ settings }: { settings: SplitSettingsResponseType }) => {
  const searchQuery = useSearchQuery();
  const currency = searchQuery.get('currency') || 'NGN';

  const [modalType, setModalType] = useState('');
  const [configToModify, setConfigToModify] = useState<SingleSplitRuleType | null>(null);

  const { data: rules, isLoading: fetchingRules } = SplitPaymentServices.useGetAllSplitRules({
    params: { currency, limit: 5, page: +searchQuery.get('page') || 1 }
  });

  return (
    <section className="split-payment-rules">
      <div className="__header">
        <div>
          <h5 data-testid="title">Split Rules {rules?.data?.paging.total_items ? `(${rules?.data?.paging.total_items})` : ''}</h5>
          <p className="grey-text" data-testid="subtitle">
            Set up rules to split your transactions among various bank accounts.
          </p>
        </div>
        {settings?.enabled && (
          <button
            onClick={() => setModalType('termsAndConditions')}
            data-testid="open-add-split-rule-modal-btn"
            className="btn header-btn"
            type="button"
          >
            <i className="os-icon os-icon-plus" />
            <span>Add Split Rule</span>
          </button>
        )}
      </div>

      {fetchingRules ? (
        <LoadingPlaceholder content={3} type="text" />
      ) : (
        <SplitRulesList
          enabled={settings?.enabled}
          fetchingRules={fetchingRules}
          rules={rules?.data}
          setConfigToModify={setConfigToModify}
          currency={currency}
          setModalType={setModalType}
        />
      )}

      {modalType && (
        <SplitPaymentsRulesModal
          configToModify={configToModify}
          setConfigToModify={setConfigToModify}
          currency={currency}
          settings={settings}
          modalType={modalType}
          setModalType={setModalType}
          rules={rules?.data?.data}
        />
      )}
    </section>
  );
};

export default SplitPaymentsRules;
