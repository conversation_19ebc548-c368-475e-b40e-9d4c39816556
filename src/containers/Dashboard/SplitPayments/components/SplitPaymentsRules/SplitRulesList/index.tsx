import { Dispatch, SetStateAction, useState } from 'react';
import { Link } from 'react-router-dom';

import Copyable from '+containers/Dashboard/Shared/Copyable';
import EmptyStateComponent from '+containers/Dashboard/Shared/EmptyState';
import Icon from '+containers/Dashboard/Shared/Icons';
import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceholder';
import { useClickOutside } from '+hooks';
import { IPaging, SingleSplitRuleType } from '+types';
import { capitalize, capitalizeFirst, formatAmount } from '+utils';

import SplitRulesPagination from './SplitRulesPagination';

import './index.scss';

type SplitRulesListPropsType = {
  setModalType: Dispatch<SetStateAction<string>>;
  currency: string;
  setConfigToModify: Dispatch<SetStateAction<SingleSplitRuleType | null>>;
  fetchingRules: boolean;
  enabled: boolean;
  rules: {
    data: SingleSplitRuleType[];
    paging: IPaging;
  };
};

const SplitRulesList = ({ setModalType, currency, setConfigToModify, rules, fetchingRules, enabled }: SplitRulesListPropsType) => {
  const [dropdown, setDropdown] = useState('');
  const [openOptions, setOpenOptions] = useState('');

  const options = useClickOutside<HTMLDivElement>(() => setOpenOptions(''), false);

  const openModal = (type: string, config: SingleSplitRuleType) => {
    setConfigToModify(config);
    setModalType(type);
    setOpenOptions('');
  };

  const toggleOptions = (splitReference: string) => {
    setOpenOptions(splitReference);
  };

  return (
    <div className="split-payment-rules-list-wrapper">
      {fetchingRules && <LoadingPlaceholder content={3} type="text" />}
      {rules?.data.length ? (
        <>
          <ul className="split-payment-rules-list">
            {rules?.data.map(rule => {
              const activeDropdown = dropdown === rule.split_reference;
              const accountsExistsInRule = rule.split_accounts?.length;

              return (
                <li data-testid="split-rule" className="rule" key={rule.split_reference}>
                  <div className="rule-heading">
                    <div className="rule-heading__title">
                      A{' '}
                      <span className={`__type ${accountsExistsInRule ? '' : '__warning'}`}>
                        {rule.split_type === 'flat' ? 'fixed' : rule.split_type} split rule
                      </span>{' '}
                      for <span className="__name">{rule.split_name}</span> has been generated{' '}
                      <span className="__id">
                        <Copyable text={rule.split_reference} textModifier={val => (val.length > 10 ? `${val.slice(0, 10)}...` : val)} />
                      </span>
                    </div>
                    <div className="rule-heading__options">
                      {accountsExistsInRule ? (
                        <>
                          <button
                            onClick={() => setDropdown(prev => (prev && prev === rule.split_reference ? '' : rule.split_reference))}
                            className="btn btn-expand"
                            data-testid="btn-expand"
                          >
                            Expand{' '}
                            <Icon
                              name="caretDown"
                              className="caret-down"
                              style={{ transform: activeDropdown ? 'rotate(180deg)' : '' }}
                              stroke="currentColor"
                              width={12}
                              height={12}
                            />
                          </button>
                          {enabled && (
                            <>
                              <hr />
                              <div ref={options} className=" options-group">
                                <button
                                  className="btn"
                                  onClick={e => {
                                    e.stopPropagation();
                                    toggleOptions(rule.split_reference);
                                  }}
                                  data-testid="options-btn"
                                >
                                  <Icon name="threeDots" fill="currentColor" height={16} width={16} />
                                </button>
                                {openOptions === rule.split_reference && (
                                  <div className="options">
                                    <button onClick={() => openModal('rule', rule)} className="btn" data-testid="open-split-rule-btn">
                                      <Icon width={16} height={16} name="pen" /> <span>Edit rule</span>
                                    </button>

                                    <button
                                      onClick={() => openModal('confirmDelete', rule)}
                                      className="btn btn-delete"
                                      data-testid="delete-split-rule-btn"
                                    >
                                      <Icon width={18} height={18} name="trashCan" /> <span>Delete rule</span>
                                    </button>
                                  </div>
                                )}
                              </div>
                            </>
                          )}
                        </>
                      ) : (
                        enabled && (
                          <>
                            <button
                              className="btn add-account-btn"
                              data-testid="add-account-btn"
                              onClick={() => openModal('addAccounts', rule)}
                            >
                              <span className="os-icon os-icon-plus" />
                              Add split account
                            </button>
                            <hr />
                            <div ref={options} className=" options-group">
                              <button
                                onClick={() => openModal('confirmDelete', rule)}
                                className="btn btn-delete-trash"
                                data-testid="delete-split-rule-btn"
                              >
                                <Icon width={18} height={18} name="trashCan" fill="currentColor" style={{ marginTop: -6 }} />
                              </button>
                            </div>
                          </>
                        )
                      )}
                    </div>
                  </div>
                  {activeDropdown && (
                    <>
                      <div className="rule-content">
                        <div className="rule-content__heading">
                          <h6>Account Name</h6>
                          <h6>Bank</h6>
                          <h6>Account Number</h6>
                          <h6>{rule.split_type == 'flat' ? 'Amount' : 'Percentage'}</h6>
                          <i />
                        </div>
                        {rule.split_accounts.map(account => {
                          const destination = account.split_destination;

                          return (
                            <ul key={destination.account_number}>
                              <li>
                                <div className="account-name">
                                  <strong>Account Name: </strong>
                                  <span className="link">{capitalize(destination.account_name ?? '')}</span>
                                </div>
                                <div>
                                  <strong>Bank: </strong>
                                  <span>{capitalize(destination.bank_name ?? '')}</span>
                                </div>
                                <div>
                                  <strong>Account Number: </strong>
                                  <span>
                                    <Copyable text={destination.account_number ?? ''} />
                                  </span>
                                </div>

                                <div>
                                  <strong>{rule.split_type == 'flat' ? 'Amount' : 'Percentage'}: </strong>
                                  <span>
                                    {rule.split_type == 'flat' ? `${currency} ${formatAmount(account.value)}` : `${account.value}%`}
                                  </span>
                                </div>

                                <div>
                                  <Link to={`/dashboard/split-payments/accounts/${destination.reference}`} className="link">
                                    See related payments <i className="os-icon os-icon-arrow-up-right" />
                                  </Link>
                                </div>
                              </li>
                            </ul>
                          );
                        })}
                      </div>
                      <div className="reason-wrapper">
                        <h6>Reason for Split</h6>
                        <p className="reason">{capitalizeFirst(rule.split_description)}</p>
                      </div>
                    </>
                  )}
                </li>
              );
            })}
          </ul>
          <SplitRulesPagination rules={rules} fetchingRules={fetchingRules} />
        </>
      ) : (
        !fetchingRules && (
          <EmptyStateComponent heading="No Payment Rule Yet" message="Your split rules will be displayed here once they are created." />
        )
      )}
    </div>
  );
};

export default SplitRulesList;
