import Icon from '+containers/Dashboard/Shared/Icons';
import { useSearchQuery } from '+hooks';
import { IPaging, SingleSplitRuleType } from '+types';

import './index.scss';

export default function SplitRulesPagination({
  rules,
  fetchingRules
}: {
  fetchingRules: boolean;
  rules: {
    data: SingleSplitRuleType[];
    paging: IPaging;
  };
}) {
  const searchQuery = useSearchQuery<{ page: string }>();
  const page = +searchQuery.get('page') > 0 ? +searchQuery.get('page') : 1;
  const limit = 5;
  const lastPage = Math.ceil(Number(rules?.paging?.total_items) / limit);
  const endOfPage = lastPage === page;

  const gotoPage = (pageNumber: number) => {
    searchQuery.setQuery({ page: String(pageNumber) });
  };

  return (
    rules?.paging?.total_items > limit && (
      <div data-testid="split-rules-pagination" className="split-rules-pagination">
        <span data-testid="number-of-split-rules">
          Page{' '}
          <strong>
            ({page} of {lastPage})
          </strong>{' '}
          Split Rules
        </span>
        <div className="__btn-wrapper">
          <button
            disabled={fetchingRules || page === 1}
            onClick={() => gotoPage(1)}
            aria-label="Go to first page"
            className="btn"
            type="button"
          >
            <Icon name="caretDoubleLeft" stroke="currentColor" width={8} height={8} />
          </button>
          <button
            disabled={fetchingRules || page === 1}
            onClick={() => gotoPage(+page - 1 || 1)}
            aria-label="Go to previous page"
            className="btn"
            type="button"
          >
            <Icon name="caretLeft" stroke="currentColor" width={8} height={8} />
          </button>
          <button
            disabled={fetchingRules || endOfPage}
            onClick={() => gotoPage(rules?.paging?.next)}
            aria-label="Go to next page"
            className="btn"
            type="button"
          >
            <Icon name="caretRight" stroke="currentColor" width={8} height={8} />
          </button>
          <button
            aria-label="Go to last page"
            className="btn"
            type="button"
            disabled={fetchingRules || endOfPage}
            onClick={() => gotoPage(Math.ceil(Number(rules?.paging?.total_items) / limit))}
          >
            <Icon name="caretDoubleRight" stroke="currentColor" width={8} height={8} />
          </button>
        </div>
      </div>
    )
  );
}
