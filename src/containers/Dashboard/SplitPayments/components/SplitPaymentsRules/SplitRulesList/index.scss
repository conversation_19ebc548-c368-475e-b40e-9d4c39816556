.split-payment-rules-list-wrapper {
  background: #f9fbfd;
  border-radius: 10px;
  padding: 1rem;

  .split-payment-rules-list {
    display: grid;
    gap: 1rem;
    list-style: none;

    .rule {
      background: #fff;
      border-radius: 10px;
      padding: 1.5rem;

      &-heading {
        align-items: flex-start;
        display: flex;
        flex-wrap: wrap;
        gap: 1rem 3rem;
        justify-content: space-between;
        line-height: 2rem;

        @media (min-width: 600px) {
          flex-wrap: nowrap;
        }

        &__title {
          flex-shrink: 1;
          overflow-wrap: break-word;
          word-break: break-word;

          .__type,
          .__name,
          .__id {
            background: #e4fff1;
            border-radius: 6px;
            color: #24b314;
            font-weight: 500;
            padding: 0.2rem 0.5rem;
          }

          .__name,
          .__id {
            background: #f3f4f8;
            color: #414f5f;
          }

          .__warning {
            background: #fff8e1;
            color: #915200;
          }
        }
        &__options {
          align-items: center;
          display: flex;

          .btn {
            border: 0;
            color: #2376f3;
            display: inline;
            outline: 0;
            padding: 0;

            &:hover {
              color: #2376f391;
            }

            &:focus {
              border: 0;
              box-shadow: none;
              outline: 0;
            }
          }

          .btn-expand,
          .add-account-btn {
            align-items: center;
            display: flex;
            gap: 0.5rem;
            white-space: nowrap;
          }

          .caret-down {
            transition: all 0.3s ease;
          }

          hr {
            background: #dde2ec;
            height: 15px;
            margin: 0 1rem;
            width: 1px;
          }

          .options-group {
            position: relative;

            .options {
              background: rgb(0, 0, 0);
              border-radius: 8px;
              display: grid;
              right: -50%;
              position: absolute;
              top: 110%;
              z-index: 2;

              .btn {
                align-items: center;
                color: #fff;
                display: flex;

                gap: 0.75rem;
                padding: 1rem;
                width: auto;

                span {
                  white-space: nowrap;
                }
              }

              .btn-delete {
                color: #f32345;
              }
            }

            .btn-delete-trash {
              color: #94a7b7;
              transition: color 0.2s ease-in;

              &:hover {
                color: #f32345;
              }
            }
          }
        }
      }

      .rule-content {
        animation: fadeIn 0.3s ease-in;
        border: 1px solid #dde2ec;
        border-width: 1px 0;
        margin: 1.5rem 0 0;
        padding: 1.5rem 0;

        .rule-content__heading {
          display: none;
        }

        ul {
          display: grid;
          gap: 1rem;
          list-style: none;
          margin: 1rem 0 0;

          li {
            div {
              display: grid;
              grid-template-columns: 1fr 1fr;
              max-width: 450px;
              margin: 0 0 0.5rem;

              .link {
                all: unset;
                color: #2376f3;
                cursor: pointer;
                white-space: nowrap;

                &:hover {
                  opacity: 0.7;
                }
              }
            }

            .account-name .link {
              white-space: unset;
              cursor: default;
              &:hover {
                opacity: 1;
              }
            }
          }
        }
        @media (min-width: 1240px) {
          ul li,
          .rule-content__heading {
            display: grid;
            gap: 0.5rem;
            grid-template-columns: 1fr 1fr 1fr 1fr 1.2fr;
          }

          ul {
            li {
              div {
                display: block;
                margin: 0;

                strong {
                  display: none;
                }
              }
              > *:last-child {
                text-align: right;
              }
            }
          }
        }
      }
      .reason-wrapper {
        animation: fadeIn 0.3s ease-in;
        display: grid;
        gap: 0.25rem;
        margin-top: 2rem;

        p {
          color: inherit;
        }
      }
    }
  }
}
