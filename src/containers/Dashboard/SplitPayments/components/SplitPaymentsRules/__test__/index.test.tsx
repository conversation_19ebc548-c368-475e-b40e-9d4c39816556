import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { http, HttpResponse } from 'msw';

import { mockedSplitRules, mockedSplitSettings } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';
import { SplitSettingsResponseType } from '+types';

import SplitPaymentsRules from '..';

const MockSplitPaymentsRules = ({ settings = mockedSplitSettings.data }: { settings?: SplitSettingsResponseType }) => {
  return (
    <MockIndex>
      <SplitPaymentsRules settings={settings} />
    </MockIndex>
  );
};

describe('Test for split payment configuration component', () => {
  it('renders component correctly', async () => {
    render(<MockSplitPaymentsRules />);

    expect(await screen.findByText(/Split Rules/i)).toBeInTheDocument();
    expect(screen.getByText(/Set up rules to split your transactions among various bank accounts./i)).toBeInTheDocument();
    expect(screen.getByTestId('open-add-split-rule-modal-btn')).toBeInTheDocument();
  });

  it('Does not render add split rule modal button, and modify option icon button if settings are not enabled', async () => {
    render(<MockSplitPaymentsRules settings={{ ...mockedSplitSettings.data, enabled: false }} />);

    expect(await screen.findByText(/Split Rules/i)).toBeInTheDocument();
    expect(screen.queryByTestId('open-add-split-rule-modal-btn')).not.toBeInTheDocument();
    expect(screen.queryByTestId('options-btn')).not.toBeInTheDocument();
  });

  it('Does not render modify option icon button if settings are not enabled', async () => {
    render(<MockSplitPaymentsRules settings={{ ...mockedSplitSettings.data, enabled: false }} />);

    expect(await screen.findByText(/Split Rules/i)).toBeInTheDocument();
    expect(screen.getByText(/Set up rules to split your transactions among various bank accounts./i)).toBeInTheDocument();
    expect(screen.queryByTestId('options-btn')).not.toBeInTheDocument();
  });

  it('Renders a total of 1 split rules which is the total items in the api mocked return array', async () => {
    render(<MockSplitPaymentsRules />);

    expect(await screen.findByText('Split Rules (1)')).toBeInTheDocument();
    expect(screen.getAllByTestId('split-rule')).toHaveLength(1);
  });

  it('Hide pagination if split rules is less than 6', async () => {
    render(<MockSplitPaymentsRules />);

    expect(await screen.findByText('Split Rules (1)')).toBeInTheDocument();
    expect(screen.getAllByTestId('split-rule')).toHaveLength(1);

    expect(screen.queryByTestId('split-rules-pagination')).not.toBeInTheDocument();
  });

  it('Hide pagination if split rules is less than 6', async () => {
    render(<MockSplitPaymentsRules />);

    expect(await screen.findByText('Split Rules (1)')).toBeInTheDocument();
    expect(screen.getAllByTestId('split-rule')).toHaveLength(1);

    expect(screen.queryByTestId('split-rules-pagination')).not.toBeInTheDocument();
  });

  it('Show  pagination if split rules is more than 5', async () => {
    const data = {
      data: {
        ...mockedSplitRules.data,
        paging: {
          ...mockedSplitRules.data.paging,
          total_items: 6
        }
      }
    };

    server.use(http.get('http://localhost:3000/api/split-payments', () => HttpResponse.json(data, { status: 200 })));

    render(<MockSplitPaymentsRules />);

    expect(await screen.findByText('Split Rules (6)')).toBeInTheDocument();
    expect(screen.getAllByTestId('split-rule')).toHaveLength(1);

    expect(screen.getByTestId('split-rules-pagination')).toBeInTheDocument();
  });

  test('toggles dropdown visibility of list item when expand button is clicked', async () => {
    render(<MockSplitPaymentsRules />);

    const user = userEvent.setup();

    const expandButton = await screen.findByTestId('btn-expand');
    expect(expandButton).toBeInTheDocument();

    await user.click(expandButton);
    expect(screen.getAllByText(/Account Name/i)).toHaveLength(2);
    expect(screen.getAllByText(/Bank/i)).toHaveLength(4);
    expect(screen.getAllByText(/Account Number/i)).toHaveLength(2);
    expect(screen.getAllByText(/Percentage/i)).toHaveLength(3);
    expect(screen.getByText(/See related payments/i)).toBeInTheDocument();

    await user.click(expandButton);

    expect(screen.queryByText(/Account Name/i)).not.toBeInTheDocument();
    expect(screen.getAllByText(/Bank/i)).toHaveLength(1);
    expect(screen.queryByText(/Account Number/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/Amount/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/See related payments/i)).not.toBeInTheDocument();
  });

  it('opens the modal for adding a new split rule', async () => {
    render(<MockSplitPaymentsRules />);

    const user = userEvent.setup();

    const openSplitRuleBtn = await screen.findByTestId('open-add-split-rule-modal-btn');
    expect(openSplitRuleBtn).toBeInTheDocument();

    await user.click(openSplitRuleBtn);

    expect(await screen.findByText(/Kora Split Payment Terms and Conditions/i)).toBeInTheDocument();
  });

  it('opens the modal for editing a rule when the edit rule button is clicked', async () => {
    render(<MockSplitPaymentsRules />);

    const user = userEvent.setup();

    const optionsBtn = await screen.findByTestId('options-btn');
    expect(optionsBtn).toBeInTheDocument();

    await user.click(optionsBtn);

    const optionsButton = screen.getByTestId('open-split-rule-btn');
    expect(optionsButton).toBeInTheDocument();

    await user.click(optionsButton);

    expect(await screen.findByText(/Update Split Payment Rule/i)).toBeInTheDocument();
  });

  it('opens the modal for deleting a rule when the delete rule button is clicked', async () => {
    render(<MockSplitPaymentsRules />);

    const user = userEvent.setup();

    const optionsBtn = await screen.findByTestId('options-btn');
    expect(optionsBtn).toBeInTheDocument();

    await user.click(optionsBtn);

    const deleteBtn = screen.getByTestId('delete-split-rule-btn');
    expect(deleteBtn).toBeInTheDocument();

    await user.click(deleteBtn);

    expect(await screen.findAllByText(/Delete split rule?/i));
  });

  it('shows the add split account and delete icon when a split rule has no accounts', async () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { split_accounts, ...mockedSplitRuleWithoutAccounts } = mockedSplitRules.data.data[0];

    const data = {
      data: {
        ...mockedSplitRules.data,
        data: [mockedSplitRuleWithoutAccounts]
      }
    };

    server.use(http.get('http://localhost:3000/api/split-payments', () => HttpResponse.json(data, { status: 200 })));

    render(<MockSplitPaymentsRules />);

    const user = userEvent.setup();

    const optionsBtn = await screen.findByTestId('add-account-btn');
    expect(optionsBtn).toBeInTheDocument();

    const deleteBtn = screen.getByTestId('delete-split-rule-btn');
    expect(deleteBtn).toBeInTheDocument();

    await user.click(deleteBtn);

    expect(await screen.findAllByText(/Delete split rule?/i));
  });
});
