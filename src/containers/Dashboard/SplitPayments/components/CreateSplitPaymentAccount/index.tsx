import { useState } from 'react';

import { useSearchQuery } from '+hooks';
import { SplitPaymentServices } from '+services/split-payment-services';
import { SplitSettingsResponseType } from '+types';

import CreateSplitAccountModal from './CreateSplitAccountModal';

import './index.scss';

export default function CreateSplitPaymentAccount({ settings }: { settings: SplitSettingsResponseType }) {
  const searchQuery = useSearchQuery();
  const currency = searchQuery.get('currency') || 'NGN';
  const [modalType, setModalType] = useState('');

  const { data: accounts } = SplitPaymentServices.useGetSplitAccounts({
    params: { currency }
  });

  return (
    <section className="create-split-payment-account">
      <div className="__header">
        <div>
          <h5 data-testid="title">Split Accounts {accounts?.data.length > 0 ? `(${accounts?.data.length})` : ''}</h5>
          <p className="grey-text" data-testid="subtitle">
            Add bank accounts so that the split rules can effectively distribute the split.
          </p>
        </div>

        {settings?.enabled ? (
          <button
            onClick={() => setModalType('addAccounts')}
            data-testid="open-add-split-account-modal-btn"
            className="btn header-btn"
            type="button"
          >
            <i className="os-icon os-icon-plus" />
            <span>Add Split Account</span>
          </button>
        ) : null}
      </div>

      {modalType && (
        <CreateSplitAccountModal
          accounts={accounts?.data}
          currency={currency}
          settings={settings}
          modalType={modalType}
          setModalType={setModalType}
        />
      )}
    </section>
  );
}
