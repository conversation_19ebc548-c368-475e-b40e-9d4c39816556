export const defaultAccountInfo = { bankName: '', accountNumber: '', bankCode: '', accountName: '', relationship: '', reference: '' };

export const RESEND_TIMEOUT = 15;

export const createSplitAccountModalState = {
  accounts: [] as (typeof defaultAccountInfo)[],
  error: '',
  otp: '',
  otpIdentifier: '',
  otpServiceIdentifier: '',
  resendTimeout: RESEND_TIMEOUT,
  typingNewAccount: false
};

export type CreateSplitAccountModalStateType = typeof createSplitAccountModalState;

export type CreateSplitAccountModalStatePropsType = {
  state: CreateSplitAccountModalStateType;
  setState: (state: Partial<CreateSplitAccountModalStateType>) => void;
};
