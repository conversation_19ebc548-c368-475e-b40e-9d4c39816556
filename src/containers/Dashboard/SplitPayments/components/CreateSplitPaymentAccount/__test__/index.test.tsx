import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { mockedSplitSettings } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { SplitSettingsResponseType } from '+types';

import CreateSplitPaymentAccount from '..';

const MockCreateSplitPaymentAccount = ({ settings = mockedSplitSettings.data }: { settings?: SplitSettingsResponseType }) => {
  return (
    <MockIndex>
      <CreateSplitPaymentAccount settings={settings} />
    </MockIndex>
  );
};

describe('Test for split payment configuration component', () => {
  it('renders component correctly', async () => {
    render(<MockCreateSplitPaymentAccount />);

    expect(await screen.findByText(/Split Accounts/i)).toBeInTheDocument();
    expect(screen.getByText(/Add bank accounts so that the split rules can effectively distribute the split./i)).toBeInTheDocument();
    expect(screen.getByTestId('open-add-split-account-modal-btn')).toBeInTheDocument();
  });

  it('Does not render add split account modal button if settings are not enabled', async () => {
    render(<MockCreateSplitPaymentAccount settings={{ ...mockedSplitSettings.data, enabled: false }} />);

    expect(await screen.findByText(/Split Accounts/i)).toBeInTheDocument();
    expect(screen.queryByTestId('open-add-split-account-modal-btn')).not.toBeInTheDocument();
  });

  it('Renders a total of 3 split accounts which is the total items in the api mocked return array', async () => {
    render(<MockCreateSplitPaymentAccount />);

    expect(await screen.findByText('Split Accounts (3)')).toBeInTheDocument();
  });

  it('opens the modal for adding  new split accounts', async () => {
    render(<MockCreateSplitPaymentAccount />);

    const user = userEvent.setup();

    const openSplitAccountBtn = await screen.findByTestId('open-add-split-account-modal-btn');
    expect(openSplitAccountBtn).toBeInTheDocument();

    await user.click(openSplitAccountBtn);

    expect(
      await within(screen.getByTestId('subtitle')).findByText(
        /Add bank accounts so that the split rules can effectively distribute the split./i
      )
    ).toBeInTheDocument();
  });
});
