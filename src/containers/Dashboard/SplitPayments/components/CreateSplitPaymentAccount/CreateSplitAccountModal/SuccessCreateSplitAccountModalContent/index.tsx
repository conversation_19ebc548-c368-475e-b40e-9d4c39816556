import { Dispatch, SetStateAction } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';

import './index.scss';

const SuccessCreateSplitAccountModalContent = ({
  accountsLength,
  setModalType
}: {
  accountsLength: number;
  setModalType: Dispatch<SetStateAction<string>>;
}) => {
  const isSingle = accountsLength === 1;

  return (
    <div className="success-crate-account-modal-content ">
      <Icon name="circledCheck" height={80} width={80} />
      <h5>
        {accountsLength} Split account{isSingle ? '' : 's'} added
      </h5>
      <p>You can now use {isSingle ? 'this account' : 'these accounts'} in your split payment rules</p>
      <button className="btn" onClick={() => setModalType('')}>
        Dismiss
      </button>
    </div>
  );
};

export default SuccessCreateSplitAccountModalContent;
