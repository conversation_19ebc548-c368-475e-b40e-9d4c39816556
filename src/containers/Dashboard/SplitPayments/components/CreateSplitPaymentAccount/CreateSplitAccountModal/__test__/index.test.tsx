import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Mock } from 'vitest';

import { mockedSplitAccounts } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { CreateSplitAccountResponseType, SplitSettingsResponseType } from '+types';

import SplitCreateSplitAccountModal from '..';

const closeModal = vi.fn();

const paymentSettings = {
  enabled: true,
  split_account: { max_count: 5, max_total_rate: 70, min_settlement_amount: 1000 }
};

const MockSplitCreateSplitAccountModal = ({
  type = '',
  settings = paymentSettings,
  accounts = mockedSplitAccounts.data
}: {
  type?: string;
  settings?: SplitSettingsResponseType;
  accounts?: CreateSplitAccountResponseType[];
}) => (
  <MockIndex>
    <SplitCreateSplitAccountModal accounts={accounts} settings={settings} currency="NGN" modalType={type} setModalType={closeModal} />
  </MockIndex>
);

describe('Test for split payment modals', () => {
  describe('Add Split Account Modal Test', () => {
    it('renders Add Split Account Modal correctly', () => {
      render(<MockSplitCreateSplitAccountModal type="addAccounts" />);

      expect(screen.getByTestId('modal-title')).toHaveTextContent('Add Split Account');
      expect(screen.getAllByText(/Add Split Account/i)).toHaveLength(2);
      expect(screen.getByText(/Select Bank/i)).toBeInTheDocument();
      expect(screen.getByText(/Account Number/i)).toBeInTheDocument();
      expect(screen.getByText(/Relationship to this Account?/i)).toBeInTheDocument();
      expect(screen.getByTestId('account-number-input')).toHaveValue('');
      expect(screen.getByTestId('account-relationship-input')).toHaveValue('');
      expect(screen.getByTestId('add-split-account-btn')).toBeDisabled();
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      expect(screen.getByTestId('modal-first-btn')).not.toBeDisabled();
      expect(screen.getByText(/No split account added yet/i)).toBeInTheDocument();
    });

    it('Closes the modal when the cancel button is clicked', async () => {
      render(<MockSplitCreateSplitAccountModal type="addAccounts" />);

      const user = userEvent.setup();

      await user.click(screen.getByTestId('modal-first-btn'));

      expect(closeModal).toHaveBeenCalled();
    });

    it('selection of a new account number flow', async () => {
      render(<MockSplitCreateSplitAccountModal type="addAccounts" />);

      expect(screen.queryByTestId('split-account-card')).not.toBeInTheDocument();

      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();

      await waitFor(() => expect(screen.getByTestId('bank')).toBeEnabled());
      await userEvent.click(screen.getByText(/Select a bank/i));

      await userEvent.click(screen.getByText(/Access Bank Nigeria/i));
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      expect(screen.getByTestId('add-split-account-btn')).toBeDisabled();

      await userEvent.type(screen.getByTestId('account-number-input'), '**********');
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      expect(screen.getByTestId('add-split-account-btn')).toBeDisabled();

      await userEvent.type(screen.getByTestId('account-relationship-input'), 'Test relationship');
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      await waitFor(() => expect(screen.getByTestId('add-split-account-btn')).toBeEnabled());

      await userEvent.click(screen.getByTestId('add-split-account-btn'));
      await waitFor(() => expect(screen.queryByText(/No split account added yet/i)).not.toBeInTheDocument());

      expect(screen.getByTestId('split-account-card')).toBeInTheDocument();
      expect(screen.getByTestId('split-account-card')).toHaveTextContent('Test');
      expect(screen.getByTestId('split-account-card')).toHaveTextContent('Access Bank Nigeria (**********)');

      await waitFor(() => expect(screen.getByTestId('modal-second-btn')).toBeEnabled());

      await userEvent.click(screen.getByTestId('delete-account-card-btn'));

      await screen.findByText(/No split account added yet/i);
      expect(screen.queryByTestId('split-account-card')).not.toBeInTheDocument();
      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
    });
  });

  describe('Verify Split Account Modal Test', () => {
    vi.mock('../../splitPaymentRulesHelpers', async () => {
      const actual = await vi.importActual('../../splitPaymentRulesHelpers');
      return {
        ...actual,
        RESEND_TIMEOUT: 1,
        createSplitAccountModalState: {
          ...(actual.createSplitAccountModalState as Mock),
          resendTimeout: 1
        }
      };
    });

    const input = () => screen.getByTestId('verification-code-input');
    const resendBtn = () => screen.getByTestId('resend-verification-button');

    it('renders Verify Split Account Modal correctly', () => {
      render(<MockSplitCreateSplitAccountModal type="confirm" />);

      expect(screen.getByText(/Verify your identity/i)).toBeInTheDocument();
      expect(screen.getByText(/enter verification code/i)).toBeInTheDocument();
      expect(input()).toHaveValue('');

      expect(resendBtn()).toHaveTextContent(/Request again in 1s/i);
      expect(resendBtn()).toBeDisabled();
    });

    it('Validates that OTP is sent and button is delayed for 1s', async () => {
      render(<MockSplitCreateSplitAccountModal type="confirm" />);

      expect(resendBtn()).toHaveTextContent(/Request again in 1s/i);
      expect(resendBtn()).toBeDisabled();

      await waitFor(
        () => {
          expect(resendBtn()).toHaveTextContent(/Resend Verification/i);
        },
        { timeout: 2000 }
      );

      expect(resendBtn()).toBeEnabled();

      await userEvent.click(resendBtn());
      expect(resendBtn()).toBeDisabled();

      await waitFor(() => {
        expect(resendBtn()).toHaveTextContent(/Request again in 1s/i);
      });
      expect(resendBtn()).toBeDisabled();

      await waitFor(
        () => {
          expect(resendBtn()).toHaveTextContent(/Resend Verification/i);
        },
        { timeout: 3000 }
      );

      expect(resendBtn()).toBeEnabled();

      expect(screen.getByTestId('modal-second-btn')).toBeDisabled();
      await userEvent.type(input(), '1234567');
      expect(screen.getByTestId('modal-second-btn')).toBeEnabled();
    });
  });
});
