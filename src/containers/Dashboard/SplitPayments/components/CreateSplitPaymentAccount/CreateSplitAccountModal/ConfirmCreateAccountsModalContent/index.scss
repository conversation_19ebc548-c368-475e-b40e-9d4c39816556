.confirm-create-account-modal-content {
  overflow: hidden;
  .success-icon {
    display: block;
    margin: 0 auto;
    width: 70px;
    height: 70px;
  }

  .heading {
    font-size: 1.5rem;
    font-weight: 500;
    text-align: center;
    margin: 0.5rem 0 0;
  }

  .subheading {
    color: #414f5f;
    font-size: 1rem;
    text-align: center;
    margin: 0.5rem 0 0;
  }
  .accounts {
    max-height: 230px;
    overflow-y: auto;
    background: #f3f4f8;
    border-radius: 8px;
    margin: 1.5rem 0 0;
    padding: 1rem 0;

    &-wrapper {
      margin-top: 1rem;
      max-height: 300px;
      overflow-y: auto;

      ul {
        display: grid;
        gap: 1rem;
        list-style: none;
        margin: 0;
        padding: 0;

        li {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          font-weight: 500;
          justify-content: space-between;
          padding: 0 1rem;

          .key {
            color: #94a7b7;
          }
        }
      }

      hr {
        margin: 1rem;
      }
    }
  }

  .verification-wrapper {
    display: grid;
    gap: 0.25rem;
    margin: 1rem 0 0;

    .__label {
      font-size: 0.9rem;
      font-weight: 500;
    }

    .__input {
      border: 2px solid #dde2ec;
      box-shadow: none;
      padding: 0.5rem;
      font-size: 0.9rem;
      color: inherit;
      text-align: center;

      &:focus {
        border-color: #2376f3;
      }
    }
    .btn {
      margin: 0.5rem auto;
      display: inline;
      padding: 0;
      font-size: 0.9rem;
      font-weight: 500;
      color: #2376f3;

      &.timeout {
        color: #94a7b7;
        cursor: not-allowed;
      }
    }
    .error,
    .verified-account-name {
      display: flex;
      font-size: 0.9rem;
      letter-spacing: 0.03rem;
    }

    .verified-account-name {
      align-items: center;

      gap: 0.25rem;
    }
  }
}
