import { Fragment } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import useStore from '+store';
import { capitalize, capitalizeFirst, stripNonNumeric } from '+utils';

import { CreateSplitAccountModalStateType } from '../../splitPaymentRulesHelpers';

import './index.scss';

export default function ConfirmCreateAccountsModalContent({
  state,
  setState,
  sendOtpFn,
  sendingOtp
}: {
  state: CreateSplitAccountModalStateType;
  setState: (state: Partial<CreateSplitAccountModalStateType>) => void;
  sendOtpFn: () => Promise<void>;
  sendingOtp: boolean;
}) {
  const { profile } = useStore();

  const otpResend = async () => {
    await sendOtpFn();
  };

  const resendBtnText = state.resendTimeout > 0 ? `Request again in ${state.resendTimeout}s` : 'Resend Verification';

  const userEmail = profile?.email ? profile?.email.slice(0, 3) + '****' + profile?.email.slice(profile?.email.indexOf('@')) : 'your email';

  return (
    <div className="confirm-create-account-modal-content">
      <Icon name="circledCheck" className="success-icon" />
      <h1 className="heading">Verify Your Identity</h1>
      <p className="subheading">We&apos;ve sent a verification code to {userEmail}</p>
      <div className="accounts">
        <div className="accounts-wrapper">
          {state.accounts.map((account, i) => (
            <Fragment key={account.accountNumber}>
              <ul>
                <li>
                  <span className="key">Account Name</span>
                  <span>{capitalize(account.accountName)}</span>
                </li>
                <li>
                  <span className="key">Bank Name</span>
                  <span>{capitalize(account.bankName)}</span>
                </li>
                <li>
                  <span className="key">Account Number</span>
                  <span>{capitalizeFirst(account.accountNumber)}</span>
                </li>
                <li>
                  <span className="key">Description</span>
                  <span>{capitalizeFirst(account.bankCode)}</span>
                </li>
              </ul>
              {i < state.accounts.length - 1 && <hr />}
            </Fragment>
          ))}
        </div>
      </div>

      <div className="verification-wrapper">
        <span className="__label">Enter Verification Code</span>
        <input
          autoFocus
          maxLength={7}
          onChange={e => setState({ otp: stripNonNumeric(e.target.value) })}
          disabled={sendingOtp}
          value={state.otp}
          data-testid="verification-code-input"
          placeholder="Enter the 7-digit code"
          type="text"
          className="__input"
        />

        <button
          className={`btn ${state.resendTimeout > 0 ? 'timeout' : ''}`}
          disabled={sendingOtp || state.resendTimeout > 0}
          onClick={() => void otpResend()}
          data-testid="resend-verification-button"
        >
          {sendingOtp ? <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" /> : resendBtnText}
        </button>
      </div>
    </div>
  );
}
