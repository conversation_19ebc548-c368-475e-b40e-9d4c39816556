import { Dispatch } from 'react';

import ListDropdown from '+containers/Dashboard/Shared/ListDropdown';
import SplitPaymentInfoMessage from '+containers/Dashboard/SplitPayments/components/SplitPaymentInfoMessage';
import { CreateSplitAccountResponseType, IListItem } from '+types';
import { capitalizeFirst, stripNonNumeric } from '+utils';

import { CreateSplitAccountModalStateType } from '../../splitPaymentRulesHelpers';
import useAddNewSplitAccountsModalContent from './useAddNewSplitAccountsModalContent';

import imgEmptyState from '+assets/img/dashboard/empty-state.png';
import deleteIcon from '+assets/img/dashboard/delete.svg';

import './index.scss';

export default function AddNewSplitAccountsModalContent({
  accounts = [],
  currency,
  state,
  setState
}: {
  currency: string;
  accounts?: CreateSplitAccountResponseType[];

  state: CreateSplitAccountModalStateType;
  setState: Dispatch<Partial<CreateSplitAccountModalStateType>>;
}) {
  const {
    accountInfo,
    setAccountInfo,
    verifyingBankInfo,
    openBankList,
    banks,
    setOpenBankList,
    maxAccountsAdded,
    disableSaveBtnFn,
    addSplitAccountFn,
    handleDeleteSplit,
    creatingAccount
  } = useAddNewSplitAccountsModalContent({
    accounts,
    currency,
    state,
    setState
  });

  return (
    <div className="add-new-split-account-modal-content">
      <SplitPaymentInfoMessage message="You can add up to 5 bank accounts at a time" />

      <div className="add-account">
        <div className="select-bank-wrapper">
          <span className="__label">Select Bank</span>
          <ListDropdown
            value={{ code: accountInfo.bankCode, name: accountInfo.bankName }}
            setValue={value => {
              setAccountInfo(prev => ({
                ...prev,
                bankCode: (value as IListItem).code,
                bankName: (value as IListItem).name
              }));
            }}
            disabled={verifyingBankInfo || !(banks?.data as unknown as IListItem[])?.length || maxAccountsAdded || creatingAccount}
            type="bank"
            active={openBankList}
            list={banks?.data as unknown as IListItem[]}
            className="bank-list-dropdown"
            onBlur={null}
            setActive={open => setOpenBankList(open)}
          />
        </div>

        <div className="account-number-wrapper">
          <span className="__label">Account Number</span>
          <input
            maxLength={10}
            onChange={e => setAccountInfo(prev => ({ ...prev, accountNumber: stripNonNumeric(e.target.value) }))}
            disabled={verifyingBankInfo || maxAccountsAdded || creatingAccount}
            value={accountInfo.accountNumber}
            data-testid="account-number-input"
            placeholder="Account Number"
            type="text"
            className="__input"
          />
          {state.error && (
            <span className="error" data-testid="error">
              {state.error}
            </span>
          )}
          {(accountInfo.accountName || verifyingBankInfo) && (
            <small className="verified-account-name">
              {verifyingBankInfo ? (
                <>
                  <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" />
                  Fetching account name...
                </>
              ) : (
                <strong>{accountInfo.accountName.toUpperCase()}</strong>
              )}
            </small>
          )}
        </div>
        <div className="account-relationship-wrapper">
          <span className="__label">Relationship to this Account?</span>

          <input
            maxLength={40}
            onChange={e => setAccountInfo(prev => ({ ...prev, relationship: e.target.value }))}
            value={capitalizeFirst(accountInfo.relationship)}
            data-testid="account-relationship-input"
            placeholder="E.g VAT account"
            type="text"
            className="__input"
            disabled={maxAccountsAdded || creatingAccount}
          />
        </div>

        <button
          className="add-split-account-btn"
          data-testid="add-split-account-btn"
          disabled={disableSaveBtnFn()}
          onClick={() => void addSplitAccountFn()}
        >
          {creatingAccount ? (
            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" />
          ) : (
            <>
              <span className="os-icon os-icon-plus" />
              <span> Add Split Account</span>
            </>
          )}
        </button>
      </div>

      <div className="accounts-list">
        {!state.accounts.length && (
          <div className="empty-split-placeholder">
            <img alt="Nothing" src={imgEmptyState} loading="lazy" /> <p>No split account added yet</p>
          </div>
        )}

        {state.accounts.map(acc => {
          return (
            <div data-testid="split-account-card" className="account-card" key={acc.accountNumber}>
              <div className="account-info">
                <strong>{acc?.accountName}</strong>
                <span>
                  {acc.bankName} ({acc.accountNumber})
                </span>
              </div>

              <button data-testid="delete-account-card-btn" onClick={() => handleDeleteSplit(acc.accountName)} className="btn">
                <img src={deleteIcon} alt="delete" />
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
}
