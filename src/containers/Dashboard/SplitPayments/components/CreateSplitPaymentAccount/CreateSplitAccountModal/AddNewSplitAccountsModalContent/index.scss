@mixin verticalSplit {
  hr {
    background: #dde2ec;
    height: 16px;
    margin: 0 1rem;
    width: 1px;
  }
}

.add-new-split-account-modal-content {
  margin: 0 0 2.5rem;

  .add-account {
    margin: 1rem 0 0;
    padding: 0 0 1rem;

    .select-bank-wrapper,
    .account-number-wrapper,
    .account-relationship-wrapper {
      display: grid;
      gap: 0.25rem;
      margin: 1rem 0 0;

      .__label {
        font-size: 0.9rem;
        font-weight: 500;
      }

      .__input {
        border: 2px solid #dde2ec;
        box-shadow: none;
        padding: 0.5rem;
        font-size: 0.9rem;

        &:focus {
          border-color: #2376f3;
        }
      }
    }

    .select-bank-wrapper {
      position: relative;

      .bank-list {
        display: block;
        border-radius: 6px;
        flex-shrink: 0;

        &-dropdown {
          align-items: center;
          display: flex;
          gap: 0.5rem;
          padding: 0.4rem;
          height: 100%;
          width: 100%;

          span {
            margin: 0;
            white-space: nowrap;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .add-split-account-btn {
      align-items: center;
      display: flex;
      gap: 0.5rem;
      font-size: 0.9rem;
      margin: 0.5rem 0 0;
      color: #2376f3;
      border: 0;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
      &:hover {
        opacity: 0.7;
      }
    }

    .error,
    .verified-account-name {
      display: flex;
      font-size: 0.9rem;
      letter-spacing: 0.03rem;
      margin: 0.3rem 0 0;
    }

    .verified-account-name {
      align-items: center;
      gap: 0.25rem;

      strong {
        font-weight: 500;
      }
    }

    .error {
      color: #f32345;
    }
  }

  .accounts-list {
    border-top: 1px solid #dde2ec;
    margin: 1rem 0 0;
    padding: 0.75rem 0 0;

    .empty-split-placeholder {
      background: #f9fbfd;
      align-items: center;
      display: flex;
      padding: 1rem;
      border-radius: 8px;
      justify-content: center;
      margin: 1rem 0;
      gap: 0.5rem;

      img {
        width: 40px;
      }

      p {
        color: #414f5f;
        margin: 0;
      }
    }

    .account-card {
      animation: fadeIn 0.3s ease-in;
      background: #f9fbfd;
      border-radius: 10px;
      display: flex;
      font-size: 0.9rem;
      gap: 0.1rem 2rem;
      justify-content: space-between;
      margin: 1rem 0 0;
      padding: 0.75rem;

      .account-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .btn {
        padding: 0;
      }
    }
  }
}
