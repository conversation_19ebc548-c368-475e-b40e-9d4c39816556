import { Dispatch, useEffect, useState } from 'react';

import { useBanks } from '+hooks';
import { SplitPaymentServices } from '+services/split-payment-services';
import { UtilServices } from '+services/util-services';
import { CreateSplitAccountResponseType, FetchBanksResponseType, IResponse } from '+types';

import { CreateSplitAccountModalStateType, defaultAccountInfo } from '../../splitPaymentRulesHelpers';

export default function useAddNewSplitAccountsModalContent({
  accounts = [],
  currency,
  state,
  setState
}: {
  currency: string;
  accounts?: CreateSplitAccountResponseType[];

  state: CreateSplitAccountModalStateType;
  setState: Dispatch<Partial<CreateSplitAccountModalStateType>>;
}) {
  const { data: banks } = useBanks(currency) as { data: IResponse<FetchBanksResponseType[]> };

  const [openBankList, setOpenBankList] = useState(false);
  const [accountInfo, setAccountInfo] = useState(defaultAccountInfo);

  const maxAccountsAdded = state.accounts.length === 5;

  const handleDeleteSplit = (name: string) => {
    state.accounts = state.accounts.filter(acc => acc.accountName !== name);
    setState({ accounts: state.accounts });
  };

  const { mutateAsync: verifyBankInfo, isLoading: verifyingBankInfo } = UtilServices.useBankEnquiry({
    bannerLevel: true,
    hasFeedbackTimeout: true,
    showSuccessMessage: false,
    showErrorMessage: false,
    onSuccess: data => {
      const accountName = (data as { data: { account_name: string } })?.data.account_name;

      setAccountInfo(prev => ({
        ...prev,
        accountName: accountName
      }));
    },
    onError: () => setState({ error: 'Account not found' })
  });

  const { mutateAsync: createSplitAccountFn, isPending: creatingAccount } = SplitPaymentServices.useCreateSplitAccount({
    onSuccess: data => {
      setState({ accounts: [{ ...accountInfo, reference: data?.data.reference }, ...state.accounts] });
      setAccountInfo(defaultAccountInfo);
    }
  });

  const verifyBankInfoFn = async () => {
    await verifyBankInfo({
      currency,
      account: accountInfo.accountNumber ?? '',
      bank: accountInfo.bankCode ?? ''
    });
  };

  const addSplitAccountFn = async () => {
    await createSplitAccountFn({
      currency,
      account_number: accountInfo.accountNumber,
      bank_code: accountInfo.bankCode,
      relationship: accountInfo.relationship
    });
  };

  const disableSaveBtnFn = () => {
    return (
      !!Object.entries(accountInfo).filter(([key, val]) => key !== 'reference' && !val).length ||
      !!state.error ||
      accountInfo.accountNumber?.length !== 10 ||
      verifyingBankInfo ||
      creatingAccount
    );
  };

  useEffect(() => {
    setState({ error: '', typingNewAccount: !!(accountInfo.accountNumber || accountInfo.bankName || accountInfo.relationship) });

    setAccountInfo(prev => ({
      ...prev,
      accountName: ''
    }));

    const accountNumbers = accounts.map(({ account_number }) => account_number);

    if (accountNumbers.includes(accountInfo.accountNumber)) {
      setState({ error: 'Bank account has been added previously' });
      return;
    }

    if (state.accounts.map(acc => acc.accountNumber).includes(accountInfo.accountNumber)) {
      setState({ error: 'Bank account already in the list' });
      return;
    }

    if (accountInfo.accountNumber.length === 10 && accountInfo.bankName) {
      void verifyBankInfoFn();
    }
  }, [accountInfo.accountNumber, accountInfo.bankName]);

  return {
    accountInfo,
    setAccountInfo,
    verifyingBankInfo,
    openBankList,
    banks,
    setOpenBankList,
    maxAccountsAdded,
    disableSaveBtnFn,
    addSplitAccountFn,
    handleDeleteSplit,
    creatingAccount
  };
}
