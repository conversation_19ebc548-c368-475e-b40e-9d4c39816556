import { Dispatch, SetStateAction, useEffect } from 'react';

import Modal from '+containers/Shared/Modal';
import { useReducerState } from '+hooks';
import { SplitPaymentServices } from '+services/split-payment-services';
import { CreateSplitAccountResponseType, SplitSettingsResponseType } from '+types';

import { createSplitAccountModalState, RESEND_TIMEOUT } from '../splitPaymentRulesHelpers';
import AddNewSplitAccountsModalContent from './AddNewSplitAccountsModalContent';
import ConfirmCreateAccountsModalContent from './ConfirmCreateAccountsModalContent';
import SuccessCreateSplitAccountModalContent from './SuccessCreateSplitAccountModalContent';

import './index.scss';

const SplitPaymentModal = ({
  modalType,
  setModalType,
  currency,
  accounts
}: {
  currency: string;
  settings: SplitSettingsResponseType;
  modalType: string;
  setModalType: Dispatch<SetStateAction<string>>;
  accounts: CreateSplitAccountResponseType[];
}) => {
  const [state, setState] = useReducerState(createSplitAccountModalState);

  const { mutateAsync: sendOtp, isLoading: sendingOtp } = SplitPaymentServices.useSendOtpToCreateSplitAccount({
    onSuccess: data => {
      setState({
        otp: '',
        resendTimeout: RESEND_TIMEOUT,
        otpIdentifier: data.data.auth_data.identifier,
        otpServiceIdentifier: data.data.auth_data.service_identifier
      });
    }
  });

  const { mutateAsync: verifyOtp, isLoading: verifyingOtp } = SplitPaymentServices.useVerifyOtpToCreateSplitAccount({
    onSuccess: () => {
      setModalType('success');
    }
  });

  const sendOtpFn = async () => {
    await sendOtp({
      references: state.accounts.map(account => account.reference)
    });
  };

  useEffect(() => {
    if (state.resendTimeout) {
      const timer = setInterval(() => {
        setState({ resendTimeout: state.resendTimeout - 1 });
      }, 1000);

      if (state.resendTimeout <= 0) {
        clearInterval(timer);
      }

      return () => clearInterval(timer);
    }
  }, [state.resendTimeout]);

  const modal = {
    addAccounts: {
      heading: 'Add Split Account',
      description: `Add bank accounts so that the split rules can effectively distribute the split.`,
      content: <AddNewSplitAccountsModalContent state={state} setState={setState} accounts={accounts} currency={currency} />,
      firstButtonText: 'Cancel',
      secondButtonText: `Continue`,
      secondButtonAction: async () => {
        if (!state.resendTimeout) await sendOtpFn();
        setModalType('confirm');
      },
      secondButtonDisable: !state.accounts.length || !!state.error || state.typingNewAccount
    },

    confirm: {
      content: <ConfirmCreateAccountsModalContent sendOtpFn={sendOtpFn} state={state} sendingOtp={sendingOtp} setState={setState} />,
      firstButtonText: 'Go Back',
      size: 'md',
      firstButtonAction: () => setModalType('addAccounts'),
      secondButtonText: `Verify Account${state.accounts.length > 1 ? 's' : ''}`,
      secondButtonAction: async () => {
        await verifyOtp({
          otp: state.otp,
          identifier: state.otpIdentifier,
          service_identifier: state.otpServiceIdentifier
        });
      },
      secondButtonDisable: verifyingOtp || state.otp.length < 7 || !!state.error
    },
    success: {
      description: <SuccessCreateSplitAccountModalContent accountsLength={state.accounts.length} setModalType={setModalType} />,
      showButtons: false,
      size: 'sm'
    }
  }[modalType];

  return (
    <section className="split-payment-modal">
      <Modal
        close={() => setModalType('')}
        secondButtonDisable={modal?.secondButtonDisable}
        heading={modal?.heading}
        description={modal?.description}
        content={modal?.content}
        firstButtonText={modal?.firstButtonText}
        firstButtonAction={modal?.firstButtonAction}
        secondButtonText={modal?.secondButtonText}
        secondButtonAction={modal?.secondButtonAction}
        secondButtonActionIsTerminal={false}
        showButtons={modal?.showButtons}
        size={modal?.size}
      />
    </section>
  );
};

export default SplitPaymentModal;
