import { ReactNode } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';

import './index.scss';

export default function SplitPaymentInfoMessage({ message }: { message: ReactNode }) {
  return (
    <div className="split-payment-info-message">
      <Icon name="infoIcon" fill="#FA9500" width={22} height={22} />
      {typeof message === 'string' ? <p>{message}</p> : message}
    </div>
  );
}
