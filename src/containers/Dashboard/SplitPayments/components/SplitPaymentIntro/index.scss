.split-payment-intro {
  &-btn {
    margin: -3px 0 0;
    padding: 0;
    text-decoration: underline;
  }
  .stm-content .stm-content-body .stm-text {
    width: 80%;
    margin: 0 0 1rem 0;
  }
  .heading {
    display: grid;
    margin: 0 0 1.25rem;

    h1 {
      font-size: 24px;
      font-weight: 500;
    }
  }

  .stm-content .stm-image {
    margin: 2rem 0 0;
  }

  .stm-content .stm-content-body .stm-text p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .create-split-rule-description {
    ul {
      margin: 0;
    }
  }
}
