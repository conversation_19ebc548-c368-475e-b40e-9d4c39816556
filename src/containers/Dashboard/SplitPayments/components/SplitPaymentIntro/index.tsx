import { useEffect, useState } from 'react';

import SecurityTipModal from '+containers/Dashboard/Shared/SecurityTipModal';
import useStore from '+store';

import createImg from '+assets/img/dashboard/splitSettlementHints/create.svg';
import destinationsImg from '+assets/img/dashboard/splitSettlementHints/destinations.svg';
import secureImg from '+assets/img/dashboard/splitSettlementHints/secure.svg';
import welcomeImg from '+assets/img/dashboard/splitSettlementHints/welcome.svg';

import './index.scss';

export default function SplitPaymentIntro({ enabled }: { enabled: boolean }) {
  const [modalType, setModalType] = useState(false);
  const { profile } = useStore();
  const identifier = profile?.email ? [...profile.email].filter((_, i) => (i + 1) % 3 === 0).join('') : 'User';

  const walkthroughCompleted = () => {
    localStorage.setItem(identifier, 'true');
    setModalType(false);
  };

  useEffect(() => {
    if (!localStorage.getItem(identifier) && enabled) {
      setTimeout(() => setModalType(true), 1000);
    }
  }, []);

  return (
    <>
      <button onClick={() => setModalType(true)} className="split-payment-intro-btn btn btn-link">
        See how it works
      </button>
      <section className="split-payment-intro">
        <SecurityTipModal
          visible={modalType}
          hasFirstButton
          secondButtonFinalText="Done"
          removeIconOnFinalStep
          close={() => setModalType(false)}
          onSubmit={walkthroughCompleted}
          contentHeight="250px"
          size="md"
          data={[
            {
              title: (
                <div className="heading">
                  <h1>Welcome to Split Payments</h1>
                  <p>Take Control of Your Settlements</p>
                </div>
              ),
              description:
                'Learn how to automatically split incoming payments across multiple accounts - perfect for managing VAT, commissions or business finances',
              image: welcomeImg
            },
            {
              title: (
                <div className="heading">
                  <h1>Create Your Split Rule</h1>
                  <p>Define the rules for how your payments are shared</p>
                </div>
              ),
              description: (
                <div className="create-split-rule-description">
                  <p>Choose how you&apos;d like your money to be split:</p>
                  <ul>
                    <li>
                      <p>• Fixed Amount (e.g., ₦500 per payment)</p>
                    </li>
                    <li>
                      <p>• Percentage (e.g., 20% of each transaction)</p>
                    </li>
                  </ul>
                  <p>Then name your rule and add the reason for the split to help you stay organized.</p>
                </div>
              ),
              image: createImg
            },
            {
              title: (
                <div className="heading">
                  <h1>Add Settlement Destinations</h1>
                  <p>Connect Your Bank Accounts</p>
                </div>
              ),
              description:
                'Add the bank accounts you want to receive each part of the payment. You can include more than one account, define the relationship and decide how much goes to each one.',
              image: destinationsImg
            },
            {
              title: (
                <div className="heading">
                  <h1>Secure Your Setup</h1>
                  <p>Two-Factor Authentication Required</p>
                </div>
              ),
              description:
                "For your protection, we'll send you a 6-digit code to verify your identity. This step is required anytime you add a new account, helping us keep your split setup secure.",
              image: secureImg
            },

            {
              title: (
                <div className="heading">
                  <h1>You&apos;re All Set!</h1>
                  <p>Understanding Fallbacks</p>
                </div>
              ),
              description:
                "Your split rules are now active. If a split ever fails, we'll automatically send the full amount to your primary account, so your money is never held up. You can always monitor and adjust your rules anytime from your dashboard.",
              image: welcomeImg
            }
          ]}
        />
      </section>
    </>
  );
}
