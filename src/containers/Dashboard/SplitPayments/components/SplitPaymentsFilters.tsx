import { CSSProperties, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import dayjs from 'dayjs';

import { useReducerState, useSearchQuery } from '+hooks';
import { cleanInput, filteredOutObjectProperty, stripNonNumeric } from '+utils';

import arrowRight from '+assets/img/dashboard/arrow-right.svg';
import calendar from '+assets/img/dashboard/calendar.svg';
import search from '+assets/img/dashboard/search-thin.svg';

import 'react-datepicker/dist/react-datepicker.css';
import '../index.scss';

import { useParams } from 'react-router-dom';

const refundsStatusOption = [
  { value: 'processing', label: 'Processing' },
  { value: 'success', label: 'Success' }
];

const initialState = {
  status: [] as string[],
  amount: '',
  clearFilter: false,
  dateSettledFrom: '',
  dateSettledTo: '',
  accountNumber: '',
  settlementReference: '',
  reference: ''
};

const showClearButtonKeys = Object.keys(initialState);

interface ISplitPaymentFilterProps {
  totalCount?: number;
  activeTab: string;
}

const SplitPaymentFilter = ({ activeTab, totalCount = 0 }: ISplitPaymentFilterProps) => {
  const { id } = useParams<{ id: string }>();
  const searchQuery = useSearchQuery<{
    [key in
      | 'amount'
      | 'reference'
      | 'dateSettledFrom'
      | 'dateSettledTo'
      | 'status'
      | 'accountNumber'
      | 'settlementReference'
      | 'tab']: string;
  }>();

  const [state, setState] = useReducerState({ ...initialState, settlementReference: searchQuery.get('settlementReference') ?? '' });

  const isTrxFilter = activeTab === 'Payouts';

  const handleClearFilter = () => {
    setState(initialState);
    searchQuery.clearAll(['page', 'accountReference']);
  };

  const filterFn = () => {
    const filterKeys = filteredOutObjectProperty(state, ['clearFilter']);
    let result = {};

    const previousKeyword = Object.keys(filterKeys).find(key => Object.keys(searchQuery.value).includes(key));
    if (previousKeyword && previousKeyword in filterKeys) {
      result = { ...filterKeys, ...searchQuery?.value, [previousKeyword]: filterKeys[previousKeyword] as string };
    } else {
      result = { ...searchQuery?.value, ...filterKeys };
    }

    return searchQuery.setQuery({ ...result }, true);
  };

  useEffect(() => {
    const filterKeys = Object.keys(searchQuery.value);
    const showKeys = showClearButtonKeys.some(key => filterKeys.indexOf(key) !== -1);

    if (filterKeys.length > 0 && showKeys) {
      setState({
        clearFilter: true,
        amount: searchQuery.value.amount ?? '',
        reference: searchQuery.value.reference ?? '',
        dateSettledFrom: searchQuery.value.dateSettledFrom ?? '',
        dateSettledTo: searchQuery.value.dateSettledTo ?? '',
        status: typeof searchQuery.value.status === 'string' ? [searchQuery.value.status] : searchQuery.value.status || []
      });
    } else {
      setState({ ...initialState, clearFilter: false });
    }
  }, [searchQuery.value]);

  return (
    <section className="settlement-filter" data-testid="dispute-filter-wrapper">
      <div className="settlement-filter__top">
        <div>
          {activeTab === 'Payouts' && (
            <span>
              {activeTab} ({totalCount || 0}) {state.clearFilter && '(filtered results)'}
            </span>
          )}
          {state.clearFilter && (
            <>
              <span className="divider-sm" />
              <button type="button" data-testid="clear-btn" onClick={handleClearFilter}>
                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none" viewBox="0 0 21 20" style={{ width: '1rem' }}>
                  <path
                    fill="#AABDCE"
                    d="M3.426 2.926c3.902-3.9 10.247-3.9 14.149 0 3.9 3.901 3.9 10.248 0 14.15A9.976 9.976 0 0110.5 20a9.975 9.975 0 01-7.074-2.924c-3.901-3.902-3.901-10.249 0-14.15zM6.374 12.95a.833.833 0 101.179 1.178L10.5 11.18l2.946 2.948a.835.835 0 001.18-1.18l-2.947-2.946 2.947-2.948a.833.833 0 10-1.179-1.179L10.5 8.822 7.553 5.874a.833.833 0 10-1.18 1.18L9.322 10l-2.947 2.948z"
                  />
                </svg>
                &nbsp;
                <span>Clear</span>
              </button>
            </>
          )}
        </div>
      </div>

      <div className="settlement-filter__search-w filter-section">
        <div className="element-search-content filter-body w-100">
          {isTrxFilter && (
            <>
              <div className="split-payments-status-field form-group filter-object filter-object-sm w-auto --no-max-width mr-0">
                <select
                  name="status"
                  className="form-control filter-object-sm mr-0"
                  value={state.status?.[0]}
                  aria-label="status"
                  style={{ border: '.0938rem solid #EAF2FE' }}
                  onChange={e => setState({ status: [e.target.value] })}
                >
                  <option value="">All Statuses</option>
                  {refundsStatusOption.map(item => (
                    <option key={item.label} value={item.value}>
                      {item.label}
                    </option>
                  ))}
                </select>
              </div>
              <div
                className="form-group filter-object d-flex filter-object-sm w-auto mr-0"
                style={{ '--calendar-image': `url("${calendar}")`, minWidth: '300px' } as CSSProperties}
              >
                <>
                  <DatePicker
                    selected={state.dateSettledFrom ? new Date(state.dateSettledFrom) : undefined}
                    dateFormat="dd-MM-yyyy"
                    popperPlacement="bottom-end"
                    className="form-control date-select pl-4 date-picker"
                    onChange={date => setState({ dateSettledFrom: dayjs(dayjs(date)).format('YYYY-MM-DD') })}
                    maxDate={new Date()}
                    placeholderText="From"
                    calendarClassName="custom-datepicker"
                    data-testid="start-date"
                  />
                  <DatePicker
                    selected={state.dateSettledTo ? new Date(state.dateSettledTo) : undefined}
                    dateFormat="dd-MM-yyyy"
                    popperPlacement="bottom-end"
                    className="form-control date-select pl-4 date-picker"
                    minDate={state.dateSettledFrom ? new Date(state.dateSettledFrom) : undefined}
                    maxDate={new Date()}
                    onChange={date => setState({ dateSettledTo: dayjs(dayjs(date)).format('YYYY-MM-DD') })}
                    placeholderText="To"
                    calendarClassName="custom-datepicker"
                    data-testid="end-date"
                  />
                </>
              </div>
              <div className="form-group position-relative filter-object flex-grow-1 w-auto --no-max-width --search-container mr-0">
                <img src={search} alt="search icon" aria-hidden />
                <input
                  type="search"
                  className="form-control"
                  placeholder="Search Split Payout ID..."
                  value={state.reference}
                  onChange={e => setState({ reference: cleanInput(e.target.value) })}
                  style={{ border: '1.5px solid #EAF2FE' }}
                  data-testid="split-payout-reference-input"
                />
              </div>
              <div className="form-group position-relative filter-object flex-grow-1 w-auto --no-max-width --search-container mr-0">
                <img src={search} alt="search icon" aria-hidden />
                <input
                  type="search"
                  className="form-control"
                  placeholder="Search Settlement Reference ID..."
                  value={state.settlementReference}
                  onChange={e => setState({ settlementReference: cleanInput(e.target.value) })}
                  style={{ border: '1.5px solid #EAF2FE' }}
                  data-testid="split-payout-reference-input"
                />
              </div>
            </>
          )}

          {!id && (
            <div
              className={`${isTrxFilter ? 'split-payments-search-account-input' : ''} form-group position-relative filter-object flex-grow-1 w-auto --no-max-width --search-container mr-0`}
            >
              <img src={search} alt="search icon" aria-hidden />
              <input
                type="search"
                className="form-control"
                placeholder="Search Account Number..."
                value={state.accountNumber}
                onChange={e => setState({ accountNumber: stripNonNumeric(e.target.value) })}
                style={{ border: '1.5px solid #EAF2FE' }}
                data-testid="account-number-input"
              />
            </div>
          )}

          <button aria-label="filter transactions" type="button" className="settlement-filter__filter-button" onClick={() => filterFn()}>
            <img src={arrowRight} alt="arrow right icon" aria-hidden />
          </button>
        </div>
      </div>
    </section>
  );
};

export default SplitPaymentFilter;
