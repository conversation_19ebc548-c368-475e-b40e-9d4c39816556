@import 'styles/base/variables';

.transaction-details__comp {
  .content-box {
    padding: 1rem 0.5rem 0 1rem;
  }

  .header-row {
    .goback-btn {
      margin-top: -20px;
      padding: 0;
      font-weight: 500;
      font-size: 0.9rem;
      letter-spacing: -0.005em;
    }
    .action-btns-container {
      display: flex;
      margin-bottom: 10px;
      button {
        font-size: 0.9rem;
        margin-left: 10px;
        min-height: 35px;
        font-weight: 500;
        &:first-of-type {
          margin-left: 0;
        }
      }
    }
    .invoice-heading {
      font-style: normal;
      border-bottom: 0.5px solid #a9afbc;
      margin-top: 12px;
      justify-content: space-between;
      align-items: center;
      flex-direction: column;
      display: flex;
      flex-wrap: wrap;
      .invoice-details {
        display: flex;
        align-items: center;
        margin-right: 10px;
        margin-bottom: 10px;

        @media (max-width: $breakpoint-tablet) {
          margin-right: 0px !important;
        }
      }

      .btn-secondary {
        background: #eaf2fe;
        border: none;
        color: #414f5f;
        margin-right: 0%;
        font-size: 0.9rem;
        font-weight: 500;
        padding: 0.6rem 1.75rem;
        border-radius: 8px;
      }

      .btn-dark {
        background: #3e4b5b;
        border: none;
        color: #ffffff;
        margin-right: 0%;
        font-size: 0.9rem;
        font-weight: 500;
        padding: 0.6rem 1.5rem;
        border-radius: 5px;
      }

      @media (min-width: $breakpoint-tablet) {
        flex-direction: row;
      }

      h3 {
        font-weight: 600;
        font-size: 1.6rem;
      }

      .invoice-date {
        color: rgba(0, 0, 0, 0.5);
        font-size: 1rem;
        margin-left: 10px;

        span {
          font-weight: 400;
          border-radius: 5px;
          padding: 4px 10px;
          font-size: 0.85rem;
        }
      }

      .amount-heading {
        font-size: 1.5rem;
        font-weight: 600;
        color: #3e4b5b;
        margin-right: 15px;
        span {
          font-size: 1rem;
          margin-left: 8px;
          color: #a9afbc;
        }

        @media (max-width: '768px') {
          font-size: 1rem;
        }
      }
    }
  }

  .transaction-details-container {
    display: flex;
    padding: 0px;
    margin-top: -20px;
    justify-content: space-between;
    flex-direction: column;

    @media (min-width: $breakpoint-tablet) {
      flex-direction: row;
    }

    p {
      color: #636c72;
      font-weight: 500;
    }

    section {
      margin-top: 2.5rem;
      flex-grow: 1;
      padding-top: 2rem;
      font-size: 0.9rem;

      &:first-of-type {
        padding: 2rem 2rem 2rem 1rem;
      }
    }

    .section-heading {
      color: rgb(16, 38, 73);
      font-weight: 600;
      font-size: 1rem;
      padding: 0;
    }

    .trxn-information {
      padding: 0px !important;
      summary {
        display: flex;
        align-items: center;
        justify-content: space-between;

        p {
          margin: 0;
          font-weight: 500;

          &:last-of-type {
            font-weight: 600;
            color: #102649;
          }
        }
      }

      .accordion-content {
        padding: unset;
      }

      .trxn-breakdown-list {
        display: flex;
        list-style-type: none;
        padding: 0px;
        height: fit-content;
        justify-content: space-between;
        align-items: flex-start;

        .btn-original {
          color: #2376f3 !important;
          padding: 0;
          padding-top: 0.375rem;
          font-size: 0.85rem;
        }

        .btn-original {
          color: #2376f3 !important;
          padding: 0.375rem 0rem;
          font-size: 0.85rem;
        }

        p {
          width: max-content;
          margin-right: 0.4rem;
          font-weight: 300;
          font-size: 0.9rem;
          color: #414f5f;
          letter-spacing: -0.003em;
          max-width: 220px;
          word-break: break-all;

          @media (max-width: $breakpoint-tablet) {
            margin-top: -0.7rem;
          }

          &:last-of-type {
            margin-top: -0.7rem;
            font-weight: 600;
            font-size: 0.9rem;
            justify-content: center;
            color: #3e4b5b;
            margin-bottom: 0;

            @media (max-width: $breakpoint-tablet) {
              font-size: 0.8rem;
              font-weight: 400;
            }
          }
        }

        li {
          padding-left: 0.6rem;
          border-left: 1px solid #dde2ec;

          &:first-of-type {
            border: 0px;
            padding-left: 0px;
          }
        }

        .reason {
          display: flex;
          position: relative;
          margin-top: 0.2rem;
          border: none;
          width: 160px;
          justify-content: center;
          align-items: center;

          &.failed {
            summary {
              p {
                &:last-of-type {
                  color: #f32345;
                }
              }
            }
          }

          summary {
            display: flex !important;
            background: #f2f4f8;
            border-radius: 0.313rem 0.313rem;
            padding: 0.7rem 1rem;
            align-items: center !important;
            justify-content: center !important;

            section {
              margin: 0px !important;
              padding: 0px !important;
              p {
                &:last-of-type {
                  color: #f32345;
                  margin: 0px !important;
                  font-size: 0.8rem;
                }
              }
              img {
                background: none !important;
                background-color: #f2f4f8 !important;
              }
            }

            p {
              opacity: 0.5;

              &:last-of-type {
                opacity: unset;
              }
            }
          }

          &[open] {
            width: fit-content;
            max-width: 250px;
            height: fit-content;
          }

          .accordion-content {
            padding: 1rem;
            background: #f2f4f8;
            margin-top: -0.6rem;
            padding: 0.7rem 1rem;
            border-radius: 0.313rem 0.313rem;
          }
        }
      }
    }

    .refund-heading {
      display: flex;
      justify-content: space-between;
      margin: 4.5rem 0 1rem;

      button {
        background: none;
        border: none;
        font-weight: 500;
        color: $kpyblue;

        img {
          width: 1rem;
          margin-left: 5px;
        }
      }
    }

    .refund-list {
      li {
        padding: 1.1rem 0 0.8rem !important;
        display: block !important;
      }

      #pending-circles {
        opacity: 0.7;
        width: 1rem;
      }
    }

    .refund-details {
      background: #f1f6fa;
      margin: 1rem 0;
      padding: 1.5rem !important;
      border-radius: 0 0 10px 10px;

      li {
        border: none !important;
        display: flex !important;
        justify-content: space-between;
        padding: 0.5rem 0 !important;

        p {
          margin: 0 !important;
        }
      }
    }

    .customer-information {
      @media (min-width: $breakpoint-tablet) {
        width: 50%;
      }
      @media (min-width: $breakpoint-desktop) {
        max-width: 38%;
      }
      .text-tooltip-w {
        div.text-tooltip--content {
          width: 130px;
          padding: 0.3rem 0.8rem;
          text-align: center;
        }
      }
      #bank-icon {
        width: 1rem;
        margin-bottom: 7px;
        margin-right: 10px;
      }
      .card-logo {
        width: 1.5rem;
        margin-right: 8px;
      }
      p {
        display: flex;
        justify-content: space-between;

        > span {
          display: block;
          &:last-of-type {
            margin-left: 1.5rem;
            word-break: break-all;
            text-align: right;
            color: rgba(16, 38, 73, 0.4);
          }
        }
      }
      ul {
        list-style-type: none;
        padding: 0;
        li {
          border-bottom: 1px solid rgba(16, 38, 73, 0.2);
          padding-bottom: 1.2rem;
          margin-bottom: 1.2rem;

          &:last-of-type {
            border: none;
            padding: 0;
            margin: 0;
          }
        }
      }
    }

    @media (max-width: $breakpoint-tablet) {
      flex-direction: row;

      .trxn-information {
        width: 100%;

        .trxn-breakdown-list {
          width: 100%;
          display: inline;
          list-style-type: none;
          padding: 0px;
          height: fit-content;
          p {
            display: flex;
            justify-content: flex-start;
            flex-basis: 50%;
            color: #414f5f;
            opacity: 0.7;
            margin-right: 0 !important;
            font-weight: 500;

            &:last-of-type {
              justify-content: flex-end;
              color: #414f5f;
              opacity: 1;
              font-weight: 500;
              display: block;
            }
          }
          li {
            display: flex;
            width: 100%;
            padding-left: 0;
            border-left: none;
            margin-left: 0;
            height: auto;
            text-align: right;
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  .copy-button-payref {
    img {
      width: 1.2em !important;
      margin-top: -7px !important;
    }
  }

  .text-tooltip--content {
    color: white;
    font-weight: 400;
    font-size: 0.8rem;
  }

  #wallet-icon {
    margin: -3px 0.7rem 0 0;
    width: 1.1rem;
  }
}

span.more-details-opt {
  margin: '3px 7px 5px 0';
  display: 'inline-flex';
  align-items: 'center';
}

.overpayment-underpayment-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.overpayment-underpayment-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 15px;
  gap: 10px;
  width: 100%;
  background: #f1f6fa;
  border-radius: 5px;
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.003em;
  color: #414f5f;
}

.overpayment-underpayment-list-left-side {
  color: #636c72;
}

.overpayment-underpayment-list-right-side {
  color: #102649;
  opacity: 0.4;
}

.overpayment-underpayment-list:last-child > .overpayment-underpayment-list-right-side {
  opacity: 1;
}

#section-title {
  color: #4e555b;
  font-weight: 700;
  font-size: 18px;
  margin-top: 30px;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

.transaction-details__comp_2 {
  margin-top: 20px;
  .transaction-details-container-2 {
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    @media (min-width: $breakpoint-tablet) {
      flex-direction: row;
    }

    p {
      color: #636c72;
      font-weight: 500;
    }

    .section-heading {
      padding: 5px 0;
      font-weight: 700;
      margin-top: 20px;
      border-bottom: 0.5px solid #a9afbc;

      &-link {
        color: #2376f3;
        font-weight: 400;
        font-size: 16px;
        cursor: pointer;
      }
    }

    .more-trxn-heading {
      font-size: 18px;
      margin-top: 30px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      color: #4e555b;

      @media (max-width: 768px) {
        font-size: 14px;

        .section-heading-link {
          font-size: 13px;
        }
      }
    }

    .target-ref {
      color: #2376f3;
      cursor: pointer;
    }

    .overpayment-underpayment-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 15px;
      gap: 10px;
      width: 100%;
      background: #f1f6fa;
      border-radius: 5px;
      margin-bottom: 20px;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: -0.003em;
      color: #414f5f;
    }

    .customer-information {
      flex-grow: 1;
      padding: 0;
      font-size: 0.5rem;
      font-weight: 500;

      @media (min-width: $breakpoint-tablet) {
        width: 100%;
      }

      @media (min-width: $breakpoint-desktop) {
        max-width: 100%;
      }

      .copyable-blue {
        font-weight: 500;
        color: #2376f3;
      }

      .copyable-bold {
        font-weight: 500;
      }

      .card-logo {
        border-radius: 2px;
        width: 1.5rem;
        margin-right: 8px;
        box-shadow: 0px 1px 2px -1px rgb(0 0 0 / 30%);
      }

      p {
        display: flex;
        font-size: 0.9rem;
        justify-content: flex-start;
        > span {
          flex-basis: 20%;
          display: block;
          color: #a9afbc;

          &:last-of-type {
            margin-left: 1.5rem;
            text-align: left !important;
            color: #414f5f;
            font-weight: 400;
            flex-basis: 34%;
          }

          @media (max-width: $breakpoint-tablet) {
            color: #414f5f;
            opacity: 0.7;
            font-weight: 500;
            &:last-of-type {
              text-align: right !important;
              flex-basis: 65%;
              min-width: 0;
              overflow-wrap: break-word;
              opacity: 1;
              font-weight: 500;
              font-size: 0.8rem;
            }
          }

          @media (max-width: $breakpoint-tablet) {
            flex-basis: 50% !important;
          }

          @media (min-width: $breakpoint-desktop) {
            max-width: 100%;
          }
        }
      }

      ul {
        list-style-type: none;
        padding: 0;
        li {
          padding-bottom: 1.2rem;
          margin-bottom: 1.2rem;

          &:last-of-type {
            border: none;
            padding: 0;
            margin: 0;
          }
        }
      }
      .timeline__container {
        position: relative;

        &::before {
          position: absolute;
          content: '';
          width: 1px;
          top: 1rem;
          bottom: 1rem;
          transform: translateX(450%);
          background-color: #d5d8db;
        }

        .timeline-item + .timeline-item {
          margin-top: 3rem;
        }

        .timeline__date {
          display: flex;
          align-items: center;
          gap: 15px;
          position: relative;
          z-index: 1;
          font-weight: 400;

          > img {
            width: 0.65rem;
          }
        }

        & + button {
          margin-top: 2rem;
          font-size: 14px;
          padding: 0.5rem 0.15rem 0.5rem 0;
          color: #2a7af0;
          display: flex;
          align-items: center;
          gap: 10px;

          > img {
            width: 1.125rem;
          }
        }
      }

      .chargeback-docs__container {
        border-radius: 10px;
        background-color: #f9fbfd;
        padding: 30px;
        margin-block-start: 40px;

        .chargeback-docs__list {
          padding-inline: 30px;
          padding-block: 20px;
          border-radius: 10px;
          background-color: #ffffff;
          box-shadow: 0 5px 3px 0 hsla(221, 25%, 59%, 0.04);

          > * {
            color: #94a7b7;
          }

          > p {
            font-weight: 400;
          }
          .chargeback-docs__item + .chargeback-docs__item {
            margin-top: 24px;
          }
        }

        .chargeback-docs__item {
          background-color: #f9fbfd;
          border: 3px solid #f3f4f8;
          padding-block: 10px;
          padding-inline: 15px;
          border-radius: 10px;
          font-size: 0.85rem;
          display: flex;
          gap: 20px;
          align-items: center;
          width: 100%;

          @media (max-width: 800px) {
            flex-direction: column;
            justify-content: center;
          }

          > .chargeback-docs__info {
            display: flex;
            flex-wrap: wrap;
            place-items: center;
            gap: 20px;

            @media (max-width: 800px) {
              flex-direction: column;
            }

            > svg path {
              fill: hsla(207, 20%, 65%, 1);
            }

            > span:nth-of-type(1) {
              font-weight: 500;
              min-width: 0;
              overflow-wrap: anywhere;
            }

            > span:nth-of-type(2) {
              font-weight: 400;
              color: #a9afbc;
            }
          }

          > .chargeback-docs__link {
            margin-inline-start: auto;
            color: #2376f3;
            display: flex;
            gap: 9px;
            min-width: 0;
            overflow-wrap: anywhere;

            @media (max-width: 800px) {
              margin-inline: auto;
            }
          }
        }
      }
    }
  }
}

.back-to-top {
  display: flex;
  height: 200px;
  width: 100%;
  align-items: center;
  justify-content: center;

  .btn {
    padding: 5px 10px;
    font-weight: 500;
    letter-spacing: 0.086px;
    color: #414f5f;
    border-radius: 20px;
    background: #f9fbfd;
    box-shadow: 0px 5px 7px rgba(126, 142, 177, 0.1);
  }
}

.rversal-rfund-cback {
  width: 100%;
  display: flex;

  @media (max-width: $breakpoint-desktop) {
    padding-top: 10px;
    display: block;
  }

  @media (min-width: $breakpoint-desktop) {
    max-width: 100%;
  }

  #reversals-tabs-list {
    width: 20%;
    min-width: 160px;

    @media (max-width: $breakpoint-desktop) {
      width: 100%;
    }
    ul {
      border-bottom: none !important;
      width: 100%;

      @media (max-width: $breakpoint-desktop) {
        display: flex;
      }
      .tab-label {
        font-size: 0.9rem;
        font-weight: 400;
      }

      .nav-item {
        margin-bottom: 0px;
        padding-bottom: 0px;
        margin-top: 0px;
        width: 100%;

        @media (max-width: $breakpoint-desktop) {
          width: auto;
        }
      }

      .nav-item.active {
        background: #f1f6fa;
        @media (max-width: $breakpoint-desktop) {
          background: none;
        }
      }
    }

    .nav-link {
      padding: 12px 20px 12px 20px;
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;

      @media (max-width: $breakpoint-desktop) {
        padding: 12px 0px 12px 0px;
        width: auto;
        font-weight: 800;
        margin-right: 1.2rem;
      }
      &::after {
        display: none;
      }

      .tab-icon {
        border-top: 3px solid #a9afbc;
        border-right: 3px solid #a9afbc;
        width: 10px;
        height: 10px;
        transform: rotate(45deg);

        @media (max-width: $breakpoint-desktop) {
          display: none;
        }
      }

      .tab-icon.active {
        border-color: #2376f3;
      }
    }
    .nav-link.active {
      border-left: 3px solid #2376f3;
      padding-left: 17px;

      @media (max-width: $breakpoint-desktop) {
        border-left: none;
        border-bottom: 3px solid #2376f3;
        padding-left: 0px;
      }
    }
  }
  .render-tabs {
    width: 80%;
    border-left: 2px solid #f1f6fa;
  }
}

.bulk-id {
  color: #2376f3;
  font-weight: 500;
  &:hover {
    text-decoration: none;
    color: #2376f3;
  }
}

.related-transactions {
  font-size: 0.9rem;

  .more-trxn-heading {
    display: flex;
    justify-content: space-between;
    margin: 1.875rem 0 1.25rem;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    &-item {
      border: 1px solid #1026493d !important;
      border-radius: 5px;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 1rem !important;

      div:nth-of-type(1) {
        color: #a9afbc;
      }

      div:nth-of-type(2) {
        align-items: center;
        display: flex;
        gap: 0.5rem;

        span:nth-of-type(1) {
          font-size: 0.8rem;
          text-transform: uppercase;
        }
        span:nth-of-type(2) {
          color: #a9afbc;
          text-transform: capitalize;
        }
      }

      div:nth-of-type(3) {
        align-items: center;
        display: flex;
        gap: 0.5rem;

        span:nth-of-type(2) {
          color: #a9afbc;
          text-transform: capitalize;
        }
      }
    }
  }
}

@media (min-width: 1280px) {
  .related-transactions {
    font-size: 0.9rem;

    .more-trxn-heading {
      display: flex;
      justify-content: space-between;
      margin: 1.875rem 0 1.25rem;
    }

    &__list {
      display: block;

      &-item {
        align-items: center;
        border: 0 !important;
        border-radius: 0;
        flex-direction: row;
        justify-content: space-between;
        gap: 1rem;
        padding: 0 !important;

        div:nth-of-type(1) {
          flex-basis: 14%;
        }

        div:nth-of-type(2) {
          flex-basis: 48%;
        }

        div:nth-of-type(3) {
          align-items: center;
          display: flex;
          gap: 0.5rem;
          flex-basis: 20%;

          span:nth-of-type(1) {
            display: flex;
            align-items: center;
          }
        }

        div:nth-of-type(4) {
          flex-basis: 10%;
          text-align: right;
        }
      }
      &-conversions {
        div:nth-of-type(4) {
          color: #a9afbc;
          flex-basis: 18%;
          text-align: right;
        }
      }
    }
  }
}

@media (min-width: 1560px) {
  .related-transactions {
    &__list {
      &-item {
        gap: 2rem;

        div:nth-of-type(2) {
          gap: 0.5rem;

          span:nth-of-type(1) {
            font-size: 1rem;
          }
        }

        div:nth-of-type(3) {
          gap: 1.5rem;
        }

        div:nth-of-type(4) {
          font-size: 1rem;
        }
      }
    }
  }
}
