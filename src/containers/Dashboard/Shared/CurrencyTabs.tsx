/* eslint-disable no-unused-vars */
import React, { useEffect, useMemo, useState } from 'react';

import { useBreakpoints, useClickOutside } from '+hooks';
import useStore from '+store';
import { currencyOrder, customTabOrder } from '+utils';

import './CurrencyTabs.scss';

interface ICurrencyTab {
  currencies: string[];
  allowedCurrencies?: string[];
  activeCurrency: string;
  onClick: (arg: string) => void;
  disabledOption?: boolean;
}

const queries = {
  isMobile: '(max-width: 650px)'
};
const CurrencyTabs: React.FC<ICurrencyTab> = ({ currencies, activeCurrency, onClick, allowedCurrencies, disabledOption = false }) => {
  const defaultCurrency = useStore(state => state.defaultCurrency);
  const customOrder = customTabOrder(currencies, currencyOrder, defaultCurrency);
  const currencyCustomOrder = Array.isArray(customOrder) ? customOrder : Object.keys(customOrder || {});
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const orderedCurrencies = useMemo(() => {
    if (!disabledOption) return currencyCustomOrder;

    if (!allowedCurrencies || allowedCurrencies.length === 0) return currencyCustomOrder;

    const enabled = currencyCustomOrder.filter(c => allowedCurrencies.includes(c));
    const disabled = currencyCustomOrder.filter(c => !allowedCurrencies.includes(c));
    return [...enabled, ...disabled];
  }, [currencyCustomOrder, disabledOption, allowedCurrencies]);

  const breakpoint = useBreakpoints(queries);
  const isMobile = (breakpoint as unknown as { isMobile: boolean })?.isMobile;
  const wrapperRef = useClickOutside<HTMLDivElement>(() => {
    setDropdownVisible(false);
  });

  const tabSwithMaxvisibleCurrencies = isMobile ? 5 : orderedCurrencies?.length;

  const visibleCurrencies = useMemo(() => {
    if (!orderedCurrencies || !tabSwithMaxvisibleCurrencies) return [];

    const visible = orderedCurrencies.slice(0, tabSwithMaxvisibleCurrencies);
    if (!visible.includes(activeCurrency)) {
      visible[tabSwithMaxvisibleCurrencies - 1] = activeCurrency;
    }

    return visible;
  }, [orderedCurrencies, tabSwithMaxvisibleCurrencies, activeCurrency]);

  const hasDropdown = visibleCurrencies?.length < orderedCurrencies?.length;

  useEffect(() => {
    if (!isMobile) {
      setDropdownVisible(false);
    }
  }, [activeCurrency]);

  return (
    <>
      <ul className="nav currency-nav nav-tabs">
        {visibleCurrencies?.map((currency: string) => (
          <li className="nav-item currency-nav-item" key={currency}>
            <button
              type="button"
              className={`currency-nav-link nav-link ${activeCurrency === currency ? 'active' : ''} ${!currencies?.includes(currency) ? 'disabled' : ''}`}
              onClick={() => onClick(currency)}
              disabled={disabledOption ? !allowedCurrencies?.includes(currency) : false}
            >
              {currency}
            </button>
          </li>
        ))}
      </ul>
      {hasDropdown && (
        <div className="currency-dropdown" ref={wrapperRef} onClick={() => setDropdownVisible(!dropdownVisible)}>
          <span>
            <i className="os-icon os-icon-chevron-down" />
          </span>
          {dropdownVisible && (
            <ul className="element-box box-style currency-options">
              {orderedCurrencies.map(currency => {
                const disabled = disabledOption && !allowedCurrencies?.includes(currency);
                return (
                  <li
                    role="presentation"
                    className={`ellipsis__item ${activeCurrency === currency && 'active'} ${disabled ? 'disabled' : ''}`}
                    key={currency}
                    onClick={() => {
                      if (disabled) return;
                      onClick(currency);
                      setDropdownVisible(false);
                    }}
                  >
                    <span>{currency}</span>
                  </li>
                );
              })}
            </ul>
          )}
        </div>
      )}
    </>
  );
};

export default CurrencyTabs;
