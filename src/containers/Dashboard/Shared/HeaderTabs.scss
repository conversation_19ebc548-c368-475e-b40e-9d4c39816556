.header-nav {
  @media (max-width: 990px) {
    display: none;
  }
}

.header-dropdown {
  position: relative;
  .header-item {
    display: flex;
    align-items: baseline;
    cursor: pointer;
    p {
      color: #292b2c;
      font-weight: 500;
      font-size: 18px;
      margin-right: 10px;
      margin-bottom: 0 !important;
    }
    i {
      color: #292b2c;
    }
  }
  .header-options {
    position: absolute;
    left: 0;
    top: 35px;
    width: 358px;
    white-space: nowrap;
    text-align: left;
    z-index: 6;
    padding: 1rem;
    border-radius: 5px;

    li {
      margin-bottom: 16px;

      span {
        font-size: 16px;
      }
    }

    .active {
      font-weight: 500;
    }
  }

  @media (min-width: 990px) {
    display: none;
  }
}
