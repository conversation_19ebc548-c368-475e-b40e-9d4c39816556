import React from 'react';
import { render, screen } from '@testing-library/react';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import EmptyStateComponent, { EmptyStateComponentProps } from '../EmptyState';

const MockedEmptyStateComponent = (props: EmptyStateComponentProps) => (
  <MockIndexWithRoute route="/" initialEntries={['/']}>
    {/* eslint-disable-next-line react/jsx-props-no-spreading */}
    <EmptyStateComponent {...props} />
  </MockIndexWithRoute>
);

describe('EmptyStateComponent', () => {
  it('renders heading and default message', () => {
    render(<MockedEmptyStateComponent heading="No Data" />);
    expect(screen.getByText('No Data')).toBeInTheDocument();
    expect(screen.getByText(/It looks like there are no results yet/)).toBeInTheDocument();
    expect(screen.getByRole('img', { name: /Nothing/i })).toBeInTheDocument();
  });

  it('renders custom message', () => {
    render(<MockedEmptyStateComponent heading="No Data" message="Custom message" />);
    expect(screen.getByText('Custom message')).toBeInTheDocument();
  });

  it('hides message when hideMessage is true', () => {
    render(<MockedEmptyStateComponent heading="No Data" hideMessage />);
    expect(screen.queryByText(/It looks like there are no results yet/)).not.toBeInTheDocument();
  });

  it('renders CTA when provided', () => {
    render(<MockedEmptyStateComponent heading="No Data" cta={<button>Click Me</button>} />);
    expect(screen.getByText('Click Me')).toBeInTheDocument();
  });
});
