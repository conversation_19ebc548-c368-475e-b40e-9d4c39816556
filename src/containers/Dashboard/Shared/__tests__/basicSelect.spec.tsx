import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import BasicSelect from '../BasicSelect';

describe('BasicSelect', () => {
  const options = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' }
  ];
  const setValue = vi.fn();

  it('Renders with placeholder', () => {
    render(<BasicSelect placeholder="Select an option" options={options} setValue={setValue} />);
    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('Opens dropdown on click', async () => {
    render(<BasicSelect options={options} setValue={setValue} />);
    await userEvent.click(screen.getByRole('button'));
    await screen.findByRole('listbox');
    options.forEach(option => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
    });
  });

  it('Selects an option', async () => {
    render(<BasicSelect options={options} setValue={setValue} />);
    await userEvent.click(screen.getByRole('button'));
    await userEvent.click(screen.getByText('Option 1'));
    expect(setValue).toHaveBeenCalledWith('option1');
    expect(screen.getByText('Option 1')).toBeInTheDocument();
  });

  it('Resets selection on select if resetOnSelect is true', async () => {
    render(<BasicSelect options={options} setValue={setValue} resetOnSelect />);
    await userEvent.click(screen.getByRole('button'));
    await userEvent.click(screen.getByText('Option 1'));
    expect(screen.getByText('Select')).toBeInTheDocument();
  });

  it('Disables the button when disabled prop is true', () => {
    render(<BasicSelect options={options} setValue={setValue} disabled />);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
