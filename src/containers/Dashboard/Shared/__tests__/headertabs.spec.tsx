import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import HeaderTabs from '../HeaderTabs';

describe('HeaderTabs', () => {
  it('calls onClick with the correct tab when clicked', async () => {
    const handleClick = vi.fn();
    const tabs = ['home', 'profile', 'settings'];

    render(<HeaderTabs tabs={tabs} activeTab="home" onClick={handleClick} />);

    await userEvent.click(screen.getByText('Profile'));
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(handleClick).toHaveBeenCalledWith('profile');
  });
});
