import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import useStore from '+store';

import CurrencyTabs from '../CurrencyTabs';

beforeAll(() => {
  useStore.setState(state => ({
    ...state,
    defaultCurrency: 'NGN'
  }));
});

describe('CurrencyTabs', () => {
  it('renders the currencies correctly', () => {
    const handleClick = vi.fn();
    const currencies = ['USD', 'EUR', 'GBP'];

    render(<CurrencyTabs currencies={currencies} activeCurrency="USD" onClick={handleClick} />);

    currencies.forEach(currency => {
      expect(screen.getByText(currency)).toBeInTheDocument();
    });
  });

  it('applies the active class to the active currency', () => {
    const handleClick = vi.fn();
    const currencies = ['USD', 'EUR', 'GBP'];

    render(<CurrencyTabs currencies={currencies} activeCurrency="EUR" onClick={handleClick} />);

    const activeTab = screen.getByText('EUR');
    expect(activeTab).toHaveClass('active');
  });

  it('calls onClick with the correct currency when a tab is clicked', async () => {
    const handleClick = vi.fn();
    const currencies = ['USD', 'EUR', 'GBP'];

    render(<CurrencyTabs currencies={currencies} activeCurrency="USD" onClick={handleClick} />);

    await userEvent.click(screen.getByText('GBP'));
    expect(handleClick).toHaveBeenCalledWith('GBP');
  });

  it('displays the default currency first', () => {
    const handleClick = vi.fn();
    const currencies = ['NGN', 'USD', 'EUR', 'GBP'];

    render(<CurrencyTabs currencies={currencies} activeCurrency="USD" onClick={handleClick} />);

    const tabs = screen.getAllByRole('button');

    expect(tabs[0]).toHaveTextContent('NGN');
    expect(tabs[1]).toHaveTextContent('USD');
    expect(tabs[2]).toHaveTextContent('GBP');
    expect(tabs[3]).toHaveTextContent('EUR');
  });

  it('moves disabled currencies to the end when disabledOption is true', () => {
    const handleClick = vi.fn();
    const currencies = ['USD', 'EUR', 'GBP'];
    const allowedCurrencies = ['USD', 'GBP'];

    render(
      <CurrencyTabs
        currencies={currencies}
        activeCurrency="USD"
        onClick={handleClick}
        allowedCurrencies={allowedCurrencies}
        disabledOption={true}
      />
    );

    const rendered = screen.getAllByRole('button').map(btn => btn.textContent?.trim() || '');

    const expectedOrder = ['USD', 'GBP', 'EUR'];

    const filtered = rendered.filter(text => expectedOrder.includes(text));
    expect(filtered).toEqual(expectedOrder);
  });
});
