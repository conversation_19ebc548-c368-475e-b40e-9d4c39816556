import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import OtpContent from '../OtpContent';

describe('OtpContent Component', () => {
  const mockSetAuthData = vi.fn();
  const mockSetTwoFactorType = vi.fn();
  const mockOnResendToken = vi.fn();

  const defaultProps = {
    email: '<EMAIL>',
    twoFactorType: 'otp' as 'otp' | 'totp' | 'totp_recovery_code',
    authData: { code: '' },
    setAuthData: mockSetAuthData,
    onResendToken: mockOnResendToken,
    timeLeft: 0,
    setTwoFactorType: mockSetTwoFactorType,
    additionalDetails: null
  };

  it('renders the OTP input and email obfuscation correctly for "otp"', () => {
    render(<OtpContent {...defaultProps} />);

    expect(screen.getByText(/To proceed, enter the OTP/i)).toBeInTheDocument();
    expect(screen.getByText('(tes*******@example.com)')).toBeInTheDocument();
    expect(screen.getByLabelText('One Time PIN (OTP)')).toBeInTheDocument();
  });

  it('renders the correct content for "totp"', () => {
    render(<OtpContent {...defaultProps} twoFactorType="totp" />);

    expect(
      screen.getByText((content, element) => {
        const hasText = (node: Element) =>
          node.textContent ===
          'Enter the six-digit code from your authenticator app in the space provided below to confirm this transaction.';
        const elementHasText = hasText(element!);
        const childrenDontHaveText = Array.from(element!.children).every(child => !hasText(child));
        return elementHasText && childrenDontHaveText;
      })
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Authentication Code')).toBeInTheDocument();
  });

  it('renders the correct content for "totp_recovery_code"', () => {
    render(<OtpContent {...defaultProps} twoFactorType="totp_recovery_code" />);

    expect(screen.getByText(/Enter one of your previously saved recovery codes/i)).toBeInTheDocument();
    expect(screen.getByLabelText('Recovery Code')).toBeInTheDocument();
  });

  it('calls setAuthData with the correct input value', async () => {
    render(<OtpContent email="<EMAIL>" twoFactorType="otp" authData={{ code: '' }} setAuthData={mockSetAuthData} />);

    const input = screen.getByTestId('otp-input');
    await userEvent.type(input, '7');

    expect(mockSetAuthData).toHaveBeenLastCalledWith({ code: '7' });
  });

  it('calls onResendToken when "Resend" button is clicked', async () => {
    render(<OtpContent {...defaultProps} />);

    const resendButton = screen.getByText('Resend');
    await userEvent.click(resendButton);

    expect(mockOnResendToken).toHaveBeenCalled();
  });

  it('displays the countdown timer when timeLeft is greater than 0', () => {
    render(<OtpContent {...defaultProps} timeLeft={30} />);

    expect(screen.getByText(/Wait 30 seconds/i)).toBeInTheDocument();
    expect(screen.queryByText('Resend')).not.toBeInTheDocument();
  });

  it('calls setTwoFactorType when "Confirm using recovery codes" button is clicked', async () => {
    render(<OtpContent {...defaultProps} twoFactorType="totp" />);

    const recoveryButton = screen.getByText('Confirm using recovery codes');
    await userEvent.click(recoveryButton);

    expect(mockSetTwoFactorType).toHaveBeenCalledWith('totp_recovery_code');
  });

  it('renders additional details when provided', () => {
    render(<OtpContent {...defaultProps} additionalDetails={<p>Additional details here</p>} />);

    expect(screen.getByText('Additional details here')).toBeInTheDocument();
  });

  it('does not have any accessibility violations', async () => {
    const { container } = render(<OtpContent {...defaultProps} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
