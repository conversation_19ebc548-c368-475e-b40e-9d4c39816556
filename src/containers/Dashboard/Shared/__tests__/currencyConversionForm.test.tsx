/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import CurrencyConversionForm from '../CurrencyConversionForm';

type FormType = 'main' | 'modal';

const mockProps = {
  formType: 'main' as 'main' | 'modal',
  payloadCurrency: { from: 'NGN', to: 'USD' },
  handleSelectChange: vi.fn(),
  handleSwitchCurrency: vi.fn(),
  handleProcessConversion: vi.fn(),
  availableBalance: 1000,
  errorMessage: '',
  checkLimitAndValidate: true,
  formatAmount: (n: number) => n.toLocaleString(),
  rate: 100,
  toAmount: 200,
  isLoading: false,
  amount: '500',
  handleInputChange: vi.fn(),
  updateCurrency: vi.fn(),
  amountInputRef: React.createRef<HTMLInputElement>(),
  currentCount: 10,
  initiateConversion: { isLoading: false },
  sourceCurrency: ['NGN', 'USD'],
  destinationCurrency: ['USD', 'NGN'],
  isProcessConversionLoading: false,
  currencyArray: ['NGN', 'USD', 'KES', 'GHS'],
  state: { isProcessing: false, data: true },
  cleanInput: vi.fn(),
  from_currency_rate: 1500,
  to_currency_rate: 2
};

const MockCurrencyConversionForm = (props: Partial<typeof mockProps> & { formType: FormType }) => (
  <MockIndex>
    <CurrencyConversionForm {...mockProps} {...props} formType={props.formType} />
  </MockIndex>
);

describe('CurrencyConversionForm', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<MockCurrencyConversionForm formType="main" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('renders formType with correct fields', () => {
    render(<MockCurrencyConversionForm formType="main" />);
    expect(screen.getByText(/When you convert/i)).toBeInTheDocument();
    expect(screen.getByLabelText("You'll Receive", { selector: '#amountToReceive' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /convert/i })).toBeInTheDocument();
  });

  it('renders modal formType with correct fields', () => {
    render(<MockCurrencyConversionForm formType="modal" />);
    expect(screen.getAllByText(/Pay/i).length).toBeGreaterThanOrEqual(1);
    expect(screen.getByText(/To Receive/i)).toBeInTheDocument();
    expect(screen.getAllByRole('textbox').length).toBeGreaterThan(0);
    expect(screen.getByText(/Balance:/i)).toBeInTheDocument();
  });

  it('calls handleSelectChange when source currency changes', async () => {
    render(<MockCurrencyConversionForm formType="main" />);
    const selects = screen.getAllByRole('combobox');
    await userEvent.selectOptions(selects[0], 'USD');
    expect(mockProps.handleSelectChange).toHaveBeenCalled();
  });

  it('display error message if errorMessage is returned', () => {
    render(<MockCurrencyConversionForm formType="modal" errorMessage="Test error" />);
    expect(screen.getByText(/test error/i)).toBeInTheDocument();
  });

  it('calls updateCurrency when BasicSelect changes in modal', () => {
    render(<MockCurrencyConversionForm formType="modal" />);
    mockProps.updateCurrency?.('currencyFrom', 'USD');
    expect(mockProps.updateCurrency).toHaveBeenCalledWith('currencyFrom', 'USD');
  });

  it('shows conversion rate when checkLimitAndValidate is true', () => {
    render(
      <MockCurrencyConversionForm
        formType="main"
        checkLimitAndValidate={true}
        state={{ isProcessing: false, data: true } as { isProcessing: boolean; data: boolean }}
      />
    );
    expect(screen.getByText(/Rate:/i)).toBeInTheDocument();
  });

  it('shows conversion rate for any currency pair', () => {
    render(
      <MockCurrencyConversionForm
        formType="main"
        checkLimitAndValidate={true}
        payloadCurrency={{ from: 'NGN', to: 'USD' }}
        from_currency_rate={1500}
        to_currency_rate={2}
        formatAmount={(n: number) => n.toLocaleString()}
        state={{ isProcessing: false, data: true } as unknown}
      />
    );
    expect(screen.getByText(/NGN 1500 → USD 1.00/)).toBeInTheDocument();
  });
});
