import { TIcon, TIconNames, TRenderSvg } from './IconNames';

//  Note: Icons should remain in alphabetical order

const icons: Record<TIconNames, TRenderSvg> = {
  arrowDown: ({ style = {}, width = 28, height = 24, fill = 'none' }) => (
    <svg style={style} width={width} height={height} viewBox="0 0 14 11" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.4522 10.5457C6.51336 10.6345 6.5952 10.7071 6.69066 10.7573C6.78613 10.8075 6.89236 10.8337 7.0002 10.8337C7.10805 10.8337 7.21428 10.8075 7.30974 10.7573C7.40521 10.7071 7.48704 10.6345 7.5482 10.5457L13.5482 1.87901C13.6177 1.77905 13.6584 1.66196 13.666 1.54048C13.6735 1.41899 13.6477 1.29776 13.5912 1.18994C13.5347 1.08212 13.4498 0.99184 13.3456 0.928913C13.2414 0.865987 13.1219 0.832817 13.0002 0.833009H1.0002C0.878764 0.83351 0.759761 0.867106 0.655989 0.930184C0.552217 0.993261 0.467604 1.08343 0.411248 1.191C0.354892 1.29857 0.328925 1.41947 0.336141 1.5407C0.343357 1.66192 0.383483 1.77888 0.452202 1.87901L6.4522 10.5457Z"
        fill="black"
      />
    </svg>
  ),
  arrowRight: ({ width = 24, stroke = 'white', height = 24, strokeWidth = 2, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} fill={fill} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12H20" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M14 6L20 12L14 18" stroke="white" strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  calendar: ({ width = 16, stroke = '#DDE2EC', height = 17, strokeWidth = 2, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 16 17" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_9084_11081)">
        <path
          d="M12.6667 3.1665H3.33333C2.59695 3.1665 2 3.76346 2 4.49984V13.8332C2 14.5696 2.59695 15.1665 3.33333 15.1665H12.6667C13.403 15.1665 14 14.5696 14 13.8332V4.49984C14 3.76346 13.403 3.1665 12.6667 3.1665Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M10.6667 1.8335V4.50016" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
        <path d="M5.33334 1.8335V4.50016" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
        <path d="M2 7.1665H14" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_9084_11081">
          <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
        </clipPath>
      </defs>
    </svg>
  ),
  cancel: ({ width = 20, height = 20, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 20 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.47301 0 0 4.47253 0 10C0 15.527 4.47253 20 10 20C15.527 20 20 15.5275 20 10C20 4.47301 15.5275 0 10 0ZM13.6597 11.7949C14.1747 12.3098 14.1747 13.1448 13.6597 13.6598C13.4022 13.9173 13.0647 14.046 12.7273 14.046C12.3898 14.046 12.0524 13.9173 11.7949 13.6598L10 11.8649L8.20519 13.6597C7.94769 13.9172 7.6102 14.046 7.27275 14.046C6.9353 14.046 6.5978 13.9172 6.34031 13.6597C5.82536 13.1447 5.82536 12.3098 6.34031 11.7948L8.13512 10L6.34031 8.20519C5.82536 7.6902 5.82536 6.85525 6.34031 6.34026C6.8553 5.82527 7.69024 5.82527 8.20523 6.34026L10 8.13508L11.7949 6.34026C12.3098 5.82527 13.1448 5.82527 13.6598 6.34026C14.1747 6.85525 14.1747 7.6902 13.6598 8.20519L11.8649 10L13.6597 11.7949Z"
        fill="#3E4B5B"
      />
    </svg>
  ),
  cancelled: ({ width = 25, height = 25, stroke = '#F32345' }) => (
    <svg width={width} height={height} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M18.5 6L6.5 18" stroke={stroke} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.5 6L18.5 18" stroke={stroke} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  caretDown: ({ height = 9, width = 9, name, strokeWidth = 2, stroke = '#414F5F', ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M1 1.5L7 7.5L13 1.5" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),

  merchantAvatar: ({ width = 64, height = 64, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 64 64" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <rect width="64" height="64" rx="32" fill="url(#paint0_linear_10776_98095)" />
      <path
        d="M37.8346 32.0003H40.3346V38.667C40.3346 39.109 40.159 39.5329 39.8465 39.8455C39.5339 40.1581 39.11 40.3337 38.668 40.3337H37.8346V32.0003ZM36.168 25.3337V40.3337H25.3346C24.8926 40.3337 24.4687 40.1581 24.1561 39.8455C23.8436 39.5329 23.668 39.109 23.668 38.667V25.3337C23.668 24.8916 23.8436 24.4677 24.1561 24.1551C24.4687 23.8426 24.8926 23.667 25.3346 23.667H34.5013C34.9433 23.667 35.3673 23.8426 35.6798 24.1551C35.9924 24.4677 36.168 24.8916 36.168 25.3337ZM30.3346 35.3337H26.168V37.0003H30.3346V35.3337ZM33.668 31.167H26.168V32.8337H33.668V31.167ZM33.668 27.0003H26.168V28.667H33.668V27.0003Z"
        fill="white"
      />
      <defs>
        <linearGradient id="paint0_linear_10776_98095" x1="-3.63216e-07" y1="24" x2="59.5" y2="43" gradientUnits="userSpaceOnUse">
          <stop stopColor="#6FAAE5" />
          <stop offset="1" stopColor="#C599D8" />
        </linearGradient>
      </defs>
    </svg>
  ),
  receipt: ({ height = 31, width = 34 }) => (
    <svg width={width} height={height} viewBox="0 0 44 41" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.5" width="43" height="40.7949" rx="20.3974" fill="#DDE2EC" />
      <rect
        x="10.9727"
        y="14.8848"
        width="22.0513"
        height="15.4359"
        rx="1.10256"
        stroke="white"
        strokeWidth="2.20513"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M26.4065 30.3208V12.6797C26.4065 11.4619 25.4192 10.4746 24.2013 10.4746H19.7911C18.5732 10.4746 17.5859 11.4619 17.5859 12.6797V30.3208"
        stroke="white"
        strokeWidth="2.20513"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  caretDoubleLeft: ({ width = 11, height = 10, stroke = '#2376F3', strokeWidth = 1.66667, opacity = 1 }) => (
    <svg width={width} height={height} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        opacity={opacity}
        d="M11.9994 1.5L6.92216 6.5L11.9994 11.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity={opacity}
        d="M6.07727 1.5L1.00004 6.5L6.07727 11.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  caretDoubleRight: ({ width = 11, height = 10, stroke = '#2376F3', strokeWidth = 1.66667, opacity = 1 }) => (
    <svg width={width} height={height} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        opacity={opacity}
        d="M1 11.5L6.07723 6.5L1 1.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity={opacity}
        d="M6.92212 11.5L11.9993 6.5L6.92212 1.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  caretLeft: ({ width = 6, height = 12, stroke = '#2376F3', strokeWidth = 2, opacity = 1 }) => (
    <svg width={width} height={height} viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        opacity={opacity}
        d="M7 1.5L0.999999 7.5L7 13.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  caretRight: ({ width = 6, height = 12, stroke = '#2376F3', strokeWidth = 2, opacity = 1 }) => (
    <svg width={width} height={height} viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        opacity={opacity}
        d="M1 13.5L7 7.5L1 1.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  caution: ({ stroke = '#FA9500', fill = 'none', height = 18, width = 18, name, ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 18" fill={fill} xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_16774_1909)">
        <path
          d="M17.8467 15.1832L9.97174 1.68311C9.77015 1.33759 9.40017 1.125 8.99998 1.125C8.5998 1.125 8.22985 1.33759 8.02823 1.68311L0.15323 15.1832C-0.0497265 15.5312 -0.0511328 15.961 0.149398 16.3104C0.350176 16.6598 0.722059 16.8751 1.12498 16.8751H16.875C17.2779 16.8751 17.6498 16.6598 17.8506 16.3104C18.0511 15.961 18.0497 15.5312 17.8467 15.1832ZM8.99998 14.625C8.37927 14.625 7.87498 14.1212 7.87498 13.5C7.87498 12.8787 8.37927 12.375 8.99998 12.375C9.62183 12.375 10.125 12.8787 10.125 13.5C10.125 14.1212 9.62183 14.625 8.99998 14.625ZM10.125 10.125C10.125 10.7463 9.62127 11.25 8.99998 11.25C8.3787 11.25 7.87498 10.7463 7.87498 10.125V6.75004C7.87498 6.12875 8.3787 5.62504 8.99998 5.62504C9.62127 5.62504 10.125 6.12875 10.125 6.75004V10.125Z"
          fill={stroke}
        />
      </g>
      <defs>
        <clipPath id="clip0_16774_1909">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  chat: ({ width = 25, height = 24 }) => (
    <svg width={width} height={height} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M22.9279 0H2.07205C1.20386 0 0.5 0.859801 0.5 1.92034V18.0512C0.5 19.1118 1.20386 19.9716 2.07205 19.9716H8.99432L11.297 23.3157C11.5957 23.7495 12.036 24 12.5001 24C12.9641 24 13.4045 23.7496 13.7031 23.3158L16.0058 19.9716H22.9279C23.7962 19.9716 24.5 19.1118 24.5 18.0513V1.92041C24.5 0.859738 23.7962 0 22.9279 0ZM4.97521 5.4708H12.1543C12.5883 5.4708 12.9403 5.9007 12.9403 6.43097C12.9403 6.96124 12.5883 7.39114 12.1543 7.39114H4.97521C4.54112 7.39114 4.18919 6.96124 4.18919 6.43097C4.18919 5.9007 4.54112 5.4708 4.97521 5.4708ZM19.9935 13.2802H5.00655C4.57245 13.2802 4.22052 12.8503 4.22052 12.32C4.22052 11.7898 4.57245 11.3599 5.00655 11.3599H19.9935C20.4275 11.3599 20.7795 11.7898 20.7795 12.32C20.7795 12.8503 20.4275 13.2802 19.9935 13.2802Z"
        fill="#24B314"
      />
    </svg>
  ),
  check: ({ width = 16, height = 12, stroke = '#24B314', ...props }) => (
    <svg width={width} height={height} viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M14.6654 1L5.4987 10.1667L1.33203 6" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  circleCancelEnvelope: ({ width = 100, height = 24, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} fill={fill} viewBox="0 0 101 100" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M100.881 50C100.881 22.3854 78.4958 0 50.8811 0C23.2672 0 0.881104 22.3854 0.881104 50C0.881104 77.6146 23.2672 100 50.8811 100C78.4958 100 100.881 77.6146 100.881 50Z"
        fill="#FFD2DA"
      />
      <path
        d="M39.3736 37.4609C38.9831 37.0703 38.3499 37.0703 37.9594 37.4609C37.5689 37.8514 37.5689 38.4845 37.9594 38.8751L39.6262 40.542C38.5603 41.2411 37.8212 42.3977 37.6881 43.7327L48.4588 49.3748L51.6874 52.6033L51.4638 52.7205C51.1732 52.8728 50.8265 52.8728 50.5358 52.7205L37.6665 45.9787V56.8346L37.6734 57.0805C37.8008 59.3594 39.6891 61.168 41.9998 61.168H59.9998L60.245 61.1612L62.6257 63.542C63.0162 63.9325 63.6494 63.9325 64.04 63.542C64.4305 63.1514 64.4305 62.5182 64.04 62.1277L39.3736 37.4609Z"
        fill="#F32345"
      />
      <path d="M55.3994 50.6565L63.7491 59.0064C64.1203 58.3675 64.333 57.6248 64.333 56.8325V45.9766L55.3994 50.6565Z" fill="#F32345" />
      <path d="M44.5757 39.832L53.918 49.1746L64.3115 43.7301C64.0932 41.5412 62.246 39.832 59.9998 39.832H44.5757Z" fill="#F32345" />
    </svg>
  ),
  circleSuccessEnvelope: ({ width = 100, height = 24, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} fill={fill} viewBox="0 0 101 100" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M100.881 50.5C100.881 22.8854 78.4955 0.5 50.8809 0.5C23.267 0.5 0.880859 22.8854 0.880859 50.5C0.880859 78.1146 23.267 100.5 50.8809 100.5C78.4955 100.5 100.881 78.1146 100.881 50.5Z"
        fill="#E4FFF1"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M51 52.416L47.684 49.667L36.115 61.51H65.591L54.235 49.647L51 52.416ZM55.89 48.39L66.892 59.829C66.955 59.607 67 59.377 67 59.134V39.362L55.89 48.39ZM35 39.316V59.134C35 59.377 35.045 59.607 35.108 59.829L46.147 48.427L35 39.316ZM66 37.5H36L51 49.519L66 37.5Z"
        fill="#24B314"
      />
    </svg>
  ),
  circledCheck: ({ height = 18, width = 19, name, fill = '#24B314', ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M9 1.30078C7.31886 1.30078 5.67547 1.7993 4.27766 2.73329C2.87984 3.66728 1.79037 4.9948 1.14703 6.54797C0.503683 8.10114 0.335355 9.81021 0.663329 11.459C0.991303 13.1079 1.80085 14.6224 2.9896 15.8112C4.17834 16.9999 5.6929 17.8095 7.34173 18.1375C8.99057 18.4654 10.6996 18.2971 12.2528 17.6538C13.806 17.0104 15.1335 15.9209 16.0675 14.5231C17.0015 13.1253 17.5 11.4819 17.5 9.80078C17.4976 7.54717 16.6013 5.38654 15.0078 3.793C13.4142 2.19946 11.2536 1.30316 9 1.30078ZM12.7318 8.30184L8.15491 12.8788C8.09418 12.9396 8.02207 12.9878 7.94269 13.0207C7.86332 13.0536 7.77824 13.0705 7.69231 13.0705C7.60638 13.0705 7.5213 13.0536 7.44193 13.0207C7.36255 12.9878 7.29044 12.9396 7.22971 12.8788L5.26818 10.9172C5.14549 10.7945 5.07656 10.6281 5.07656 10.4546C5.07656 10.2811 5.14549 10.1147 5.26818 9.99203C5.39086 9.86934 5.55726 9.80042 5.73077 9.80042C5.90428 9.80042 6.07068 9.86934 6.19337 9.99203L7.69231 11.4918L11.8066 7.37665C11.8674 7.3159 11.9395 7.26771 12.0189 7.23483C12.0982 7.20195 12.1833 7.18503 12.2692 7.18503C12.3551 7.18503 12.4402 7.20195 12.5196 7.23483C12.599 7.26771 12.6711 7.3159 12.7318 7.37665C12.7926 7.43739 12.8408 7.50951 12.8736 7.58889C12.9065 7.66826 12.9234 7.75333 12.9234 7.83924C12.9234 7.92515 12.9065 8.01022 12.8736 8.0896C12.8408 8.16897 12.7926 8.24109 12.7318 8.30184Z"
        fill={fill}
      />
    </svg>
  ),
  circledClose: ({ height = 18, width = 19, name, fill = '#F32345' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9 1.30078C7.31886 1.30078 5.67547 1.7993 4.27766 2.73329C2.87984 3.66728 1.79037 4.9948 1.14703 6.54797C0.503683 8.10114 0.335355 9.81021 0.663329 11.459C0.991303 13.1079 1.80085 14.6224 2.9896 15.8112C4.17834 16.9999 5.6929 17.8095 7.34173 18.1375C8.99057 18.4654 10.6996 18.2971 12.2528 17.6538C13.806 17.0104 15.1335 15.9209 16.0675 14.5231C17.0015 13.1253 17.5 11.4819 17.5 9.80078C17.4976 7.54717 16.6013 5.38654 15.0078 3.793C13.4142 2.19946 11.2536 1.30316 9 1.30078ZM12.078 11.9536C12.1387 12.0143 12.1869 12.0864 12.2198 12.1658C12.2527 12.2452 12.2696 12.3303 12.2696 12.4162C12.2696 12.5021 12.2527 12.5871 12.2198 12.6665C12.1869 12.7459 12.1387 12.818 12.078 12.8788C12.0172 12.9395 11.9451 12.9877 11.8657 13.0206C11.7864 13.0535 11.7013 13.0704 11.6154 13.0704C11.5295 13.0704 11.4444 13.0535 11.365 13.0206C11.2857 12.9877 11.2135 12.9395 11.1528 12.8788L9 10.7252L6.84721 12.8788C6.78647 12.9395 6.71435 12.9877 6.63497 13.0206C6.5556 13.0535 6.47053 13.0704 6.38462 13.0704C6.29871 13.0704 6.21363 13.0535 6.13426 13.0206C6.05489 12.9877 5.98277 12.9395 5.92202 12.8788C5.86127 12.818 5.81308 12.7459 5.78021 12.6665C5.74733 12.5871 5.73041 12.5021 5.73041 12.4162C5.73041 12.3303 5.74733 12.2452 5.78021 12.1658C5.81308 12.0864 5.86127 12.0143 5.92202 11.9536L8.07563 9.80078L5.92202 7.64799C5.79933 7.5253 5.73041 7.3589 5.73041 7.1854C5.73041 7.01189 5.79933 6.84549 5.92202 6.7228C6.04471 6.60011 6.21111 6.53119 6.38462 6.53119C6.55813 6.53119 6.72453 6.60011 6.84721 6.7228L9 8.8764L11.1528 6.7228C11.2135 6.66205 11.2857 6.61386 11.365 6.58098C11.4444 6.54811 11.5295 6.53119 11.6154 6.53119C11.7013 6.53119 11.7864 6.54811 11.8657 6.58098C11.9451 6.61386 12.0172 6.66205 12.078 6.7228C12.1387 6.78355 12.1869 6.85567 12.2198 6.93504C12.2527 7.01441 12.2696 7.09948 12.2696 7.1854C12.2696 7.27131 12.2527 7.35638 12.2198 7.43575C12.1869 7.51512 12.1387 7.58724 12.078 7.64799L9.92438 9.80078L12.078 11.9536Z"
        fill={fill}
      />
    </svg>
  ),
  circledInfo: ({ name, width = 20, height = 20, fill = '#FA9500', ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10ZM8.40909 9.09091C8.40909 8.58882 8.81609 8.18182 9.31818 8.18182H10.6818C11.1839 8.18182 11.5909 8.58882 11.5909 9.09091V15.4545C11.5909 15.9566 11.1839 16.3636 10.6818 16.3636H9.31818C8.81609 16.3636 8.40909 15.9566 8.40909 15.4545V9.09091ZM11.8182 5.45455C11.8182 4.45039 11.0042 3.63636 10 3.63636C8.99582 3.63636 8.18182 4.45039 8.18182 5.45455C8.18182 6.4587 8.99582 7.27273 10 7.27273C11.0042 7.27273 11.8182 6.4587 11.8182 5.45455Z"
        fill={fill}
      />
    </svg>
  ),
  circledTrash: ({ name, fill = '#F32345', stroke = '#fff', ...props }) => (
    <svg data-testid={name} width="100" height="101" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M100 50.5049C100 22.8903 77.6146 0.504883 50 0.504883C22.3861 0.504883 0 22.8903 0 50.5049C0 78.1195 22.3861 100.505 50 100.505C77.6146 100.505 100 78.1195 100 50.5049Z"
        fill={fill}
      />
      <path
        d="M42.8571 36.2216V31.9334C42.8571 31.5545 43.0077 31.1912 43.2756 30.9233C43.5435 30.6554 43.9068 30.5049 44.2857 30.5049H55.7143C56.0932 30.5049 56.4565 30.6554 56.7244 30.9233C56.9923 31.1912 57.1429 31.5545 57.1429 31.9334V36.2216H68.5714C68.9503 36.2216 69.3137 36.3721 69.5816 36.64C69.8495 36.9079 70 37.2712 70 37.6501C70 38.0289 69.8495 38.3923 69.5816 38.6602C69.3137 38.9281 68.9503 39.0786 68.5714 39.0786H31.4286C31.0497 39.0786 30.6863 38.9281 30.4184 38.6602C30.1505 38.3923 30 38.0289 30 37.6501C30 37.2712 30.1505 36.9079 30.4184 36.64C30.6863 36.3721 31.0497 36.2216 31.4286 36.2216H42.8571ZM45.7143 36.2216H54.2857V33.3647H45.7143V36.2216ZM35.7143 70.5049C35.3354 70.5049 34.972 70.3544 34.7041 70.0865C34.4362 69.8186 34.2857 69.4553 34.2857 69.0764V39.0786H65.7143V69.0764C65.7143 69.4553 65.5638 69.8186 65.2959 70.0865C65.028 70.3544 64.6646 70.5049 64.2857 70.5049H35.7143ZM45.7143 61.9341C46.0932 61.9341 46.4565 61.7836 46.7244 61.5157C46.9923 61.2478 47.1429 60.8844 47.1429 60.5056V46.2209C47.1429 45.842 46.9923 45.4787 46.7244 45.2108C46.4565 44.9429 46.0932 44.7924 45.7143 44.7924C45.3354 44.7924 44.972 44.9429 44.7041 45.2108C44.4362 45.4787 44.2857 45.842 44.2857 46.2209V60.5056C44.2857 60.8844 44.4362 61.2478 44.7041 61.5157C44.972 61.7836 45.3354 61.9341 45.7143 61.9341ZM54.2857 61.9341C54.6646 61.9341 55.028 61.7836 55.2959 61.5157C55.5638 61.2478 55.7143 60.8844 55.7143 60.5056V46.2209C55.7143 45.842 55.5638 45.4787 55.2959 45.2108C55.028 44.9429 54.6646 44.7924 54.2857 44.7924C53.9068 44.7924 53.5435 44.9429 53.2756 45.2108C53.0077 45.4787 52.8571 45.842 52.8571 46.2209V60.5056C52.8571 60.8844 53.0077 61.2478 53.2756 61.5157C53.5435 61.7836 53.9068 61.9341 54.2857 61.9341Z"
        fill={stroke}
      />
    </svg>
  ),
  circledCaution: ({ name, ...props }) => (
    <svg data-testid={name} width="80" height="100" viewBox="0 0 80 100" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <circle cx="40" cy="50" r="40" fill="#FFF8E1" />
      <g clipPath="url(#clip0_42688_127798)">
        <path
          d="M56.2105 61.6792L41.3355 36.1791C40.9547 35.5264 40.2559 35.1249 39.5 35.1249C38.7441 35.1249 38.0453 35.5264 37.6644 36.1791L22.7894 61.6792C22.4061 62.3365 22.4034 63.1484 22.7822 63.8084C23.1614 64.4683 23.8639 64.8751 24.625 64.8751H54.375C55.1361 64.8751 55.8386 64.4683 56.2178 63.8084C56.5965 63.1484 56.5939 62.3365 56.2105 61.6792ZM39.5 60.6248C38.3275 60.6248 37.375 59.6733 37.375 58.4998C37.375 57.3263 38.3275 56.3748 39.5 56.3748C40.6746 56.3748 41.625 57.3263 41.625 58.4998C41.625 59.6733 40.6746 60.6248 39.5 60.6248ZM41.625 52.1249C41.625 53.2985 40.6735 54.2499 39.5 54.2499C38.3264 54.2499 37.375 53.2985 37.375 52.1249V45.7499C37.375 44.5764 38.3264 43.6249 39.5 43.6249C40.6735 43.6249 41.625 44.5764 41.625 45.7499V52.1249Z"
          fill="#FA9500"
        />
      </g>
      <defs>
        <clipPath id="clip0_42688_127798">
          <rect width="34" height="34" fill="white" transform="translate(22.5 33)" />
        </clipPath>
      </defs>
    </svg>
  ),
  circledTrashOutline: ({ name, fill = '#F1F6FA', stroke = '#414F5F', strokeWidth, ...props }) => (
    <svg data-testid={name} width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="44" height="44" rx="22" fill={fill} />
      <path d="M13 16H15H31" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M18 16V14C18 13.4696 18.2107 12.9609 18.5858 12.5858C18.9609 12.2107 19.4696 12 20 12H24C24.5304 12 25.0391 12.2107 25.4142 12.5858C25.7893 12.9609 26 13.4696 26 14V16M29 16V30C29 30.5304 28.7893 31.0391 28.4142 31.4142C28.0391 31.7893 27.5304 32 27 32H17C16.4696 32 15.9609 31.7893 15.5858 31.4142C15.2107 31.0391 15 30.5304 15 30V16H29Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M20 21V27" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M24 21V27" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  copy: ({ width = 24, stroke = '#2376F3', height = 24, strokeWidth = 2, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 14 14" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.5835 1.1665H3.50016C3.19074 1.1665 2.894 1.28942 2.6752 1.50821C2.45641 1.72701 2.3335 2.02375 2.3335 2.33317V11.6665C2.3335 11.9759 2.45641 12.2727 2.6752 12.4915C2.894 12.7103 3.19074 12.8332 3.50016 12.8332H10.5002C10.8096 12.8332 11.1063 12.7103 11.3251 12.4915C11.5439 12.2727 11.6668 11.9759 11.6668 11.6665V5.24984L7.5835 1.1665Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M7.5835 1.1665V5.24984H11.6668" stroke={stroke} strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  correct: ({ width = 20, height = 21, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 20 21" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path d="M16.6666 5.5L7.49998 14.6667L3.33331 10.5" stroke="#36C9A8" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  downloadAttestation: ({ width = 24, height = 30, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 24 30" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M22.4279 4H1.57205C0.70386 4 0 4.8598 0 5.92034V22.0512C0 23.1118 0.70386 23.9716 1.57205 23.9716H8.49432L10.797 27.3157C11.0957 27.7495 11.536 28 12.0001 28C12.4641 28 12.9045 27.7496 13.2031 27.3158L15.5058 23.9716H22.4279C23.2962 23.9716 24 23.1118 24 22.0513V5.92041C24 4.85974 23.2962 4 22.4279 4ZM4.47521 9.4708H11.6543C12.0883 9.4708 12.4403 9.9007 12.4403 10.431C12.4403 10.9612 12.0883 11.3911 11.6543 11.3911H4.47521C4.04112 11.3911 3.68919 10.9612 3.68919 10.431C3.68919 9.9007 4.04112 9.4708 4.47521 9.4708ZM19.4935 17.2802H4.50655C4.07245 17.2802 3.72052 16.8503 3.72052 16.32C3.72052 15.7898 4.07245 15.3599 4.50655 15.3599H19.4935C19.9275 15.3599 20.2795 15.7898 20.2795 16.32C20.2795 16.8503 19.9275 17.2802 19.4935 17.2802Z"
        fill="#FA9500"
      />
    </svg>
  ),
  down: ({ width = 21, height = 20, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 21 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16.3208 7.12875L11.3796 13.32C10.9108 13.8863 10.1883 13.8863 9.73895 13.32L4.7977 7.12875C4.32895 6.5425 4.56333 6.09375 5.28583 6.09375H15.8327C16.5752 6.09375 16.7896 6.54313 16.3208 7.12875Z"
        fill="#2376F3"
      />
    </svg>
  ),
  details: ({ width = 24, height = 24, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 25 25" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.19141 12.8359C7.19141 13.9405 6.29598 14.8359 5.19141 14.8359C4.08684 14.8359 3.19141 13.9405 3.19141 12.8359C3.19141 11.7313 4.08684 10.8359 5.19141 10.8359C6.29598 10.8359 7.19141 11.7313 7.19141 12.8359Z"
        fill="#2376F3"
      />
      <path
        d="M14.1914 12.8359C14.1914 13.9405 13.296 14.8359 12.1914 14.8359C11.0868 14.8359 10.1914 13.9405 10.1914 12.8359C10.1914 11.7313 11.0868 10.8359 12.1914 10.8359C13.296 10.8359 14.1914 11.7313 14.1914 12.8359Z"
        fill="#2376F3"
      />
      <path
        d="M21.1914 12.8359C21.1914 13.9405 20.296 14.8359 19.1914 14.8359C18.0868 14.8359 17.1914 13.9405 17.1914 12.8359C17.1914 11.7313 18.0868 10.8359 19.1914 10.8359C20.296 10.8359 21.1914 11.7313 21.1914 12.8359Z"
        fill="#2376F3"
      />
    </svg>
  ),
  file: ({ width = 18, height = 21, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 18 21" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18 7.40723V19.4002C18.0009 19.5315 17.976 19.6618 17.9266 19.7834C17.8772 19.9051 17.8043 20.0159 17.7121 20.1094C17.6199 20.2029 17.5101 20.2773 17.3892 20.3284C17.2682 20.3795 17.1383 20.4063 17.007 20.4072H0.993C0.729813 20.4072 0.477391 20.3027 0.291196 20.1167C0.105001 19.9307 0.000265042 19.6784 0 19.4152V1.39923C0 0.862227 0.447 0.407227 0.998 0.407227H11V6.40723C11 6.67244 11.1054 6.9268 11.2929 7.11433C11.4804 7.30187 11.7348 7.40723 12 7.40723H18ZM18 5.40723H13V0.410227L18 5.40723Z"
        fill="#292B2C"
      />
    </svg>
  ),
  halfAndFullCircle: ({ width = 22, height = 16, fill = '#FA9500', ...props }) => (
    <svg width={width} height={height} viewBox="0 0 22 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M7.5 0.799805C7.86426 0.799805 8.22168 0.826172 8.57129 0.875977C6.39453 2.61719 5 5.29492 5 8.2998C5 11.3047 6.39453 13.9824 8.57129 15.7236C8.22168 15.7734 7.86426 15.7998 7.5 15.7998C3.35742 15.7998 0 12.4424 0 8.2998C0 4.15723 3.35742 0.799805 7.5 0.799805Z"
        fill={fill}
      />
      <path
        d="M14.5 15.7998C18.6426 15.7998 22 12.4424 22 8.2998C22 4.15723 18.6426 0.799805 14.5 0.799805C10.3574 0.799805 7 4.15723 7 8.2998C7 12.4424 10.3574 15.7998 14.5 15.7998Z"
        fill={fill}
      />
    </svg>
  ),
  halfCircle: ({ width = 8, height = 16, fill = '#FA9500', ...props }) => (
    <svg width={width} height={height} viewBox="0 0 8 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M7.5 15.7998V0.799805C3.59082 1.05721 0.5 4.31638 0.5 8.29981C0.5 12.2832 3.59082 15.5424 7.5 15.7998Z" fill={fill} />
    </svg>
  ),

  lightbulb: ({ width = 20, height = 20, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 20 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_28475_46086)">
        <path
          d="M12.6059 14.7826H7.3885C7.02847 14.7826 6.73632 14.4907 6.73632 14.1304C6.73632 13.2313 6.35374 11.9686 5.71389 11.4122C3.96077 9.88899 3.16589 7.60445 3.58757 5.30145C4.14889 2.23254 6.84206 0.00296875 9.99167 0C9.99335 0 9.9955 0 9.99761 0C11.7384 0 13.3753 0.677422 14.607 1.90813C15.84 3.14031 16.5189 4.77879 16.5189 6.52172C16.5189 8.39547 15.7016 10.1794 14.2767 11.4158C13.6389 11.969 13.2581 13.2305 13.2581 14.1304C13.2581 14.4907 12.9659 14.7826 12.6059 14.7826Z"
          fill="#FFD15D"
        />
        <path
          d="M9.99594 14.7835C9.6359 14.7835 9.34375 14.4916 9.34375 14.1313V8.91391C9.34375 8.55363 9.6359 8.26172 9.99594 8.26172C10.356 8.26172 10.6481 8.55363 10.6481 8.91391V14.1313C10.6481 14.4916 10.356 14.7835 9.99594 14.7835Z"
          fill="#FF9F19"
        />
        <path
          d="M6.15322 6.42136C6.11416 6.42136 6.07467 6.41777 6.03517 6.41054C5.68064 6.34582 5.44584 6.00589 5.5108 5.6516C5.84705 3.81336 7.32338 2.35613 9.18435 2.02558C9.54017 1.96336 9.87771 2.19921 9.94057 2.55375C10.0038 2.90828 9.76736 3.24687 9.41279 3.30996C8.0851 3.54562 7.03252 4.58097 6.79392 5.8864C6.73623 6.20105 6.46189 6.42136 6.15322 6.42136Z"
          fill="#F2FAFF"
        />
        <path
          d="M11.3 16.0859H8.69125C8.33039 16.0859 8.03906 16.3772 8.03906 16.7381V18.0425C8.03906 19.1207 8.9173 19.999 9.99559 19.999C11.0043 19.999 11.8347 19.2338 11.9434 18.2512C11.9477 18.1816 11.9521 18.112 11.9521 18.0425V16.7381C11.9521 16.3772 11.6608 16.0859 11.3 16.0859Z"
          fill="#485566"
        />
        <path
          d="M12.6082 14.7826C12.9683 14.7826 13.2604 14.4907 13.2604 14.1304C13.2604 13.2305 13.6413 11.969 14.279 11.4158C15.7039 10.1793 16.5213 8.39543 16.5213 6.52172C16.5213 4.77875 15.8424 3.14027 14.6093 1.90813C13.3776 0.677422 11.7408 0 10 0V14.7826H12.6082Z"
          fill="#F9B54C"
        />
        <path
          d="M11.9565 16.7381V18.0425C11.9565 18.112 11.9522 18.1816 11.9478 18.2512C11.8391 19.2338 11.0087 19.999 10 19.999V16.0859H11.3043C11.6652 16.0859 11.9565 16.3772 11.9565 16.7381Z"
          fill="#36404D"
        />
        <path
          d="M10 14.7834C10.3599 14.7832 10.6518 14.4914 10.6518 14.1313V8.91387C10.6518 8.55375 10.3599 8.26195 10 8.26172V14.7834Z"
          fill="#F28618"
        />
        <path
          d="M11.2996 17.3937H8.6909C7.61035 17.3937 6.73438 16.5177 6.73438 15.4371V14.1328H13.2561V15.4371C13.2561 16.5177 12.3802 17.3937 11.2996 17.3937Z"
          fill="#576573"
        />
        <path d="M13.2604 15.4371V14.1328H10V17.3937H11.3039C12.3845 17.3937 13.2604 16.5177 13.2604 15.4371Z" fill="#485566" />
        <path
          d="M11.9434 7.40453C11.8391 6.42191 11.0043 5.65234 9.99559 5.65234C8.91734 5.65234 8.03906 6.53059 8.03906 7.60887C8.03906 8.68715 8.9173 9.56539 9.99559 9.56539C11.0739 9.56539 11.9521 8.68715 11.9521 7.60887C11.9521 7.53934 11.9477 7.4741 11.9434 7.40453ZM9.99555 8.26105C9.63469 8.26105 9.34336 7.96976 9.34336 7.60887C9.34336 7.24801 9.63465 6.95668 9.99555 6.95668C10.3564 6.95668 10.6477 7.24797 10.6477 7.60887C10.6477 7.96976 10.3564 8.26105 9.99555 8.26105Z"
          fill="#FF9F19"
        />
        <path
          d="M11.9565 7.60887C11.9565 8.68711 11.0783 9.56539 10 9.56539V8.26105C10.3609 8.26105 10.6522 7.96977 10.6522 7.60887C10.6522 7.24801 10.3609 6.95668 10 6.95668V5.65234C11.0087 5.65234 11.8435 6.42191 11.9478 7.40453C11.9522 7.47406 11.9565 7.5393 11.9565 7.60887Z"
          fill="#F28618"
        />
      </g>
      <defs>
        <clipPath id="clip0_28475_46086">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  message: ({ width = 20, height = 20, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 24 24" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M22.4279 0H1.57205C0.70386 0 0 0.859801 0 1.92034V18.0512C0 19.1118 0.70386 19.9716 1.57205 19.9716H8.49432L10.797 23.3157C11.0957 23.7495 11.536 24 12.0001 24C12.4641 24 12.9045 23.7496 13.2031 23.3158L15.5058 19.9716H22.4279C23.2962 19.9716 24 19.1118 24 18.0513V1.92041C24 0.859738 23.2962 0 22.4279 0ZM4.47521 5.4708H11.6543C12.0883 5.4708 12.4403 5.9007 12.4403 6.43097C12.4403 6.96124 12.0883 7.39114 11.6543 7.39114H4.47521C4.04112 7.39114 3.68919 6.96124 3.68919 6.43097C3.68919 5.9007 4.04112 5.4708 4.47521 5.4708ZM19.4935 13.2802H4.50655C4.07245 13.2802 3.72052 12.8503 3.72052 12.32C3.72052 11.7898 4.07245 11.3599 4.50655 11.3599H19.4935C19.9275 11.3599 20.2795 11.7898 20.2795 12.32C20.2795 12.8503 19.9275 13.2802 19.4935 13.2802Z"
        fill="#FA9500"
      />
    </svg>
  ),
  pageLoading: ({ width = 16, height = 4, fill = '#414F5F' }) => (
    <svg width={width} height={height} viewBox="0 0 16 4" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect y="0.75" width="3" height="3" rx="1" fill={fill} />
      <rect x="6.25" y="0.75" width="3" height="3" rx="1" fill={fill} fillOpacity="0.5" />
      <rect x="12.5" y="0.75" width="3" height="3" rx="1" fill={fill} fillOpacity="0.5" />
    </svg>
  ),
  pen: props => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M12 20H21" stroke="#F9FBFD" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M16.5 3.50023C16.8978 3.1024 17.4374 2.87891 18 2.87891C18.2786 2.87891 18.5544 2.93378 18.8118 3.04038C19.0692 3.14699 19.303 3.30324 19.5 3.50023C19.697 3.69721 19.8532 3.93106 19.9598 4.18843C20.0665 4.4458 20.1213 4.72165 20.1213 5.00023C20.1213 5.2788 20.0665 5.55465 19.9598 5.81202C19.8532 6.06939 19.697 6.30324 19.5 6.50023L7 19.0002L3 20.0002L4 16.0002L16.5 3.50023Z"
        stroke="#F9FBFD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  plus: ({ name, stroke = '#fff', strokeWidth = 2, ...props }) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" data-testid={name} {...props}>
      <path d="M10 4.16797V15.8346" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M4.16602 10H15.8327" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  search: ({ width = 24, stroke = '#AABDCE', height = 24, strokeWidth = 2, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} fill={fill} viewBox="0 0 24 25" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.5 18.5C14.6421 18.5 18 15.1421 18 11C18 6.85786 14.6421 3.5 10.5 3.5C6.35786 3.5 3 6.85786 3 11C3 15.1421 6.35786 18.5 10.5 18.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M21 21.4998L15.8 16.2998" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  star: ({ width = 16, height = 16 }) => (
    <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        opacity="0.9"
        d="M15.1622 7.79592L12.8966 6.96022C11.0627 6.28373 9.61716 4.83781 8.94114 3.00341L8.1054 0.737682C8.07416 0.652279 7.99282 0.595703 7.90206 0.595703C7.81129 0.595703 7.72996 0.652279 7.69871 0.737412L6.86324 3.00287C6.18695 4.83728 4.74117 6.28346 2.90729 6.95995L0.641669 7.79565C0.55656 7.82691 0.5 7.90827 0.5 7.99906C0.5 8.08985 0.55656 8.17121 0.641669 8.20246L2.90702 9.03844C4.74118 9.71492 6.18695 11.1611 6.86324 12.9958L7.69871 15.2615C7.72996 15.3466 7.81129 15.4032 7.90206 15.4032C7.99282 15.4032 8.07416 15.3466 8.1054 15.2615L8.94114 12.9955C9.61743 11.1608 11.0629 9.71492 12.8971 9.03871L15.1624 8.20273C15.2476 8.17148 15.3041 8.09012 15.3041 7.99933C15.3041 7.90854 15.2476 7.82718 15.1622 7.79592Z"
        fill="#FA9500"
      />
    </svg>
  ),
  upIcon: ({ width = 13, height = 8, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 13 8" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0.679171 6.87125L5.62042 0.68C6.08917 0.113751 6.81167 0.11375 7.26105 0.68L12.2023 6.87125C12.671 7.4575 12.4367 7.90625 11.7142 7.90625L1.1673 7.90625C0.424797 7.90625 0.210422 7.45688 0.679171 6.87125Z"
        fill="#2376F3"
      />
    </svg>
  ),
  upload: ({ width = 20, height = 20, fill = 'none' }) => (
    <svg width={width} height={height} viewBox="0 0 20 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.0711 2.92892C15.1823 1.04015 12.6711 0 10 0C7.32893 0 4.81769 1.04015 2.92892 2.92892C1.04015 4.81769 0 7.32893 0 10C0 12.6711 1.04015 15.1823 2.92892 17.0711C4.81769 18.9598 7.32889 20 10 20C12.6711 20 15.1823 18.9598 17.0711 17.0711C18.9598 15.1823 20 12.6711 20 10C20 7.32889 18.9598 4.81769 17.0711 2.92892ZM14.4197 9.0989C14.32 9.33945 14.0853 9.49632 13.8249 9.49632H12.1737V14.9724C12.1737 15.328 11.8855 15.6162 11.53 15.6162H8.47003C8.1145 15.6162 7.82626 15.328 7.82626 14.9724V9.49632H6.17504C5.91466 9.49632 5.67994 9.3395 5.58029 9.0989C5.48068 8.85835 5.53574 8.58144 5.71986 8.39732L9.54477 4.57237C9.6655 4.45164 9.82923 4.38379 9.99996 4.38379C10.1707 4.38379 10.3345 4.4516 10.4551 4.57237L14.2801 8.39732C14.4642 8.58144 14.5193 8.8583 14.4197 9.0989Z"
        fill="#2376F3"
      />
    </svg>
  ),
  hourGlass: ({ width = 32, height = 44, fill = 'none' }) => (
    <svg opacity={0.6} width={width} height={height} viewBox="0 0 32 44" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M31.2301 10.9238V3.38462C31.2301 2.48696 30.8735 1.62607 30.2388 0.991331C29.604 0.356592 28.7431 0 27.8455 0H4.15317C3.25551 0 2.39462 0.356592 1.75989 0.991331C1.12515 1.62607 0.768555 2.48696 0.768555 3.38462V11C0.769698 11.5252 0.892537 12.0431 1.12743 12.5129C1.36233 12.9827 1.70289 13.3916 2.1224 13.7077L13.1795 22L2.1224 30.2923C1.70289 30.6084 1.36233 31.0173 1.12743 31.4871C0.892537 31.9569 0.769698 32.4748 0.768555 33V40.6154C0.768555 41.513 1.12515 42.3739 1.75989 43.0087C2.39462 43.6434 3.25551 44 4.15317 44H27.8455C28.7431 44 29.604 43.6434 30.2388 43.0087C30.8735 42.3739 31.2301 41.513 31.2301 40.6154V33.0762C31.2289 32.5529 31.1071 32.037 30.8741 31.5685C30.6411 31.1 30.3033 30.6915 29.8868 30.3748L18.8064 22L29.8868 13.6252C30.3033 13.3085 30.6411 12.9 30.8741 12.4315C31.1071 11.963 31.2289 11.4471 31.2301 10.9238ZM27.8455 33.0762V40.6154H4.15317V33L14.307 25.3846V30.4615C14.307 30.9104 14.4853 31.3408 14.8027 31.6582C15.1201 31.9756 15.5505 32.1538 15.9993 32.1538C16.4482 32.1538 16.8786 31.9756 17.196 31.6582C17.5133 31.3408 17.6916 30.9104 17.6916 30.4615V25.4015L27.8455 33.0762ZM27.8455 10.9238L26.6249 11.8462H5.28067L4.15317 11V3.38462H27.8455V10.9238Z"
        fill="#AABDCE"
      />
    </svg>
  ),
  send: ({ width = 18, height = 17, stroke = 'hsla(216, 90%, 55%, 1)' }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} fill="none" viewBox="0 0 25 24">
      <path stroke={stroke} strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="m22.5 2-11 11M22.5 2l-7 20-4-9-9-4 20-7Z" />
    </svg>
  ),
  trashCan: ({ fill = '#C10B28', ...props }) => (
    <svg width="20" height="24" viewBox="0 0 20 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M19.0009 4.61418H13.2317V2.76803C13.2317 1.75264 12.4009 0.921875 11.3855 0.921875H8.61629C7.6009 0.921875 6.77013 1.75264 6.77013 2.76803V4.61418H1.0009C0.631671 4.61418 0.308594 4.93726 0.308594 5.30649V6.69111C0.308594 7.06034 0.631671 7.38341 1.0009 7.38341H19.0009C19.3701 7.38341 19.6932 7.06034 19.6932 6.69111V5.30649C19.6932 4.93726 19.3701 4.61418 19.0009 4.61418ZM8.61629 3.22957C8.61629 2.95264 8.8009 2.76803 9.07782 2.76803H10.924C11.2009 2.76803 11.3855 2.95264 11.3855 3.22957V4.61418H8.61629V3.22957Z"
        fill={fill}
      />
      <path
        d="M17.1543 9.23047H2.8466C2.47737 9.23047 2.1543 9.55355 2.1543 9.92278V20.7689C2.1543 22.0612 3.16968 23.0766 4.46199 23.0766H15.5389C16.8312 23.0766 17.8466 22.0612 17.8466 20.7689V9.92278C17.8466 9.55355 17.5235 9.23047 17.1543 9.23047ZM8.61584 19.3843C8.61584 19.6612 8.43122 19.8459 8.1543 19.8459H7.23122C6.9543 19.8459 6.76968 19.6612 6.76968 19.3843V12.9228C6.76968 12.6459 6.9543 12.4612 7.23122 12.4612H8.1543C8.43122 12.4612 8.61584 12.6459 8.61584 12.9228V19.3843ZM13.2312 19.3843C13.2312 19.6612 13.0466 19.8459 12.7697 19.8459H11.8466C11.5697 19.8459 11.3851 19.6612 11.3851 19.3843V12.9228C11.3851 12.6459 11.5697 12.4612 11.8466 12.4612H12.7697C13.0466 12.4612 13.2312 12.6459 13.2312 12.9228V19.3843Z"
        fill={fill}
      />
    </svg>
  ),

  threeDots: ({ name, width = 24, height = 24, fill = '#2376F3', ...props }) => (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-testid={name} {...props}>
      <path d="M6 12C6 13.6569 4.65692 15 3 15C1.34308 15 0 13.6569 0 12C0 10.3431 1.34308 9 3 9C4.65692 9 6 10.3431 6 12Z" fill={fill} />
      <path
        d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z"
        fill={fill}
      />
      <path
        d="M24 12C24 13.6569 22.6569 15 21 15C19.3431 15 18 13.6569 18 12C18 10.3431 19.3431 9 21 9C22.6569 9 24 10.3431 24 12Z"
        fill={fill}
      />
    </svg>
  ),
  thumbsUp: ({ width = 32, height = 32 }) => (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_34167_88242)">
        <path
          d="M27.3654 9.78075H20.5241V6.0491C20.5241 4.33213 19.1321 2.93945 17.4145 2.93945H16.7927C15.0753 2.93945 13.6829 4.33213 13.6829 6.0491V9.78064H10.5732V29.0604H23.6338C26.7253 29.0604 29.7074 26.6 30.2948 23.5651L31.8992 15.2759C32.4866 12.2411 30.4567 9.78075 27.3654 9.78075Z"
          fill="#2376F3"
        />
        <path
          d="M0 15.3787V23.4637C0 26.5552 2.5057 29.061 5.59742 29.061H8.08507V9.78125H5.59742C2.5057 9.78125 0 12.2868 0 15.3787ZM6.2193 26.5733C3.10965 26.5733 2.22837 25.6694 2.22837 22.8419H6.2193V26.5733Z"
          fill="#2376F3"
        />
      </g>
      <defs>
        <clipPath id="clip0_34167_88242">
          <rect width="32" height="32" fill="none" />
        </clipPath>
      </defs>
    </svg>
  ),
  linkHook: ({ width = 32, height = 32 }) => (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 24C6.34314 24 3.51472 24 1.75736 22.2426C-1.43051e-07 20.4853 0 17.6568 0 12C0 6.34314 -1.43051e-07 3.51472 1.75736 1.75736C3.51472 -1.43051e-07 6.34314 0 12 0C17.6568 0 20.4853 -1.43051e-07 22.2426 1.75736C24 3.51472 24 6.34314 24 12C24 17.6568 24 20.4853 22.2426 22.2426C20.4853 24 17.6568 24 12 24ZM8.6376 6.3H8.76244C9.8406 6.29996 10.7396 6.29994 11.4534 6.39589C12.2068 6.49718 12.8869 6.71999 13.4334 7.26655C13.98 7.81312 14.2028 8.49328 14.3041 9.24662C14.4001 9.96036 14.4 10.8594 14.4 11.9376V12C14.4 12.497 13.997 12.9 13.5 12.9C13.003 12.9 12.6 12.497 12.6 12C12.6 10.8432 12.5981 10.0661 12.5202 9.48648C12.4457 8.93272 12.317 8.69573 12.1607 8.53934C12.0043 8.38296 11.7673 8.2543 11.2135 8.17985C10.6339 8.10192 9.8568 8.1 8.7 8.1C7.54319 8.1 6.76612 8.10192 6.18647 8.17985C5.63272 8.2543 5.39573 8.38296 5.23934 8.53934C5.08296 8.69573 4.9543 8.93272 4.87985 9.48648C4.80192 10.0661 4.8 10.8432 4.8 12C4.8 13.1568 4.80192 13.9339 4.87985 14.5135C4.9543 15.0673 5.08296 15.3043 5.23934 15.4607C5.39573 15.617 5.63272 15.7457 6.18647 15.8202C6.76612 15.8981 7.54319 15.9 8.7 15.9C9.19706 15.9 9.6 16.303 9.6 16.8C9.6 17.297 9.19706 17.7 8.7 17.7H8.63758C7.55941 17.7 6.66036 17.7001 5.94662 17.6041C5.19328 17.5028 4.51312 17.28 3.96655 16.7334C3.41999 16.1869 3.19718 15.5068 3.09589 14.7534C2.99994 14.0396 2.99996 13.1406 3 12.0624V11.9376C2.99996 10.8594 2.99994 9.96036 3.09589 9.24662C3.19718 8.49328 3.41999 7.81312 3.96655 7.26655C4.51312 6.71999 5.19328 6.49718 5.94662 6.39589C6.66035 6.29994 7.55945 6.29996 8.6376 6.3ZM17.8135 8.17985C17.2339 8.10192 16.4568 8.1 15.3 8.1C14.803 8.1 14.4 7.69706 14.4 7.2C14.4 6.70295 14.803 6.3 15.3 6.3H15.3624C16.4406 6.29996 17.3396 6.29994 18.0534 6.39589C18.8068 6.49718 19.4869 6.71999 20.0334 7.26655C20.58 7.81312 20.8028 8.49328 20.9041 9.24662C21.0001 9.96036 21 10.8594 21 11.9375V12.0624C21 13.1406 21.0001 14.0396 20.9041 14.7534C20.8028 15.5068 20.58 16.1869 20.0334 16.7334C19.4869 17.28 18.8068 17.5028 18.0534 17.6041C17.3396 17.7001 16.4406 17.7 15.3624 17.7H15.2376C14.1594 17.7 13.2604 17.7001 12.5466 17.6041C11.7932 17.5028 11.1131 17.28 10.5666 16.7334C10.02 16.1869 9.79716 15.5068 9.69588 14.7534C9.59994 14.0396 9.59996 13.1406 9.6 12.0624V12C9.6 11.503 10.003 11.1 10.5 11.1C10.997 11.1 11.4 11.503 11.4 12C11.4 13.1568 11.4019 13.9339 11.4798 14.5135C11.5543 15.0673 11.683 15.3043 11.8393 15.4607C11.9957 15.617 12.2327 15.7457 12.7865 15.8202C13.3661 15.8981 14.1432 15.9 15.3 15.9C16.4568 15.9 17.2339 15.8981 17.8135 15.8202C18.3673 15.7457 18.6043 15.617 18.7607 15.4607C18.917 15.3043 19.0457 15.0673 19.1202 14.5135C19.1981 13.9339 19.2 13.1568 19.2 12C19.2 10.8432 19.1981 10.0661 19.1202 9.48648C19.0457 8.93272 18.917 8.69573 18.7607 8.53934C18.6043 8.38296 18.3673 8.2543 17.8135 8.17985Z"
        fill="#5C33F3"
      />
    </svg>
  ),
  userProfile: ({ width = 25, height = 24 }) => (
    <svg width={width} height={height} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.333 12C24.333 18.6274 18.9604 24 12.333 24C5.70559 24 0.333008 18.6274 0.333008 12C0.333008 5.37258 5.70559 0 12.333 0C18.9604 0 24.333 5.37258 24.333 12ZM15.933 8.4C15.933 10.3883 14.3213 12 12.333 12C10.3447 12 8.73301 10.3883 8.73301 8.4C8.73301 6.41178 10.3447 4.8 12.333 4.8C14.3213 4.8 15.933 6.41178 15.933 8.4ZM12.333 22.2C14.4738 22.2 16.4606 21.5405 18.1013 20.4134C18.826 19.9157 19.1356 18.9674 18.7144 18.1958C17.841 16.5964 16.0412 15.6 12.3329 15.6C8.62464 15.6 6.82497 16.5962 5.95158 18.1958C5.53027 18.9674 5.83993 19.9156 6.56456 20.4133C8.20522 21.5404 10.1921 22.2 12.333 22.2Z"
        fill="#FA9500"
      />
    </svg>
  ),
  briefcase: ({ width = 24, height = 24, fill = '#24B314' }) => (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.2934 1.33115e-06H12.1731C11.1339 -3.33676e-05 10.2673 -5.64621e-05 9.5794 0.0924274C8.85328 0.190058 8.19771 0.404809 7.6709 0.931618C7.14409 1.45843 6.92934 2.114 6.83171 2.84012C6.76541 3.33327 6.74665 4.51254 6.74135 5.52366C4.40236 5.60005 2.99801 5.8731 2.02206 6.84904C0.666992 8.20413 0.666992 10.3851 0.666992 14.747C0.666992 19.1089 0.666992 21.2899 2.02206 22.6449C3.37713 24 5.55809 24 9.91998 24H14.5465C18.9084 24 21.0894 24 22.4444 22.6449C23.7995 21.2899 23.7995 19.1089 23.7995 14.747C23.7995 10.3851 23.7995 8.20413 22.4444 6.84904C21.4685 5.8731 20.0642 5.60005 17.7251 5.52366C17.7198 4.51254 17.7011 3.33327 17.6348 2.84012C17.5372 2.114 17.3224 1.45843 16.7956 0.931618C16.2688 0.404809 15.6133 0.190058 14.8871 0.0924274C14.1992 -5.64621e-05 13.3326 -3.33676e-05 12.2934 1.33115e-06ZM15.9899 5.49615C15.9847 4.51689 15.968 3.46302 15.9154 3.07131C15.8435 2.53756 15.7196 2.30914 15.5688 2.15841C15.4181 2.00767 15.1897 1.88366 14.6559 1.8119C14.0973 1.73679 13.3482 1.73494 12.2333 1.73494C11.1183 1.73494 10.3692 1.73679 9.81057 1.8119C9.27684 1.88366 9.04842 2.00767 8.89768 2.15841C8.74695 2.30914 8.62294 2.53756 8.55118 3.07131C8.49851 3.46302 8.48187 4.51689 8.47663 5.49615C8.92935 5.49398 9.40976 5.49398 9.92 5.49398H14.5465C15.0568 5.49398 15.5372 5.49398 15.9899 5.49615ZM12.2333 9.25301C12.7123 9.25301 13.1007 9.6414 13.1007 10.1205V10.1323C14.3601 10.4495 15.414 11.4425 15.414 12.8192C15.414 13.2983 15.0256 13.6867 14.5465 13.6867C14.0674 13.6867 13.679 13.2983 13.679 12.8192C13.679 12.3751 13.1865 11.7591 12.2333 11.7591C11.28 11.7591 10.7875 12.3751 10.7875 12.8192C10.7875 13.2635 11.28 13.8795 12.2333 13.8795C13.8351 13.8795 15.414 14.9896 15.414 16.6747C15.414 18.0515 14.3601 19.0444 13.1007 19.3617V19.3735C13.1007 19.8526 12.7123 20.241 12.2333 20.241C11.7542 20.241 11.3658 19.8526 11.3658 19.3735V19.3617C10.1065 19.0444 9.05253 18.0515 9.05253 16.6747C9.05253 16.1957 9.44092 15.8073 9.92 15.8073C10.3991 15.8073 10.7875 16.1957 10.7875 16.6747C10.7875 17.1189 11.28 17.7349 12.2333 17.7349C13.1865 17.7349 13.679 17.1189 13.679 16.6747C13.679 16.2305 13.1865 15.6145 12.2333 15.6145C10.6314 15.6145 9.05253 14.5044 9.05253 12.8192C9.05253 11.4425 10.1065 10.4495 11.3658 10.1323V10.1205C11.3658 9.6414 11.7542 9.25301 12.2333 9.25301Z"
        fill={fill}
      />
    </svg>
  ),
  circleSpinner: ({ width = 32, height = 32 }) => (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.3682 26.1503C30.7165 23.2889 32 19.7017 32 16H28C28 22.6274 22.6274 28 16 28C9.37259 28 4 22.6274 4 16C4 9.37261 9.37259 4.00002 16 4.00002C19.3137 4.00002 22.3137 5.34317 24.4853 7.51474L27.3137 4.68632C24.6962 2.06883 21.2521 0.439897 17.5683 0.0770692C13.8844 -0.285759 10.1887 0.639966 7.11088 2.69651C4.03304 4.75306 1.7635 7.81318 0.688958 11.3555C-0.385584 14.8978 -0.198641 18.703 1.21793 22.123C2.63451 25.5429 5.19306 28.3658 8.45765 30.1108C11.7222 31.8557 15.4909 32.4148 19.1214 31.6926C22.752 30.9704 26.0198 29.0118 28.3682 26.1503Z"
        fill="#2376F3"
      />
    </svg>
  ),
  gear: ({ width = 24, height = 24, stroke = '#3E4B5B', className, ...otherProps }) => (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...otherProps}
    >
      <path
        d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15V15Z"
        stroke="#3E4B5B"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  arrowDownRounded: ({ width = 8, height = 8, className, ...otherProps }) => (
    <svg
      width={width}
      height={height}
      viewBox="0 0 10 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...otherProps}
    >
      <path
        d="M0.720006 3.66643C-0.329922 2.6165 0.41368 0.821289 1.8985 0.821289L7.99319 0.821289C9.47801 0.821289 10.2216 2.6165 9.17168 3.66643L6.12434 6.71377C5.47348 7.36464 4.41821 7.36464 3.76735 6.71377L0.720006 3.66643Z"
        fill="#2376F3"
      />
    </svg>
  ),
  infoIcon: ({ width = '20', height = '20', fill = '#AABDCE', ...props }) => (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none" {...props} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.47707 0 0 4.47723 0 10C0 15.5231 4.47707 20 10 20C15.5229 20 20 15.5231 20 10C20 4.47723 15.5229 0 10 0ZM10 5C10.6904 5 11.25 5.55969 11.25 6.25C11.25 6.94063 10.6904 7.5 10 7.5C9.30961 7.5 8.75 6.94063 8.75 6.25C8.75 5.55969 9.30961 5 10 5ZM11.875 15H8.125C7.77984 15 7.5 14.7205 7.5 14.375C7.5 14.0298 7.77984 13.75 8.125 13.75H8.75V10H8.125C7.77984 10 7.5 9.72047 7.5 9.375C7.5 9.02984 7.77984 8.75 8.125 8.75H10.625C10.9702 8.75 11.25 9.02984 11.25 9.375V13.75H11.875C12.2202 13.75 12.5 14.0298 12.5 14.375C12.5 14.7205 12.2202 15 11.875 15Z"
        fill={fill}
      />
    </svg>
  ),
  cautionOutlined: ({ width = '21', height = '20', stroke = '#FA9500' }) => (
    <svg width={width} height={height} viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_13911_17135)">
        <path
          d="M9.55756 3.21635L2.49923 14.9997C2.3537 15.2517 2.2767 15.5374 2.27589 15.8284C2.27507 16.1195 2.35047 16.4056 2.49458 16.6585C2.6387 16.9113 2.8465 17.122 3.09732 17.2696C3.34814 17.4171 3.63323 17.4965 3.92423 17.4997H18.0409C18.3319 17.4965 18.617 17.4171 18.8678 17.2696C19.1186 17.122 19.3264 16.9113 19.4705 16.6585C19.6147 16.4056 19.6901 16.1195 19.6892 15.8284C19.6884 15.5374 19.6114 15.2517 19.4659 14.9997L12.4076 3.21635C12.259 2.97144 12.0498 2.76895 11.8002 2.62842C11.5506 2.48789 11.269 2.41406 10.9826 2.41406C10.6961 2.41406 10.4145 2.48789 10.1649 2.62842C9.91529 2.76895 9.70612 2.97144 9.55756 3.21635V3.21635Z"
          stroke={stroke}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M10.9827 7.5V10.8333" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M10.9827 14.167H10.991" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_13911_17135">
          <rect width="20" height="20" fill="white" transform="translate(0.982666)" />
        </clipPath>
      </defs>
    </svg>
  ),
  calendly: props => (
    <svg xmlns="http://www.w3.org/2000/svg" width={32} height={33} fill="none" {...props}>
      <path
        fill="#006BFF"
        d="M21.414 21.245c-1.01.897-2.273 2.013-4.562 2.013h-1.37c-1.655 0-3.162-.602-4.24-1.693-1.054-1.067-1.634-2.526-1.634-4.11v-1.873c0-1.584.58-3.044 1.634-4.11 1.078-1.091 2.584-1.693 4.24-1.693h1.37c2.291 0 3.551 1.116 4.562 2.013 1.05.925 1.955 1.733 4.37 1.733.368 0 .736-.03 1.1-.088l-.009-.021a8.274 8.274 0 0 0-.507-1.043l-1.614-2.796a8.291 8.291 0 0 0-7.18-4.145h-3.23a8.291 8.291 0 0 0-7.18 4.145L5.55 12.373a8.293 8.293 0 0 0 0 8.291l1.614 2.796a8.29 8.29 0 0 0 7.18 4.145h3.23a8.291 8.291 0 0 0 7.18-4.145l1.614-2.791c.193-.336.363-.684.507-1.044l.009-.02a6.832 6.832 0 0 0-1.1-.092c-2.415 0-3.32.803-4.37 1.733"
      />
      <path
        fill="#006BFF"
        d="M16.851 11.475h-1.369c-2.52 0-4.177 1.8-4.177 4.105v1.873c0 2.305 1.656 4.105 4.177 4.105h1.369c3.674 0 3.385-3.745 8.932-3.745.526 0 1.05.047 1.567.144a8.282 8.282 0 0 0 0-2.881 8.559 8.559 0 0 1-1.567.144c-5.549 0-5.258-3.745-8.932-3.745Z"
      />
      <path
        fill="#006BFF"
        d="M30.538 19.326a7.733 7.733 0 0 0-3.186-1.37v.029a8.3 8.3 0 0 1-.463 1.613c.952.148 1.855.52 2.634 1.085 0 .008-.005.017-.007.025a14.203 14.203 0 1 1 0-8.382c0 .009.004.018.007.025a6.054 6.054 0 0 1-2.634 1.084c.208.522.363 1.063.463 1.616v.026a7.732 7.732 0 0 0 3.186-1.369c.908-.672.732-1.432.594-1.881a15.92 15.92 0 1 0 0 9.38c.138-.449.314-1.208-.594-1.88Z"
      />
      <path
        fill="#0AE8F0"
        d="M26.885 13.433c-.364.06-.732.092-1.1.093-2.415 0-3.32-.804-4.369-1.733-1.012-.897-2.271-2.014-4.563-2.014h-1.369c-1.657 0-3.163.602-4.241 1.694-1.054 1.066-1.634 2.525-1.634 4.11v1.873c0 1.584.58 3.043 1.634 4.11 1.078 1.091 2.584 1.692 4.241 1.692h1.369c2.292 0 3.551-1.115 4.563-2.012 1.049-.925 1.954-1.733 4.37-1.733.367 0 .735.029 1.099.087.208-.52.363-1.061.462-1.614v-.027a8.55 8.55 0 0 0-1.567-.144c-5.549 0-5.258 3.747-8.932 3.747H15.48c-2.52 0-4.177-1.802-4.177-4.106v-1.877c0-2.305 1.656-4.105 4.177-4.105h1.37c3.673 0 3.384 3.744 8.931 3.744.526.001 1.05-.047 1.567-.143v-.026a8.357 8.357 0 0 0-.462-1.616Z"
      />
      <path
        fill="#0AE8F0"
        d="M26.885 13.433c-.364.06-.732.092-1.1.093-2.415 0-3.32-.804-4.369-1.733-1.012-.897-2.271-2.014-4.563-2.014h-1.369c-1.657 0-3.163.602-4.241 1.694-1.054 1.066-1.634 2.525-1.634 4.11v1.873c0 1.584.58 3.043 1.634 4.11 1.078 1.091 2.584 1.692 4.241 1.692h1.369c2.292 0 3.551-1.115 4.563-2.012 1.049-.925 1.954-1.733 4.37-1.733.367 0 .735.029 1.099.087.208-.52.363-1.061.462-1.614v-.027a8.55 8.55 0 0 0-1.567-.144c-5.549 0-5.258 3.747-8.932 3.747H15.48c-2.52 0-4.177-1.802-4.177-4.106v-1.877c0-2.305 1.656-4.105 4.177-4.105h1.37c3.673 0 3.384 3.744 8.931 3.744.526.001 1.05-.047 1.567-.143v-.026a8.357 8.357 0 0 0-.462-1.616Z"
      />
    </svg>
  ),

  warningOutlined: ({ width = '24', height = '25', stroke = '#915200' }) => (
    <svg width={width} height={height} viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 22.5C17.5228 22.5 22 18.0228 22 12.5C22 6.97715 17.5228 2.5 12 2.5C6.47715 2.5 2 6.97715 2 12.5C2 18.0228 6.47715 22.5 12 22.5Z"
        stroke={stroke}
        strokeWidth="2.22222"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M12 16.5V12.5" stroke={stroke} strokeWidth="2.22222" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M12 8.5H12.01" stroke={stroke} strokeWidth="2.22222" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),

  fileOutline: ({ width = '32', height = '33', stroke = '#2376F3' }) => (
    <svg width={width} height={height} viewBox="0 0 42 43" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M24.5 4H10.5C9.57174 4 8.6815 4.36875 8.02513 5.02513C7.36875 5.6815 7 6.57174 7 7.5V35.5C7 36.4283 7.36875 37.3185 8.02513 37.9749C8.6815 38.6313 9.57174 39 10.5 39H31.5C32.4283 39 33.3185 38.6313 33.9749 37.9749C34.6313 37.3185 35 36.4283 35 35.5V14.5L24.5 4Z"
        stroke={stroke}
        strokeWidth="4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M24.5 4V14.5H35" stroke={stroke} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M15.75 26.75H26.25" stroke={stroke} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),

  ellipsis: props => (
    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" width={20} height={20} fill="none" {...props}>
      <path
        fill="#414F5F"
        stroke="#414F5F"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M10 10.834a.833.833 0 1 0 0-1.667.833.833 0 0 0 0 1.667ZM15.833 10.834a.833.833 0 1 0 0-1.667.833.833 0 0 0 0 1.667ZM4.167 10.834a.833.833 0 1 0 0-1.667.833.833 0 0 0 0 1.667Z"
      />
    </svg>
  ),

  cautionRounded: props => (
    <svg xmlns="http://www.w3.org/2000/svg" width={16} height={16} fill="none" viewBox="0 0 16 16" {...props}>
      <g clipPath="url(#a)">
        <path
          fill="#FA9500"
          fillRule="evenodd"
          d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Zm6.727-.727c0-.402.326-.728.728-.728h1.09c.402 0 .728.326.728.728v5.09a.727.727 0 0 1-.728.728h-1.09a.727.727 0 0 1-.728-.727V7.273Zm2.728-2.91a1.455 1.455 0 1 0-2.91 0 1.455 1.455 0 0 0 2.91 0Z"
          clipRule="evenodd"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h16v16H0z" />
        </clipPath>
      </defs>
    </svg>
  ),
  userCircle: ({ width = '42', height = '35', fill = '#94A7B7' }) => (
    <svg width={width} height={height} viewBox="0 0 42 35" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.1663 17.5006C31.1663 25.3246 24.8236 31.6673 16.9997 31.6673C9.17564 31.6673 2.83301 25.3246 2.83301 17.5006C2.83301 9.67661 9.17564 3.33398 16.9997 3.33398C24.8236 3.33398 31.1663 9.67661 31.1663 17.5006ZM21.2497 13.2507C21.2497 15.5979 19.3469 17.5006 16.9997 17.5006C14.6524 17.5006 12.7497 15.5979 12.7497 13.2507C12.7497 10.9034 14.6524 9.00065 16.9997 9.00065C19.3469 9.00065 21.2497 10.9034 21.2497 13.2507ZM16.9997 29.5423C19.527 29.5423 21.8726 28.7637 23.8094 27.4332C24.665 26.8455 25.0305 25.7261 24.5332 24.8152C23.5022 22.9269 21.3775 21.7506 16.9995 21.7506C12.6217 21.7506 10.4971 22.9268 9.46604 24.8152C8.96866 25.7261 9.33423 26.8454 10.1897 27.433C12.1266 28.7636 14.4722 29.5423 16.9997 29.5423Z"
        fill={fill}
      />
      <path
        d="M33.576 17.7772C33.729 9.83824 27.9022 5.23266 24.9697 3.92224C30.6718 3.28474 39.5472 7.0188 39.5472 17.7772C39.5472 28.5085 30.1335 31.8731 24.9697 31.0372C27.7747 29.9252 33.423 25.7162 33.576 17.7772Z"
        fill={fill}
      />
    </svg>
  ),
  userCheck: ({ width = '18', height = '20', fill = '#24B314' }) => (
    <svg width={width} height={height} viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.50098 4.60117C4.50098 7.00117 6.50098 9.00117 9.00098 9.00117C11.501 9.00117 13.501 7.00117 13.501 4.60117C13.501 2.20117 11.501 0.201172 9.00098 0.201172C6.50098 0.201172 4.50098 2.20117 4.50098 4.60117ZM11.501 4.60117C11.501 5.90117 10.401 7.00117 9.00098 7.00117C7.60098 7.00117 6.50098 5.90117 6.50098 4.60117C6.50098 3.30117 7.60098 2.20117 9.00098 2.20117C10.401 2.20117 11.501 3.30117 11.501 4.60117Z"
        fill={fill}
      />
      <path
        d="M3.60072 14.802C4.00072 13.102 5.40072 12.002 7.00072 12.002H11.0007C11.8007 12.002 12.6007 12.302 13.3007 12.802C13.7007 13.202 14.4007 13.102 14.7007 12.702C15.1007 12.302 15.0007 11.602 14.6007 11.302C13.6007 10.502 12.3007 10.002 11.0007 10.002H7.00072C4.50072 10.002 2.30072 11.702 1.60072 14.302L1.10072 16.202C0.80072 17.102 1.00072 18.102 1.60072 18.902C2.20072 19.602 3.00072 20.002 4.00072 20.002H10.9007C11.5007 20.002 11.9007 19.602 11.9007 19.002C11.9007 18.402 11.5007 18.002 10.9007 18.002H4.00072C3.60072 18.002 3.40072 17.802 3.30072 17.602C3.00072 17.402 2.90072 17.002 3.00072 16.702L3.60072 14.802Z"
        fill={fill}
      />
      <path
        d="M14.3006 18.7015C14.5006 18.9015 14.7006 19.0015 15.0006 19.0015C15.1006 19.0015 15.1006 19.0015 15.2006 19.0015C15.5006 18.9015 15.8006 18.7015 15.9006 18.5015L17.9006 14.5015C18.1006 14.0015 17.9006 13.4015 17.5006 13.2015C17.0006 13.0015 16.4006 13.2015 16.2006 13.6015L14.8006 16.4015L13.8006 15.4015C13.4006 15.0015 12.8006 15.0015 12.4006 15.4015C12.0006 15.8015 12.0006 16.4015 12.4006 16.8015L14.3006 18.7015Z"
        fill={fill}
      />
    </svg>
  ),
  book: ({ width = '25', height = '25', fill = '#2376F3' }) => (
    <svg width={width} height={height} viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.5 3.55078H8.5C9.56087 3.55078 10.5783 3.97221 11.3284 4.72235C12.0786 5.4725 12.5 6.48992 12.5 7.55078V21.5508C12.5 20.7551 12.1839 19.9921 11.6213 19.4295C11.0587 18.8669 10.2956 18.5508 9.5 18.5508H2.5V3.55078Z"
        stroke={fill}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.5 3.55078H16.5C15.4391 3.55078 14.4217 3.97221 13.6716 4.72235C12.9214 5.4725 12.5 6.48992 12.5 7.55078V21.5508C12.5 20.7551 12.8161 19.9921 13.3787 19.4295C13.9413 18.8669 14.7044 18.5508 15.5 18.5508H22.5V3.55078Z"
        stroke={fill}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  user: ({ width = '18', height = '20', fill = '#3E4B5B' }) => (
    <svg width={width} height={height} viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.37109 4.81386C4.37109 7.38643 6.46396 9.47929 9.0366 9.47929C11.6092 9.47929 13.7021 7.38643 13.7021 4.81386C13.7021 2.2413 11.6092 0.148438 9.0366 0.148438C6.46396 0.148438 4.37109 2.2413 4.37109 4.81386Z"
        fill={fill}
      />
      <path
        d="M1.60352 19.8881H16.4734C17.0893 19.8881 17.5887 19.3887 17.5887 18.7728C17.5887 14.0583 13.753 10.2227 9.03847 10.2227C4.32389 10.2227 0.488281 14.0583 0.488281 18.7728C0.488281 19.3887 0.987612 19.8881 1.60352 19.8881Z"
        fill={fill}
      />
    </svg>
  ),
  heart: ({ width = '20', height = '18', fill = '#3E4B5B' }) => (
    <svg width={width} height={height} viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16.4555 1.12428C13.7865 0.258955 10.918 1.70303 10.0021 4.35237C9.08624 1.70024 6.21773 0.258955 3.54871 1.12428C0.848766 2.00367 -0.623409 4.90869 0.25319 7.61987C1.09604 10.2046 8.91205 17.1272 9.91219 17.1272C10.9124 17.1272 18.9026 10.213 19.7455 7.61987C20.6248 4.90307 19.1526 1.99805 16.4555 1.12428Z"
        fill={fill}
      />
    </svg>
  ),
  briefcasePlain: ({ width = '20', height = '20', fill = '#3E4B5B' }) => (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0.823653 12.0432V12.0526H4.15855C4.23465 11.2271 4.92133 10.5794 5.76602 10.5794C6.61071 10.5794 7.29739 11.2271 7.37349 12.0526H12.3463C12.4224 11.2271 13.1091 10.5794 13.9538 10.5794C14.7985 10.5794 15.4851 11.2271 15.5612 12.0526H19.1768V12.0432C19.6808 11.592 20.0004 10.9389 20.0004 10.2083V6.68497C20.0004 5.32549 18.8984 4.22296 17.5384 4.22296H14.7747C14.4336 2.33975 12.4255 0.886719 10.0002 0.886719C7.57492 0.886719 5.56682 2.33975 5.22572 4.22386H2.46201C1.10208 4.22386 0 5.32639 0 6.68586V10.2097C0 10.9393 0.319613 11.5933 0.823653 12.0432ZM10.0002 3.1258C11.0746 3.1258 12.0441 3.59806 12.4233 4.22475H7.57761C7.95586 3.59672 8.92545 3.1258 10.0002 3.1258ZM19.1768 12.9474V16.6525C19.1768 18.012 18.0747 19.1145 16.7148 19.1145H3.28566C1.92574 19.1145 0.823653 18.012 0.823653 16.6525V12.9479H4.15855C4.23465 13.7733 4.92133 14.4219 5.76602 14.4219C6.61071 14.4219 7.29739 13.7733 7.37349 12.9479H12.3463C12.4224 13.7733 13.1091 14.4219 13.9538 14.4219C14.7985 14.4219 15.4851 13.7733 15.5612 12.9479H19.1768V12.9474Z"
        fill={fill}
      />
    </svg>
  ),
  arrowLeft: ({ width = '18', height = '16', fill = '#3E4B5B' }) => (
    <svg width={width} height={height} viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.17969 1.29297C7.73494 0.738048 8.63517 0.738068 9.19043 1.29297C9.74576 1.8483 9.74576 2.74935 9.19043 3.30469L5.47852 7.01562H16.0469C16.8322 7.01562 17.4688 7.65214 17.4688 8.4375C17.4688 9.22286 16.8322 9.85938 16.0469 9.85938H5.48047L9.19043 13.5693C9.7457 14.1246 9.74558 15.0247 9.19043 15.5801C8.6351 16.1354 7.73502 16.1354 7.17969 15.5801L1.05078 9.45215C0.982262 9.38487 0.921016 9.3104 0.867188 9.23047C0.83373 9.18066 0.80415 9.12861 0.777344 9.0752C0.721834 8.96489 0.678642 8.8471 0.65332 8.72266C0.634202 8.62927 0.625 8.53323 0.625 8.43652C0.625051 8.29527 0.646204 8.15617 0.686523 8.02344C0.755252 7.7973 0.878111 7.59455 1.04102 7.43164L7.17969 1.29297Z"
        fill={fill}
      />
    </svg>
  ),
  warningInfoRounded: props => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width={20} height={20} fill="none" {...props}>
      <rect width={20} height={20} fill="#FA9500" rx={10} />
      <path
        fill="#fff"
        d="m9.245 12.272-.368-8.048h2.288l-.352 8.048H9.245Zm.768 3.872c-.736 0-1.328-.576-1.328-1.296 0-.704.592-1.28 1.328-1.28.704 0 1.296.576 1.296 1.28a1.3 1.3 0 0 1-1.296 1.296Z"
      />
    </svg>
  ),
  x: props => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={24} height={24} fill="none" {...props}>
      <path stroke="#292B2C" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 6 6 18M6 6l12 12" />
    </svg>
  )
};

const Icon = (props: TIcon & { name: TIconNames }) => icons[props.name]?.(props) ?? null;
export default Icon;
