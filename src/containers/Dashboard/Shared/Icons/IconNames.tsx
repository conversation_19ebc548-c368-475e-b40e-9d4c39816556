/* eslint-disable no-unused-vars */
import { SVGAttributes } from 'react';

export type TIcon = SVGAttributes<SVGElement>;
export type TRenderSvg = (args: TIcon) => JSX.Element;

// Note: Icon names should be in camelCase and in alphabetical order

export const iconNames = [
  'arrowDown',
  'arrowRight',
  'calendar',
  'cancel',
  'cancelled',
  'caretDoubleLeft',
  'caretDoubleRight',
  'caretDown',
  'caretLeft',
  'caretRight',
  'caution',
  'chat',
  'checkRounded',
  'circleCancelEnvelope',
  'circleSuccessEnvelope',
  'circledCheck',
  'circledClose',
  'copy',
  'correct',
  'downloadAttestation',
  'down',
  'file',
  'fileOutline',
  'halfAndFullCircle',
  'halfCircle',
  'lightbulb',
  'linkHook',
  'message',
  'pageLoading',
  'search',
  'star',
  'upIcon',
  'upload',
  'merchantAvatar',
  'receipt',
  'hourGlass',
  'send',
  'briefcase',
  'userProfile',
  'thumbsUp',
  'circleSpinner',
  'gear',
  'arrowDownRounded',
  'infoIcon',
  'cautionOutlined',
  'calendly',
  'warningOutlined',
  'threeDots',
  'pen',
  'trashCan',
  'plus',
  'check',
  'circledTrash',
  'circledTrashOutline',
  'circledCaution',
  'details',
  'ellipsis',
  'cautionRounded',
  'userCircle',
  'userCheck',
  'details',
  'book',
  'user',
  'heart',
  'briefcasePlain',
  'arrowLeft',
  'warningInfoRounded',
  'x'
] as const;

export type TIconNames = (typeof iconNames)[number];
