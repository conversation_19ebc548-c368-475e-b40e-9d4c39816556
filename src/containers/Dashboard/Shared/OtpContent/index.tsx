import React from 'react';

import { AuthDataType } from '+types';

interface OtpContentProps {
  email: string;
  twoFactorType: 'otp' | 'totp' | 'totp_recovery_code';
  authData: { code: string };
  setAuthData: ({ code }: Partial<AuthDataType>) => void;
  onResendToken?: () => void;
  timeLeft?: number | JSX.Element;
  setTwoFactorType?: (type: 'otp' | 'totp' | 'totp_recovery_code') => void;
  additionalDetails?: React.ReactNode;
  loading?: boolean;
}

const OtpContent: React.FC<OtpContentProps> = ({
  email,
  twoFactorType,
  authData,
  setAuthData,
  onResendToken,
  timeLeft,
  setTwoFactorType,
  additionalDetails,
  loading
}) => {
  const obfuscatedEmail = email.includes('@') ? `${email.split('@')[0].slice(0, 3)}*******@${email.split('@')[1]}` : email;

  let inputLabel = 'One Time PIN (OTP)';
  let inputPlaceholder = '7-digit code';
  let infoText;

  if (twoFactorType === 'totp') {
    inputLabel = 'Authentication Code';
    inputPlaceholder = 'Enter authentication code';
    infoText = (
      <>
        Enter the <b>six-digit</b> code from your <span className="colored-text">authenticator app</span> in the space provided below to
        confirm this transaction.
      </>
    );
  } else if (twoFactorType === 'totp_recovery_code') {
    inputLabel = 'Recovery Code';
    inputPlaceholder = 'Enter recovery code';
    infoText = <>Can&apos;t use your authenticator app? Enter one of your previously saved recovery codes to validate this transaction.</>;
  } else {
    infoText = (
      <>
        To proceed, enter the OTP (one-time PIN) that was sent to your email <b>({obfuscatedEmail})</b>.
      </>
    );
  }

  if (loading) {
    return (
      <div className="bankForm-container">
        <div className="ip-otp-info --no-color d-flex justify-content-center align-items-center">
          <span className="spinner-border spinner-border-s text-primary" role="status" aria-hidden="true" />
        </div>
      </div>
    );
  }

  return (
    <div className="bankForm-container">
      {twoFactorType === 'totp_recovery_code' ? (
        <div className="ip-otp-info --no-color">
          <p>{infoText}</p>
        </div>
      ) : (
        <div className="ip-otp-info --no-color">{additionalDetails || <p>{infoText}</p>}</div>
      )}

      <label htmlFor="pin">{inputLabel}</label>
      <input
        type="number"
        id="pin"
        data-testid="otp-input"
        name="pin"
        maxLength={7}
        placeholder={inputPlaceholder}
        autoComplete="one-time-code"
        value={authData.code}
        onChange={e => {
          const formattedInput = e.target.value.replace(/\D/g, ''); // Clean input to allow only numbers
          setAuthData({ code: formattedInput });
        }}
      />

      {twoFactorType === 'totp' && setTwoFactorType && (
        <div className="recovery-code">
          Can&apos;t access authenticator app?{' '}
          <button type="button" onClick={() => setTwoFactorType('totp_recovery_code')} className="recovery-code-btn">
            Confirm using recovery codes
          </button>
        </div>
      )}

      {twoFactorType === 'otp' && onResendToken && (
        <div className="resend mt-1">
          <span>Didn’t receive any email?</span>
          {!timeLeft ? (
            <button type="button" onClick={onResendToken} className="link-text">
              Resend
            </button>
          ) : (
            <span> Wait {timeLeft} seconds...</span>
          )}
        </div>
      )}
    </div>
  );
};

export default OtpContent;
