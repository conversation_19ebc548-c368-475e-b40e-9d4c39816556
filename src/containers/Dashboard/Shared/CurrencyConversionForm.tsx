import React, { useEffect } from 'react';

import { ArrowsLeftRight } from '+containers/Shared/Icons';
import { ICurrencyConversionFormProps } from '+types';
import { formatWithCommas, getFormattedConversionRateString } from '+utils';

import Accordion from './Accordion';
import DisplayCountdown from './DisplayCountdown';
import Icon from './Icons';

import './index.scss';

const CurrencyConversionForm: React.FC<ICurrencyConversionFormProps> = props => {
  const {
    formType,
    payloadCurrency,
    handleSelectChange,
    handleSwitchCurrency,
    handleProcessConversion,
    handleRetrieveRate,
    availableBalance,
    errorMessage,
    checkLimitAndValidate,
    formatAmount,
    toAmount,
    isLoading,
    amount,
    handleInputChange,
    amountInputRef,
    currentCount,
    initiateConversion,
    sourceCurrency,
    destinationCurrency,
    isProcessConversionLoading,
    from_currency_rate,
    state
  } = props;

  const [accordionOpen, setAccordionOpen] = React.useState(true);
  const accordionRef = React.useRef<HTMLDetailsElement>(null);

  const handleAccordionToggle = () => {
    setAccordionOpen(accordionRef.current ? accordionRef.current.open : false);
  };

  useEffect(() => {
    setAccordionOpen(accordionRef.current ? accordionRef.current.open : false);
  }, []);

  const renderRateSection = () => {
    if (isLoading) {
      return <span className="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true" />;
    }

    if (!checkLimitAndValidate) {
      return null;
    }

    if (state?.data) {
      return (
        <>
          <span>Rate: {getFormattedConversionRateString(payloadCurrency.from, payloadCurrency.to, from_currency_rate)}</span>
          {state?.showRetrieveButton ? (
            <button className="btn retrieve-conversion-rate-btn" onClick={handleRetrieveRate} disabled={isLoading}>
              Retrieve Conversion Rate
            </button>
          ) : (
            state?.startCount && <DisplayCountdown currentCount={currentCount} />
          )}
        </>
      );
    }

    return null;
  };

  switch (formType) {
    case 'main':
      return (
        <div className="conversions-wrapper">
          <div className="conversion-forms">
            <div className="conversion-forms inner">
              <div className="form-group conversions-form">
                <label htmlFor="mobileNo" className="withdraw-label">
                  <span className="dark">When you convert</span>
                </label>
                <div className="input-group">
                  <div className="input-group-prepend">
                    <div className="select-cur">
                      <select
                        className="form-control"
                        aria-label="source currency"
                        value={payloadCurrency.from}
                        onChange={e => handleSelectChange(e, 'currencyFrom')}
                      >
                        {sourceCurrency.map((currency: string) => (
                          <option key={currency} value={currency}>
                            {currency}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <input
                    className="form-control"
                    name="amount"
                    inputMode="numeric"
                    type="text"
                    maxLength={16}
                    value={formatWithCommas(amount)}
                    onChange={handleInputChange}
                    placeholder="0"
                    ref={amountInputRef}
                  />
                </div>
                <div className="text-amount-info" style={{ right: 0 }}>
                  <div className="text-amount-info">{errorMessage && <small> {errorMessage} </small>}</div>
                </div>
                <div className="conversion-summary-item">
                  <div className="conversion-summary-item__mobile">
                    <span>Balance:</span>
                    <span className="available-balance" data-testid="conversion-balance">
                      {formatAmount(availableBalance)} {payloadCurrency.from}
                    </span>
                  </div>
                </div>
              </div>
              <button className="switch-btn" type="button" onClick={handleSwitchCurrency}>
                <ArrowsLeftRight className="btn-arrows-left-right" title="Switch currencies" />
              </button>
              <div className="form-group conversions-form currency-to">
                <label htmlFor="amountToReceive" className="withdraw-label">
                  <span className="dark">You&apos;ll Receive</span>
                </label>
                <div className="input-group currency-to-amount">
                  <div className="input-group-prepend">
                    <div className="select-cur">
                      <select
                        className="form-control"
                        aria-label="destination currency"
                        value={payloadCurrency.to}
                        onChange={e => handleSelectChange(e, 'currencyTo')}
                      >
                        {destinationCurrency.map((currency: string) => (
                          <option key={currency} value={currency}>
                            {currency}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <input
                    className="form-control currency-to-input"
                    type="number"
                    name="amount"
                    id="amountToReceive"
                    aria-label="You'll Receive amount"
                    value={''}
                    onChange={() => {}}
                    placeholder={checkLimitAndValidate && !state?.showRetrieveButton ? `${formatAmount(toAmount || 0)}` : ''}
                  />
                </div>
              </div>
            </div>
            <button
              className="btn btn-primary convert-btn"
              disabled={!checkLimitAndValidate || state?.showRetrieveButton}
              onClick={handleProcessConversion}
            >
              {' '}
              {isProcessConversionLoading ? (
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true" />
              ) : (
                <>
                  <span>Convert</span>
                  <i className="os-icon os-icon-arrow-right6" />
                </>
              )}
            </button>
          </div>

          <div className="conversion-summary">
            <div className="conversion-summary-item">
              <div className="conversion-summary-item__desktop">
                <span>Balance:</span>
                <span className="available-balance" data-testid="conversion-balance">
                  {formatAmount(availableBalance)} {payloadCurrency.from}
                </span>
              </div>
            </div>
            <div className="conversion-summary-item exchange_rate_wrapper">
              <ul>
                <li>
                  <div className="d-flex">{renderRateSection()}</div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      );
    case 'modal':
      return (
        <>
          <div className="form-group convert-form">
            <div className="conversion-label">
              <label htmlFor="conversion-label" className="withdraw-label">
                <span className="dark">Pay</span>
              </label>
              <label htmlFor="balance" className="withdraw-label balance">
                <span className="">Balance:</span>
                <span className="dark" data-testid="conversion-balance">
                  {formatAmount(availableBalance)} {payloadCurrency.from}
                </span>
              </label>
            </div>
            <div className="input-group">
              <div className="input-group-prepend">
                <div className="select-cur">
                  <select
                    className="form-control"
                    aria-label="source currency"
                    value={payloadCurrency.from}
                    onChange={e => handleSelectChange(e, 'currencyFrom')}
                  >
                    {sourceCurrency.map((currency: string) => (
                      <option key={currency} value={currency}>
                        {currency}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <input
                className="form-control conversion-form-input"
                name="amount"
                inputMode="numeric"
                type="text"
                maxLength={16}
                value={formatWithCommas(amount)}
                onChange={handleInputChange}
                placeholder="0"
              />
            </div>
            <div className="text-amount-info">{errorMessage && <small> {errorMessage} </small>}</div>
          </div>
          <button type="button" onClick={handleSwitchCurrency} className="switch-btn-modal">
            <ArrowsLeftRight className="btn-arrows" title="Switch currencies" />
          </button>
          <div className="form-group convert-form btn-margin">
            <label htmlFor="conversion-label" className="withdraw-label">
              <span className="dark">To Receive</span>
            </label>
            <div className="input-group">
              <div className="input-group-prepend">
                <div className="select-cur">
                  <select
                    className="form-control"
                    aria-label="destination currency"
                    value={payloadCurrency.to}
                    onChange={e => handleSelectChange(e, 'currencyTo')}
                  >
                    {destinationCurrency.map((currency: string) => (
                      <option key={currency} value={currency}>
                        {currency}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <input
                className="form-control currency-to-input conversion-form-input"
                name="amount"
                type="number"
                maxLength={11}
                value={''}
                placeholder={checkLimitAndValidate ? `${formatAmount(toAmount || 0)}` : ''}
              />
            </div>
          </div>
          <div className="conversion-summary">
            <div className="conversion-summary-item exchange-rate">
              <div>
                {initiateConversion?.isLoading === true ? (
                  <span className="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true" />
                ) : (
                  checkLimitAndValidate && (
                    <Accordion
                      ref={accordionRef}
                      onToggle={handleAccordionToggle}
                      className="conversion-summary-rate w-100 mb-4 custom-modal-accordion"
                      showToggle={true}
                      defaultOpen={true}
                      title={
                        <div className={`conversion-summary-rate-header${accordionOpen ? ' open' : ''}`}>
                          <p className="label text-capitalize mr-2 mb-0 mt-1">Summary</p>
                          <p className="mr-2 mb-0 mt-1 show-toggle-label">{accordionOpen ? 'Show less' : 'Show more'}</p>
                        </div>
                      }
                      customToggle={
                        <Icon
                          className={`arrow-down show-more-arrow${accordionOpen ? ' open' : ''}`}
                          name="caretDown"
                          width={12}
                          strokeWidth={1.5}
                          stroke="#2376F3"
                        />
                      }
                      content={
                        <>
                          <div className="top-up-row my-2 conversion-summary-rate-content">
                            <p className="value fs-1">Conversion rate</p>
                            <p className="value fs-1">
                              <span className="swaps-amount">
                                {getFormattedConversionRateString(payloadCurrency.from, payloadCurrency.to, from_currency_rate)}
                              </span>
                            </p>
                          </div>
                          <div className="top-up-row my-2 conversion-summary-rate-content">
                            <p className="value fs-1">
                              Amount to pay <span>(Plus Fees)</span>
                            </p>
                            <p className="value fs-1">
                              <span className="swaps-amount">{`${payloadCurrency.from} ${formatWithCommas(amount || '0')}`}</span>
                            </p>
                          </div>
                          <div className="top-up-row my-2 conversion-summary-rate-content">
                            <p className="value fs-1">You will receive</p>
                            <p className="value fs-1">
                              <span className="swaps-amount">{`${payloadCurrency.to} ${formatAmount(toAmount || 0)} `}</span>
                            </p>
                          </div>
                        </>
                      }
                    />
                  )
                )}
              </div>
            </div>
          </div>
        </>
      );
    default:
      return null;
  }
};

export default CurrencyConversionForm;
