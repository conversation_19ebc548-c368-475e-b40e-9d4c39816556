.basic-select {
  align-items: center;
  border: 2px solid #dde2ec;
  border-radius: 5px;
  color: inherit;
  display: flex;
  flex-shrink: 0;
  flex-grow: 1;
  padding: 0;
  position: relative;
  width: 100%;
  z-index: 1;

  > svg {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
  }

  > span {
    padding: 0.5rem;
  }

  > ul {
    animation: fadeIn 0.5s;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px #0000001a;
    list-style: none;
    margin: 0;
    max-height: 200px;
    overflow: hidden scroll;
    padding: 0.5rem 0;
    position: absolute;
    top: 120%;
    width: 100%;
    overflow: auto;

    > li {
      &:hover {
        background: #f5f7fa;
      }
    }
  }

  &:disabled {
    background: #cbcbcb42;
    cursor: not-allowed;
  }
}
