@import 'styles/base/variables';

.confirm-container {
  display: flex;
  justify-content: space-between;
  .btn-secondary {
    background-color: #eaf2fe;
    color: #3e4b5b;
    padding: 1rem 3.5rem;
    font-weight: 500;
    border-radius: 8px;
  }

  .btn-primary {
    background-color: #2376f3;
    padding: 1rem 3.5rem;
    font-weight: 500;
    border-radius: 8px;
  }
}

.refund-request-note {
  background-color: #f3f4f8;
  border-radius: 5px;

  p {
    margin-bottom: 0 !important;
    color: #a9afbc;
    font-size: 13px;
  }
}

.radio-div {
  padding-left: 1.25rem;

  small {
    opacity: 0.7;
    font-size: 0.8rem;
  }
}

.full-disabled {
  opacity: 0.5;
}

.optional {
  font-weight: 400;
  font-style: italic;
  color: #3e4b5b;
}

.show-status {
  small {
    font-size: 0.8rem;
    margin-left: 5px;
  }

  span {
    color: #24b314;
    font-size: 0.8rem;
  }

  .icon-danger {
    color: #f32345 !important;
    padding-left: 1.25rem;
  }
}

.show-reason {
  small {
    font-size: 0.8rem;
    margin-left: 5px;
  }

  span {
    color: #f32345 !important;
    font-size: 0.8rem;
  }
}

.reason-container {
  position: relative;
  input {
    padding: 7px 11px;
    border: 2.5px solid #e3e7ee;
    box-shadow: none;
    font-size: 14px;

    &:focus {
      box-shadow: none;
    }
  }

  em {
    font-size: 12px;
  }

  .reason-list {
    width: 100%;
    padding: 7px 11px;
    font-weight: unset;
  }

  .list-dropdown--overlay {
    height: 151%;
    width: 106%;
    top: -38%;

    @media (min-width: 500px) {
      height: 77%;
      width: 100%;
      top: 17px;
    }
  }
}
