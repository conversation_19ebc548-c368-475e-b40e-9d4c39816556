import React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import VirtualAccountDetails from '../index';

const MockedVirtualAccountDetails = () => {
  return (
    <MockIndex>
      <VirtualAccountDetails />
    </MockIndex>
  );
};

describe('VirtualAccountDetails', () => {
  test('Renders VirtualAccountDetails without error', () => {
    render(<MockedVirtualAccountDetails />);
  });

  test('VirtualAccountDetails is accessible', async () => {
    const { container } = render(<MockedVirtualAccountDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
