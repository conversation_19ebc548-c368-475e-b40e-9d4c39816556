import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import VirtualBankAccountLimit, { IVirtualBankAccountLimitProps } from '../VirtualBankAccountLimitBanner';

const MockedVirtualBankAccountLimit = ({ action, count, currency, loading, completed }: IVirtualBankAccountLimitProps) => {
  return (
    <MockIndex>
      <VirtualBankAccountLimit action={action} count={count} loading={loading} currency={currency} completed={completed} />
    </MockIndex>
  );
};

describe('AuthStatus', () => {
  test('Virtual Bank Account limit banner page is accessible', async () => {
    const { container } = render(
      <MockedVirtualBankAccountLimit action={vi.fn()} count={10} loading={false} currency="NGN" completed={false} />
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('Virtual Bank Account limit banner render properly', async () => {
    render(<MockedVirtualBankAccountLimit action={vi.fn()} count={10} loading={false} currency="NGN" completed={false} />);
    expect(screen.getByText(/You have 10 Virtual Bank Accounts/i)).toBeInTheDocument();
    expect(screen.getByText(/NGN/i)).toBeInTheDocument();
  });
});
