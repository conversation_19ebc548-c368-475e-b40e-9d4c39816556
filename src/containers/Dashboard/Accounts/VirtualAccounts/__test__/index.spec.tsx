import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import useStore from '+store';

import VirtualAccounts from '../index';

useStore.setState(state => ({
  ...state,
  permissions: { virtual_account: 'view' }
}));

const MockedVirtualAccounts = () => {
  return (
    <MockIndexWithRoute
      initialEntries={['/dashboard/accounts/virtual-accounts', '/dashboard/accounts/virtual-accounts/:id']}
      route="/dashboard/accounts/virtual-accounts"
    >
      <VirtualAccounts />
    </MockIndexWithRoute>
  );
};

describe('VirtualAccounts', () => {
  test('VirtualAccounts is accessible', async () => {
    const { container } = render(<MockedVirtualAccounts />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('Viritual account page header text should be in the dom', async () => {
    render(<MockedVirtualAccounts />);
    const txt = await screen.findByText(/Local Virtual Accounts for Global Businesses/i);

    expect(txt).toBeInTheDocument();
  });

  it('Viritual account numbers lists should be fetched and rendered successfully', async () => {
    render(<MockedVirtualAccounts />);
    const txt = await screen.findByText(/Upgrade Request/i);
    expect(txt).toBeInTheDocument();
  });
});
