@import 'styles/base/variables';

.virtual-account-summary {
  article {
    @media (max-width: $breakpoint-tablet) {
      width: 90%;
      margin: auto;
    }
    .virtual-account-summary-list > li {
      display: flex;

      p {
        width: 20%;
        font-family: Averta PE;
        font-style: normal;
        font-weight: 400;

        @media (max-width: $breakpoint-tablet) {
          width: 40%;
        }
      }
      .summaary-value {
        color: #414f5f;
      }
      .phone-number {
        color: #2376f3;
      }
    }
  }
}

.related-document {
  font-size: 16px;
  margin-top: 20px;
}

.related-document-content {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-radius: 4px;
  background: #f1f6fa;

  &:last-child {
    cursor: pointer;
  }

  div {
    display: flex;
    gap: 10px;
    align-items: center;

    > img {
      height: 80%;
    }
  }

  .--view-text {
    color: #2376f3;
  }

  .--document-name {
    color: #414f5f;
    opacity: 0.6;
  }
}
