@import 'styles/base/variables';

.virtual-accounts__container {
  .--vba-history {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 12%;
          }
          &:nth-of-type(2) {
            width: 28%;
          }
          &:nth-of-type(3) {
            width: 20%;
          }
          &:nth-of-type(4) {
            width: 40%;
            text-align: right;
          }
        }
      }
    }
  }

  .--vba-account-holder {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 12%;
          }
          &:nth-of-type(2) {
            width: 30%;
          }
          &:nth-of-type(3) {
            width: 18%;
            text-align: left;
          }
          &:nth-of-type(4) {
            width: 20%;
            text-align: left;
          }
          &:nth-of-type(5) {
            width: 20%;
            text-align: right;
          }
        }
      }
    }
  }

  .--vba-account-number {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          font-size: 14px;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 10%;
          }
          &:nth-of-type(2) {
            width: 28%;
          }
          &:nth-of-type(3) {
            width: 20%;
          }
          &:nth-of-type(4) {
            width: 10%;
          }
          &:nth-of-type(5) {
            width: 18%;
          }
          &:nth-of-type(6) {
            width: 14%;
          }
        }
      }
    }
  }

  .--vba-upgrade-request {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 12%;
          }
          &:nth-of-type(2) {
            width: 30%;
          }
          &:nth-of-type(3) {
            width: 20%;
          }
          &:nth-of-type(4) {
            width: 12%;
          }
          &:nth-of-type(5) {
            width: 12%;
          }
          &:nth-of-type(6) {
            width: 14%;
          }
        }
      }
    }
  }

  .--fvba-account-numbers {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 15%;
          }
          &:nth-of-type(2) {
            width: 30%;
          }
          &:nth-of-type(3) {
            width: 20%;
          }
          &:nth-of-type(4) {
            width: 10%;
          }
          &:nth-of-type(5) {
            width: 10%;
          }
          &:nth-of-type(6) {
            width: 15%;
          }
        }
      }
    }
  }

  .--vba-trxns {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 20%;
            color: unset;
          }
          &:nth-of-type(2) {
            width: 35%;
            text-align: center;
          }
          &:nth-of-type(3) {
            width: 25%;
            text-align: left;
          }
          &:nth-of-type(4) {
            width: 20%;
            text-align: right;
          }
        }
      }
    }
  }

  .--vba-pool {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 40%;
            color: unset;
          }
          &:nth-of-type(2) {
            width: 35%;
          }
          &:nth-of-type(3) {
            width: 25%;
            text-align: right;
          }
        }
      }
    }
  }
  .--vba-pool-details {
    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          margin-right: 0;
          word-break: break-all;
          &:nth-of-type(1) {
            width: 25%;
            color: unset;
          }
          &:nth-of-type(2) {
            width: 35%;
          }
          &:nth-of-type(3) {
            width: 20%;
          }
          &:nth-of-type(4) {
            width: 20%;
            text-align: right;
          }
        }
      }
    }
  }
  .vba-banner-wrapper {
    margin-bottom: 30px;
  }

  .vba-banner {
    background: #fff8e1;
    border-radius: 10px;
    font-weight: 400;
    margin: 1rem 1rem 1rem;
    padding: 10px;
    text-align: center;
    font-size: 14px;

    &.deleted {
      background: #ffd2da;
    }

    > span {
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

.virtual-accounts {
  justify-content: flex-start;
}

.account-holder-header {
  display: flex;
  justify-content: space-between;
  margin: 30px 0px;

  .holder-details {
    display: flex;
    flex-direction: column;
    gap: 5px;

    div {
      display: flex;
      gap: 7px;
      color: #a9afbc;

      span {
        margin-bottom: 0px;
        font-size: 0.9rem;
      }

      img {
        height: 18px;
      }
    }

    h2 {
      color: #292b2c;
      margin-bottom: 0px;
      font-size: 1.5rem;
    }

    p {
      color: #a9afbc;
      font-size: 0.9rem;
    }
  }
}

.manage-account-holder {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  cursor: pointer;

  > p {
    color: #2376f3;
    margin-bottom: 0px;
  }

  img {
    width: 24px;
  }

  .dropdown-icon {
    width: 10px;
  }

  .dropdown {
    position: absolute;
    top: 80px;
    padding: 20px !important;
    border-radius: 6px;
    box-shadow: 0px 2px 11px 0px #0f18211a;

    > p {
      font-size: 16px;
      color: #414f5f;
      font-weight: 400;
    }
    .deactivate {
      color: #f32345;
    }
  }
}

.virtual_account-filter {
  &__top {
    margin-top: 5px;
    margin-bottom: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  &__top > div {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  &__top > div button {
    border: none;
    outline: none;
    background: none;
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    text-align: center;
    color: #2376f3;
  }

  &__top > div button > span {
    color: #2376f3;
    font-weight: 500;
    font-size: 0.95rem;
  }

  &__top > div > span {
    font-style: normal;
    font-weight: 500;
    font-size: 0.95rem;
    color: #636c72;
    padding: 1px;
  }

  &__search-w {
    display: block;
    margin-left: 0;
    @media (max-width: '768px') {
      display: none;
    }
  }

  &__filter-button {
    border: none;
    background: #3e4b5b;
    height: 2.5rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    img {
      width: 1rem;
    }
  }

  .form-control {
    font-family: $font-family-base;
    height: 38px;
  }

  .filter-section .filter-body .form-group {
    margin-bottom: 0.5rem;
    margin-right: 0;
  }

  .filter-body .form-group.filter-object-sm .react-datepicker-wrapper:nth-child(1) .react-datepicker__input-container > input {
    border-radius: 4px;
    border: 2px solid #dde2ec;

    &:disabled {
      background: #efefef;
    }
  }

  .filter-body .form-group.filter-object-sm .react-datepicker-wrapper:nth-child(2) .react-datepicker__input-container > input {
    border-radius: 0 4px 4px 0;
  }

  .form-group select.form-control {
    background: url("data:image/svg+xml,%3Csvg width='40' height='20' viewBox='0 0 40 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16 8L20.5 12.56L25 8' stroke='%233E4B5B' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A")
      no-repeat right #fff;
    background-size: 35px;
    background-position-y: 55%;
    cursor: pointer;
  }

  .form-group.--search-container {
    position: relative;

    input {
      padding-left: 1.875rem;
    }

    img {
      width: 1.05rem;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 0.625rem;
    }
  }

  .form-group .date-select.form-control {
    background-image: var(--calendar-image),
      url("data:image/svg+xml,%3Csvg width='40' height='20' viewBox='0 0 40 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16 8L20.5 12.56L25 8' stroke='%233E4B5B' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
    background-repeat: no-repeat;
    background-position:
      4px 50%,
      right 55%;
    background-size: 12px, 35px;
    cursor: pointer;

    &::placeholder {
      color: #354253;
      font-weight: 400;
    }
  }
  .date-picker {
    border: 1.5px solid #eaf2fe;
  }
  select.form-control:hover {
    border-color: #047bf8;
  }

  .css-tlfecz-indicatorContainer {
    padding: 0px 8px !important;
  }

  @media (min-width: $breakpoint-desktop) {
    .filter-section .filter-body .form-group {
      margin-bottom: 0;
      margin-right: 8px;
    }

    &__filter-button {
      width: 2.75rem;
    }
  }

  @media (max-width: $breakpoint-desktop) {
    .filter-body > div,
    .filter-body .react-datepicker-wrapper {
      width: 100% !important;
    }

    .filter-body .form-group.filter-object-ssm,
    .filter-body .form-group.filter-object-xl,
    .filter-body .form-group.filter-object-sm {
      max-width: 100% !important;
    }
  }
}

.custom-file-upload {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0px;
  font-size: 14px;
  gap: 10px;
  background: #f1f6fa;
  padding: 10px 10px 10px 10px;
  border-radius: 6px;
  border: 1px solid #dde2ec;

  .browse-files {
    border-left: 1px solid #dde2ec;
    padding-left: 5px;
    color: #2376f3;
    cursor: pointer;
    max-width: 150px;
    text-align: right;
  }
  .file-name {
    margin-bottom: 0px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #414f5f;
    > img {
      width: 20px;
    }
    > span {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .uploaded-files {
    margin-bottom: 0px;
    display: flex;
    align-items: center;
    font-size: 12px;
  }
  &.error {
    border: 3px solid #f32345;
  }
  &.success {
    background: #e4fff1;
  }
}

.custom-file-upload input {
  display: none;
}

.upgrade_request_details {
  display: flex;
  flex-direction: column;
  padding-bottom: 1rem;
  background: #f1f6fa;
  width: 100%;

  @media (min-width: $breakpoint-desktop) {
    flex-direction: row;
  }

  ul {
    display: block;
    margin: 1rem;

    &:first-of-type {
      @media (min-width: $breakpoint-desktop) {
        width: 100%;
        border-bottom: unset;
      }

      &::after {
        content: '';
        display: block;
        margin-right: 1rem;
      }
    }
  }

  .details-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.94rem;
    word-break: break-all;
    padding: 5px;
    border-bottom: 1px solid#aabdce;

    @media (min-width: $breakpoint-tablet) {
      min-width: 300px;
      flex-direction: row;
    }

    .details-key {
      color: #aabdce;
      @media (min-width: $breakpoint-tablet) {
        min-width: 200px;
      }
    }
    .details-val {
      font-weight: 400;
      color: #000000;
      word-break: break-word;

      .smaller {
        font-size: 0.88rem;
      }
      .lighter  {
        color: #aabdce;
      }
    }
  }
}

.upgrade-request-summary::after,
.upgrade-request-summary::before,
.upgrade-request-summary::marker {
  content: '';
}

.upgrade-request-summary {
  border-bottom: 1px solid #aabdce;
  padding: 20px 0px;

  p {
    font-family: 'Averta PE';
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    color: #414f5f;
    margin-bottom: 5px;
    opacity: 0.7;
  }

  .upgrade-request-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .upgrade-request-warning {
    display: flex;
    background: #fff8e1;
    align-items: center;
    padding: 5px;
  }

  h5 {
    color: #414f5f;
    font-size: 16px;
    margin-bottom: 0px;
  }
}

.upgrade-request-documents {
  margin: 20px 0px;

  .no-record-request {
    font-size: 14px;
    font-style: italic;
    font-weight: 300;
    color: #414f5f;
  }
}
.upgrade-request-wrapper {
  .upgrade-request-banner {
    border-radius: 10px;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    color: #414f5f;
  }
}

.confirm-deactivation {
  background: #ffd2da;
  display: flex;
  padding: 10px;
  align-items: flex-start;
  border-radius: 4px;
  gap: 5px;
  margin-top: 20px;
}

.upgrade-account-number-tier {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  background: #fff8e1;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;

  img {
    width: 15px;
  }
}

.upgrade-text {
  color: #414f5f;
  margin-bottom: 5px;
}

.upgrade-input {
  border: 1px solid #dde2ec;
  outline: none;
  margin-bottom: 10px;
}

.disclaimer-content-wrapper {
  .upgrade-sub-title {
    font-size: 12px;
    color: #414f5f;
    font-weight: 500;
    border-bottom: 1px solid #dde2ec;
    padding: 0px 0px 10px 0px;
  }
  .upgrade-wrapper {
    border-bottom: 1px solid #dde2ec;
    padding: 10px 0px;

    p:first-child {
      font-size: 12px;
      color: #414f5f;
      font-weight: 400;
      margin-bottom: 0px;
    }
    p:last-child {
      font-size: 12px;
      color: #414f5f;
      font-weight: 600;
      margin-bottom: 0px;
    }
  }

  .no-border-bottom {
    border-bottom: 0px;
  }
}

.disclaimer-footer {
  text-align: center;
  font-size: 12px;
  margin-top: 10px;
}

.kyc-tabs {
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  border-bottom: 1px solid #dee2e6;
  margin-right: 0px;
}

.view-account-holder {
  display: flex;
  align-items: center;
  gap: 30px;
  width: 50%;
  justify-content: flex-end;

  .view-text {
    color: #2376f3;
    margin-bottom: 0px;
    font-weight: 600;
    cursor: pointer;
  }

  @media (max-width: 768px) {
    display: block;
    width: 100%;
    margin-top: 1rem;
    text-align: center;
  }
}

.reason_for_action {
  border: 2px solid #dde2ec;
  background-color: #f9fbfd;
  padding: 5px 10px 5px 10px;
  border-radius: 5px;
  color: #414f5f;
}

.upgrade-feedback {
  color: #ffffff;
  padding: 5px 10px 5px 10px;

  &.approved {
    background-color: #24b314;
  }
  &.declined {
    background-color: #f32345;
  }
}

.account-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.pending-reactivation {
  display: flex;
  align-items: center;
  gap: 10px;

  p {
    margin-bottom: 0px;
    color: #414f5f;
  }

  img {
    height: 1rem;
  }
}

.form-control-modal {
  background-color: red;
}

.upgrade-tier-indication {
  margin-bottom: 0px !important;
  @media (min-width: 1140px) {
    div.text-tooltip--content {
      display: none;
    }
  }
}

.supporting-document-tooltip {
  cursor: pointer;
  margin-bottom: 4px;
}
.supporting-document {
  left: -8rem;
}
.supporting-document-header {
  font-size: 14px;
}
.vba-limit-request-wrapper {
  margin-top: 20px;
  margin-bottom: 40px;
  .vba-limit-banner-container {
    width: 100%;
    min-height: 58px;
    background-color: #dde2ec;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  .vab-limit-text-label {
    font-family: 'Averta PE';
    font-size: 14px;
  }
  .vab-limit-text-400 {
    font-weight: 400;
  }
  .vab-limit-text-600 {
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
  }
  .vba-limit-color {
    color: #2376f3;
  }
  .vba-img-style {
    transform: rotate(180deg);
  }
  .hide-text-vba-limit {
    opacity: 0.2;
    cursor: not-allowed;
  }
  .ml-10-vba-limit {
    margin-left: 10px;
  }
  img {
    width: 15px;
  }
}
