import { Link, Redirect, Route, Switch } from 'react-router-dom';

import useStore from '+store';

import PoolAccounts from './PoolAccounts';
import PoolAccountDetails from './PoolAccounts/PoolAccountDetails';
import PoolAccountTransactionDetails from './PoolAccounts/PoolAccountTransactionDetails';
import VirtualAccounts from './VirtualAccounts';
import VirtualAccountDetails from './VirtualAccounts/VirtualAccountDetails';
import VirtualAccountHoldersDetails from './VirtualAccounts/VirtualAccountHoldersDetails';

import './index.scss';

export function AccountsComponent() {
  const { permissions } = useStore();

  const canViewPoolAccounts = permissions?.pool_account === 'view';
  const canViewVirtualAccounts = permissions?.virtual_account === 'view';

  return (
    <div className="content-i">
      <main className="accounts">
        <div>
          <h1 className="form-header">Local Accounts for Global Businesses</h1>
          <p className="form-header__description">
            Expand your business globally and receive payments just like a local. Explore accounts to receive payments in different
            currencies from all over the world and simplify your payment processes.
          </p>
        </div>

        <div className="accounts-options">
          {canViewVirtualAccounts && (
            <div className="accounts-options__item">
              <Link to="/dashboard/accounts/virtual-accounts" className="accounts-options__item-link">
                Fixed Virtual Accounts
              </Link>
              <p>Manage virtual accounts by merchants and their customers</p>
            </div>
          )}

          {canViewPoolAccounts && (
            <div className="accounts-options__item">
              <Link to="/dashboard/accounts/pool-accounts" className="accounts-options__item-link">
                Pool Accounts
              </Link>
              <p>Manage pool account references and maintain compliance effortlessly.</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

export default function Accounts() {
  const { permissions } = useStore();

  const canViewPoolAccounts = permissions?.pool_account === 'view';
  const canViewVirtualAccounts = permissions?.virtual_account === 'view';

  const getDefaultRedirect = () => {
    if (canViewVirtualAccounts) return '/dashboard/accounts/virtual-accounts';
    if (canViewPoolAccounts) return '/dashboard/accounts/pool-accounts';
    return '/dashboard';
  };

  return (
    <div className="virtual-accounts__container">
      <Switch>
        <Route exact path="/dashboard/accounts">
          <AccountsComponent />
        </Route>

        <Route
          exact
          path="/dashboard/accounts/virtual-accounts"
          render={() => (canViewVirtualAccounts ? <VirtualAccounts /> : <Redirect to={getDefaultRedirect()} />)}
        />
        <Route
          path="/dashboard/accounts/virtual-accounts/details/:id"
          render={() => (canViewVirtualAccounts ? <VirtualAccountDetails /> : <Redirect to={getDefaultRedirect()} />)}
        />
        <Route
          path="/dashboard/accounts/virtual-accounts/holders/:id"
          render={() => (canViewVirtualAccounts ? <VirtualAccountHoldersDetails /> : <Redirect to={getDefaultRedirect()} />)}
        />

        <Route
          exact
          path="/dashboard/accounts/pool-accounts"
          render={() => (canViewPoolAccounts ? <PoolAccounts /> : <Redirect to={getDefaultRedirect()} />)}
        />
        <Route
          path="/dashboard/accounts/pool-accounts/:reference/transactions/:id"
          render={() => (canViewPoolAccounts ? <PoolAccountTransactionDetails /> : <Redirect to={getDefaultRedirect()} />)}
        />
        <Route
          path="/dashboard/accounts/pool-accounts/:id"
          render={() => (canViewPoolAccounts ? <PoolAccountDetails /> : <Redirect to={getDefaultRedirect()} />)}
        />

        <Route path="*">
          <Redirect to={getDefaultRedirect()} />
        </Route>
      </Switch>
    </div>
  );
}
