import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import GenerateReferenceModal from '../GenerateReferenceModal';

const onClose = vi.fn();
const refetch = vi.fn();

const MockedGenerateReferenceModal = () => {
  return (
    <MockIndex>
      <GenerateReferenceModal onClose={onClose} refetch={refetch} currency="GHS" />
    </MockIndex>
  );
};
describe('GenerateReferenceModal', () => {
  it('It is accessible', async () => {
    const { container } = render(<MockedGenerateReferenceModal />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('It renders form fields', () => {
    render(<MockedGenerateReferenceModal />);
    expect(screen.getByText(/Customer name/i)).toBeInTheDocument();
    expect(screen.getByText(/Customer email/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/Who is this reference for?/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/Enter the customer’s email for this reference/i)).toBeInTheDocument();
  });

  it('It disables the submit button when the form is empty', () => {
    render(<MockedGenerateReferenceModal />);
    const proceedBtn = screen.getByRole('button', { name: /Proceed/i });
    expect(proceedBtn).toBeDisabled();
  });

  it('It should disable submit button when email is invalid', async () => {
    render(<MockedGenerateReferenceModal />);
    const nameInput = screen.getByPlaceholderText(/Who is this reference for?/i);
    const emailInput = screen.getByPlaceholderText(/Enter the customer’s email for this reference/i);

    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, 'invalid-email');

    const proceedBtn = screen.getByRole('button', { name: /Proceed/i });
    expect(proceedBtn).toBeDisabled();
  });

  it('It should not allow numbers in customer name field', async () => {
    render(<MockedGenerateReferenceModal />);
    const nameInput = screen.getByPlaceholderText(/Who is this reference for?/i);
    const emailInput = screen.getByPlaceholderText(/Enter the customer’s email for this reference/i);

    await userEvent.type(nameInput, 'John123 Doe');
    await userEvent.type(emailInput, '<EMAIL>');

    const proceedBtn = screen.getByRole('button', { name: /Proceed/i });
    expect(proceedBtn).toBeDisabled();
  });

  it('It enables the submit button only when all fields are filled', async () => {
    render(<MockedGenerateReferenceModal />);
    const nameInput = screen.getByPlaceholderText(/Who is this reference for?/i);
    const emailInput = screen.getByPlaceholderText(/Enter the customer’s email for this reference/i);

    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');

    const proceedBtn = screen.getByRole('button', { name: /Proceed/i });
    expect(proceedBtn).not.toBeDisabled();
  });
});
