import { useEffect, useState } from 'react';
import { useFormik } from 'formik';

import Copyable from '+containers/Dashboard/Shared/Copyable';
import Icon from '+containers/Dashboard/Shared/Icons';
import Modal from '+containers/Dashboard/Shared/Modal';
import { VirtualBankServices } from '+services/virtual-bank-services';
import { IModalProps } from '+types';
import { GenerateReferencePayloadType } from '+types/virtual-account-types';
import { EmailValidation } from '+utils';

import CheckLightGreen from '+assets/img/dashboard/check-light-green.svg';

import './GenerateReferenceModal.scss';

interface IGenerateReferenceModal {
  currency: string;
  onClose: () => void;
  refetch: () => void;
}

const GenerateReferenceModal = ({ onClose, refetch, currency }: IGenerateReferenceModal) => {
  const initialValues = {
    customer_name: '',
    customer_email: ''
  };
  const [existingReference, setExistingReference] = useState<string | null>('');
  const [newReference, setNewReference] = useState<string | null>('');
  const validate = (values: typeof initialValues) => {
    const errors: Partial<typeof initialValues> = {};

    if (!values.customer_name) {
      errors.customer_name = 'Customer name is required';
    }
    if (/\d/.test(values.customer_name)) {
      errors.customer_name = 'Name should not contain numbers';
    }

    const emailError = EmailValidation(values.customer_email);
    if (emailError) {
      errors.customer_email = emailError;
    }

    return errors;
  };

  const formik = useFormik({
    initialValues,
    validate,
    validateOnChange: true,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: async () => {
      await generateReference();
      refetch();
    }
  });
  const { getFieldProps, touched, errors } = formik;

  function renderFundingForm() {
    return (
      <>
        <div className="form-group">
          <label htmlFor="referenceFor">
            <h5 className="dark">Customer name</h5>
          </label>
          <input
            maxLength={100}
            id="referenceFor"
            type="text"
            className="form-control"
            placeholder="Who is this reference for?"
            {...getFieldProps?.('customer_name')}
          />
          {touched.customer_name && errors.customer_name && <p style={{ color: 'red', fontSize: '.875rem' }}>{errors.customer_name}</p>}
        </div>
        <div className="form-group">
          <label htmlFor="referenceFor">
            <h5 className="dark">Customer email</h5>
          </label>
          <input
            maxLength={100}
            id="referenceFor"
            type="text"
            className="form-control"
            placeholder="Enter the customer’s email for this reference..."
            {...getFieldProps?.('customer_email')}
          />
          {touched.customer_email && errors.customer_email && <p style={{ color: 'red', fontSize: '.875rem' }}>{errors.customer_email}</p>}
        </div>
        {existingReference && (
          <p className="fade-in d-flex p-3 rounded-lg align-items-start mb-0" style={{ backgroundColor: '#FFF8E1', marginTop: '1.5rem' }}>
            <Icon name="caution" className="mr-2" />
            <span style={{ fontSize: '13px', color: '#414F5F' }}>
              This email is already associated with another reference ID: <br />
              <strong>
                <Copyable text={existingReference || ''} />
              </strong>
            </span>
          </p>
        )}
      </>
    );
  }

  const cleanupForm = () => {
    formik.resetForm({ values: initialValues });
  };

  const { mutateAsync: mutateAccessStatus } = VirtualBankServices.useGeneratePoolAccountReference({
    onSuccess: data => {
      setNewReference(data?.data?.reference);
      cleanupForm();
    },
    onError: e => {
      const error = (e as { response?: { data?: { message: string; data: { reference: string } } } })?.response;
      const refExistsMessage = 'A reference already exists for this customer';
      if (error?.data?.message === refExistsMessage) {
        setExistingReference(error?.data?.data?.reference);
      }
    },
    showErrorMessage: !existingReference
  });
  let payload = {} as GenerateReferencePayloadType;
  const generateReference = async () => {
    payload = {
      customer_name: formik.values.customer_name,
      customer_email: formik.values.customer_email,
      currency
    };
    await mutateAccessStatus(payload);
  };

  useEffect(() => {
    if (existingReference && formik.values.customer_email) {
      setExistingReference(null);
    }
  }, [formik.values.customer_email]);

  const getCompletedDescription = () => (
    <div className="completed-description">
      <p style={{ fontSize: '13px', fontWeight: 'lighter', color: '#414F5F' }}>
        Your Pool Account reference has been generated successfully
      </p>
      <div className="my-4 p-3" style={{ fontSize: '13px', background: '#F9FBFD' }}>
        <p className="header"> Generated Reference ID:</p>
        <span>
          <Copyable text={newReference || ''} buttonClassName="mt-2" spanClassName="copyable" />
        </span>
      </div>
    </div>
  );

  const modalPropsOptions: Record<string, Partial<IModalProps>> = {
    sharedProps: {
      close: onClose
    },
    set_customer: {
      heading: 'Generate Pool Account Reference',
      description:
        'Provide your customers with payment references to include when making a payment. Please, confirm you want to generate this reference',
      content: renderFundingForm(),
      secondButtonAction: formik.submitForm,
      secondButtonText: 'Proceed',
      secondButtonActionIsTerminal: true,
      secondButtonDisable: !(formik.dirty && formik.isValid) || !!existingReference,
      completedHeading: 'Done!',
      completedDescription: getCompletedDescription(),
      completedImage: CheckLightGreen
    }
  };

  const modalProps = {
    ...modalPropsOptions.sharedProps,
    ...modalPropsOptions.set_customer
  };
  return (
    <Modal
      close={modalProps.close || (() => {})}
      heading={modalProps.heading}
      description={modalProps.description}
      content={modalProps.content}
      secondButtonAction={modalProps.secondButtonAction}
      secondButtonText={modalProps.secondButtonText}
      secondButtonActionIsTerminal={modalProps.secondButtonActionIsTerminal}
      secondButtonDisable={modalProps.secondButtonDisable}
      completedHeading={modalProps.completedHeading}
      completedDescription={modalProps.completedDescription}
      completedImage={modalProps.completedImage}
    />
  );
};

export default GenerateReferenceModal;
