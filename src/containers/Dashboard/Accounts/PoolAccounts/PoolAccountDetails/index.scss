@import 'styles/base/variables';

.pool-account-details {
  .header {
    display: flex;
    gap: 18px;
    align-items: center;
    padding-top: 24px;
  }

  .header-title {
    font-weight: 600;
    font-size: 28px;
    line-height: 40px;
    letter-spacing: -0.8px;
  }

  .header-details {
    margin: 30px 0 50px;

    h1 {
      font-size: 1.2rem;
      font-weight: 600;
      color: #292b2c;
      margin-bottom: 0;
      padding-bottom: 30px;
      border-bottom: 1px solid #d5d8db;
    }

    .header-details__info {
      display: flex;
      gap: 30px;
      flex-direction: column;

      padding-top: 24px;

      @media (min-width: $breakpoint-desktop-sm) {
        flex-direction: row;
        align-items: center;
        gap: 100px;
      }

      h2 {
        font-weight: 400;
        font-size: 1rem;
        margin-bottom: 10px;
        letter-spacing: 0px;
      }

      .header-details__info > div:not(:first-of-type) {
        height: fit-content;
        border-left: 1px solid #d5d8db;
        padding-left: 24px;
      }

      p {
        font-weight: 600;
        font-size: 1rem;
        line-height: 24px;
        letter-spacing: 0px;
        color: #3e4b5b;
        margin-bottom: 0;
      }

      span {
        font-weight: 600;
        font-size: 1rem;
        line-height: 28px;
        letter-spacing: 0px;
        color: #3e4b5b;
      }
    }
  }
}

.pool-account-details .header-details__info > div:not(:first-of-type) {
  height: fit-content;

  @media (min-width: $breakpoint-desktop-sm) {
    border-left: 1px solid #d5d8db;
    padding-left: 24px;
  }
}
