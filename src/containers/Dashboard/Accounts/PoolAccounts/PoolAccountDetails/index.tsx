import { useParams } from 'react-router-dom';

import Copyable from '+containers/Dashboard/Shared/Copyable';
import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceholder';
import Table from '+containers/Dashboard/Shared/Table';
import { useSearchQuery } from '+hooks';
import { VirtualBankServices } from '+services/virtual-bank-services';
import { IPaging, ParamsType, PoolAccountReferenceDetailsType, PoolAccountReferenceType } from '+types';
import { filteredOutObjectProperty, getDate, getTime, history, queriesParams } from '+utils';

import VirtualAccountsFilter from '../../VirtualAccounts/Shared/VirtualAccountsFilter';
import PoolAccountsDetailsRow from './PoolAccountDetailsRow';

import VBAIcon from '+assets/img/dashboard/vba-icon.svg';

import './index.scss';

export default function PoolAccountDetails() {
  const searchQuery = useSearchQuery();
  const page = (searchQuery.value.page as string) || '1';
  const limit = (searchQuery.value.limit as string) || '25';
  const status = (searchQuery.value.status as string) || undefined;
  const sortingParams = {
    ...filteredOutObjectProperty(searchQuery.value, [queriesParams.page, queriesParams.limit, queriesParams.currency, queriesParams.status])
  };
  const { id } = useParams<ParamsType>();

  const { data, isFetching: isFetchingReferenceData } = VirtualBankServices.useGetPoolAccountReference({
    params: { page, limit, ...sortingParams },
    errorMessage: 'There has been an error getting pool account information'
  });

  const referenceData = data as { data?: { data?: PoolAccountReferenceType[] } } | undefined;
  const reference = referenceData?.data?.data?.find((ref: { reference: string }) => ref.reference === id);

  const { data: transactionsResponse, isLoading: isFetchingTableData } = VirtualBankServices.useGetPoolAccountReferenceTransactions({
    params: { reference: id, page, limit, status, ...sortingParams },
    errorMessage: `There has been an error this account's information`
  });

  const tableData = transactionsResponse as { data?: { data?: PoolAccountReferenceDetailsType[]; paging?: IPaging } };
  const paging = tableData?.data?.paging;

  const getTableHeaders = () => {
    return [
      {
        value: 'Status'
      },
      {
        value: 'Transaction ID'
      },
      {
        value: 'Transaction Date'
      },
      {
        value: 'Amount'
      }
    ];
  };
  return (
    <>
      <div className="row">
        <button type="button" className="btn btn-link" onClick={() => history.goBack()}>
          <i className="os-icon os-icon-arrow-left7" />
          <span style={{ fontSize: '13px', fontWeight: '500' }}>Go Back</span>
        </button>
      </div>

      <div className="pool-account-details">
        {isFetchingReferenceData ? (
          <LoadingPlaceholder type="text" />
        ) : (
          <>
            <div className="header">
              <div>
                <img src={VBAIcon} alt="" style={{ width: '3.2rem', paddingTop: '0.3rem' }} />
              </div>
              <div className="header-title">
                <Copyable text={id} copyText="Copied" showCopyText={false} />
              </div>
            </div>
            <div className="header-details">
              <h1>Pool Account Reference Summary</h1>
              <div className="header-details__info">
                <div>
                  <h2>Customer Name</h2>
                  <p className="value">{reference?.customer_name}</p>
                </div>
                <div>
                  <h2>Customer Email</h2>
                  <p className="value">{reference?.customer_email}</p>
                </div>
                <div>
                  <h2>Created At</h2>
                  <p>
                    <span>{getDate(reference?.updated_at || '')}</span>
                    <span className="annotation" style={{ marginLeft: '5px' }}>
                      {getTime(reference?.updated_at || '')}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
        <div className="os-tabs-controls os-tabs-complex settlement-tabs">
          <div style={{ width: '100%' }}>
            <VirtualAccountsFilter type={'pool_accounts_details'} totalCount={paging?.total_items} Tab={'Transactions'} />
          </div>
        </div>
        <Table
          tableClassName={'--vba-pool-details'}
          headings={getTableHeaders()}
          hasPagination
          loading={isFetchingTableData}
          current={paging?.current}
          totalItems={paging?.total_items || 0}
          pageSize={paging?.page_size}
          limitAction={c => searchQuery.setQuery({ limit: String(c) })}
          actionFn={c => searchQuery.setQuery({ page: String(c) })}
          annotation="accounts"
          emptyStateHeading={`No Pool Accounts Transactions yet.`}
          emptyStateMessage={`You currently have not created any Pool Accounts Transactions.`}
          tableWrapperClassName="vba-container sub"
        >
          <PoolAccountsDetailsRow rowData={tableData?.data?.data || []} />
        </Table>
      </div>
    </>
  );
}
