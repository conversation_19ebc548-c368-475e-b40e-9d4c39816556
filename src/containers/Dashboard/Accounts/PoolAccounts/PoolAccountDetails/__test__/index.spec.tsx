import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import PoolAccountDetails from '../index';

const MockedPoolAccountDetails = () => (
  <MockIndexWithRoute initialEntries={['/dashboard/accounts/pool-accounts/KPY-OY-JANE']} route="/dashboard/accounts/pool-accounts/:id">
    <PoolAccountDetails />
  </MockIndexWithRoute>
);

describe('PoolAccountDetails', () => {
  test('PoolAccountDetails is accessible', async () => {
    const { container } = render(<MockedPoolAccountDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('It Renders customer information correctly', async () => {
    render(<MockedPoolAccountDetails />);
    expect(await screen.findByText(/Customer Name/i)).toBeInTheDocument();
    expect(await screen.findByText(/Jane <PERSON>/i)).toBeInTheDocument();
    expect(await screen.findByText(/Customer Email/i)).toBeInTheDocument();
    expect(await screen.findByText(/<EMAIL>/i)).toBeInTheDocument();
    expect(await screen.findByText(/Created At/i)).toBeInTheDocument();

    // Use more flexible assertions for date parts
    const dateElement = await screen.findByText(/May/i);
    expect(dateElement).toBeInTheDocument();

    // Look for the time in a flexible way
    const timePattern = /\d{1,2}:\d{2}/i;
    const timeElements = await screen.findAllByText(timePattern);
    expect(timeElements.length).toBeGreaterThan(0);
  });
});
