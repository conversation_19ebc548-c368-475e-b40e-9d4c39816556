import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import { mockPoolAccountsDetails } from '+__mock__/mockData';
import MockIndex from '+mock/MockIndex';

import PoolAccountDetailsRow from '../PoolAccountDetailsRow';

const MockedPoolAccountsDetailsRow = () => (
  <MockIndex>
    <PoolAccountDetailsRow rowData={mockPoolAccountsDetails.data} />
  </MockIndex>
);

describe('PoolAccountsRow', () => {
  it('It is accessible', async () => {
    const { container } = render(<MockedPoolAccountsDetailsRow />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('It renders row data correctly', () => {
    render(<MockedPoolAccountsDetailsRow />);
    expect(screen.getByText(/SUCCESS/i)).toBeInTheDocument();
    expect(screen.getByText(/TXN-123456/i)).toBeInTheDocument();

    // Check for date and time elements in a more flexible way
    const dateRegex = /\d{1,2}\s+\w{3}\s+\d{4}/i; // Matches date formats like "26 May 2025"
    const timeRegex = /\d{1,2}:\d{2}/i; // Matches time formats like "9:55"

    // The date and time might be in the same element or separate elements
    const elements = screen.getAllByText(content => {
      return dateRegex.test(content) || timeRegex.test(content);
    });
    expect(elements.length).toBeGreaterThan(0);

    expect(screen.getByText(/9,900/i)).toBeInTheDocument();
    expect(screen.getByText(/NGN/i)).toBeInTheDocument();
  });
});
