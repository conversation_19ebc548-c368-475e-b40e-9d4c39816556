import { PoolAccountReferenceDetailsType } from '+types/virtual-account-types';
import { capitalize, formatAmount, getDateAndTime, history, switchStatus } from '+utils';

const PoolAccountsDetailsRow = ({ rowData }: { rowData: Array<PoolAccountReferenceDetailsType> }) => {
  const handleClick = (reference: string, transaction_reference: string) =>
    history.push(`/dashboard/accounts/pool-accounts/${reference}/transactions/${transaction_reference}`);

  return rowData?.map?.(tx => {
    return (
      <div
        key={`transaction_${tx.transaction_reference}`}
        className="div-table --vba-pool-details --row cards-transactions-row"
        role="button"
        tabIndex={0}
        onClick={() => handleClick(tx.pool_account_reference, String(tx.transaction_reference))}
        onKeyUp={e => {
          if (e.key === 'Enter') e.preventDefault();
          handleClick(tx.pool_account_reference, String(tx.transaction_reference));
        }}
        data-testid="reserved-card-list-item"
      >
        <div>
          <span className="body-row-header">Status:</span>
          <span className={`status-pill smaller ${switchStatus(tx?.status.toLocaleLowerCase())}`} />
          <span>{capitalize(tx?.status === 'settled' ? 'success' : tx?.status)}</span>
        </div>
        <div className="--column">
          <span className="body-row-header">Transaction ID:</span>
          <span className="font-weight-500 text-uppercase" style={{ color: '#007bff' }}>
            {tx.transaction_reference}
          </span>
        </div>
        <div className="--column">
          <span className="body-row-header">Transaction Date:</span>
          <span className="value">{getDateAndTime(tx.transaction_date)}</span>
        </div>
        <div className="--column">
          <span className="body-row-header">Amount:</span>
          <span className="">
            <span style={{ color: '#414F5F', marginRight: '6px', fontSize: '16px', fontWeight: 600 }}>{formatAmount(tx.amount_paid)}</span>
            <span className="grey-text">{tx.currency}</span>
          </span>
        </div>
      </div>
    );
  });
};

export default PoolAccountsDetailsRow;
