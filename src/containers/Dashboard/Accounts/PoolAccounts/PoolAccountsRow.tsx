import { PoolAccountReferenceType } from '+types/virtual-account-types';
import { getDateAndTime, history } from '+utils';

const PoolAccountsRow = ({ rowData }: { rowData: Array<PoolAccountReferenceType> }) => {
  const handleClick = (reference: string) => history.push(`/dashboard/accounts/pool-accounts/${reference}`);

  return rowData?.map?.(tx => {
    return (
      <div
        key={`transaction_${tx.reference}`}
        className="div-table --vba-pool --row cards-transactions-row"
        role="button"
        tabIndex={0}
        onClick={() => handleClick(tx.reference)}
        onKeyUp={e => {
          if (e.key === 'Enter') e.preventDefault();
          handleClick(tx.reference);
        }}
        data-testid="reserved-card-list-item"
      >
        <div className="--column">
          <span className="body-row-header">Reference:</span>
          <span className="font-weight-500 text-uppercase" style={{ color: '#007bff' }}>
            {tx.reference}
          </span>
        </div>
        <div className="--column">
          <span className="body-row-header">Customer Name:</span>
          <span className="value">{tx.customer_name}</span>
        </div>
        <div className="--column">
          <span className="body-row-header">Date Created:</span>
          <span className="grey-text">{getDateAndTime(tx.created_at)}</span>
        </div>
      </div>
    );
  });
};

export default PoolAccountsRow;
