@import 'styles/base/variables';

.pool-accounts {
  .heading-box {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
    margin-bottom: 32px;

    @media (min-width: $breakpoint-desktop-sm) {
      flex-direction: row;
      align-items: flex-start;
    }

    .header-text {
      margin-top: 30px;
      width: 100%;
      .header {
        font-weight: 600;
        font-size: 1.2rem;
        line-height: 2rem;
        letter-spacing: 0;
        margin-bottom: 0.5rem;
        color: #414f5f;
      }
      .desc {
        font-size: 1rem;
        color: #94a7b7;
        margin-bottom: 0;
        width: 70%;
      }
    }

    .page-action-btns {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      gap: 12px;

      .btn,
      .btn-primary {
        padding: 10px 14px !important;
      }

      @media (min-width: $breakpoint-desktop-sm) {
        justify-content: flex-end;
      }
    }
  }

  .os-tabs-controls {
    margin-bottom: 24px;
  }
}
