import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import { mockPoolAccounts } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';

import PoolAccountsRow from '../PoolAccountsRow';

const MockedPoolAccountsRow = () => (
  <MockIndex>
    <PoolAccountsRow rowData={mockPoolAccounts.data.data} />
  </MockIndex>
);

describe('PoolAccountsRow', () => {
  it('is accessible', async () => {
    const { container } = render(<MockedPoolAccountsRow />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('renders row data correctly', () => {
    render(<MockedPoolAccountsRow />);
    expect(screen.getByText(/KPY-OY-JANE/i)).toBeInTheDocument();
    expect(screen.getByText(/Jane <PERSON>/i)).toBeInTheDocument();

    // Instead of checking for exact formatted date, check that some date is rendered
    const dateElements = screen.getAllByText(/\d{1,2}\s+\w{3}\s+\d{4}/);
    expect(dateElements.length).toBeGreaterThan(0);
  });
});
