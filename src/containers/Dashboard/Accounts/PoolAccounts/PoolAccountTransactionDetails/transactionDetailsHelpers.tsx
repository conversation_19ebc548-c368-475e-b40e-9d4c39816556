import Tooltip from '+containers/Dashboard/Shared/Tooltip';
import { SummaryItemType } from '+containers/Dashboard/TransactionDetailsNew/types';
import Copyable from '+dashboard/Shared/Copyable';
import { PoolAccountReferenceDetailsType } from '+types/virtual-account-types';
import { capitalize, formatAmount, getDateAndTime, switchCurrency, switchStatus } from '+utils';

import InfoIcon from '+assets/img/dashboard/information-button.svg';

export function generateTransactionSummaryFrom(data: PoolAccountReferenceDetailsType): Array<SummaryItemType> {
  return [
    {
      label: (
        <>
          Net Amount
          <Tooltip
            type="net_amount"
            image={InfoIcon}
            message={
              <em>
                Net Amount <br /> This is the amount less fee
              </em>
            }
          />
        </>
      ),
      value: `${formatAmount(data?.net_amount ?? 0)} ${data?.currency}`
    },
    {
      label: (
        <>
          Fee
          <Tooltip
            type="net_amount"
            image={InfoIcon}
            message={
              <em>
                Fees <br /> Total charges incurred while processing this transaction.
              </em>
            }
          />
        </>
      ),
      value: `${formatAmount(data?.fee ?? 0)} ${data?.currency}`
    },
    { label: 'Date/Time', value: getDateAndTime(data?.transaction_date) },
    {
      label: 'Transaction Reference',
      value: (
        <>
          <Copyable text={data?.transaction_reference.toUpperCase?.()} textModifier={(text: string) => `${text.substring(0, 15)}...`} />{' '}
          <br />
        </>
      )
    }
  ];
}

export function generateMoreDetailsFrom(data: PoolAccountReferenceDetailsType): Array<SummaryItemType> {
  return [
    {
      label: 'Status',
      value: (
        <>
          <span className={`status-pill smaller ${switchStatus(data?.status.toLocaleLowerCase())}`} />{' '}
          {capitalize(data?.status.toLocaleLowerCase() === 'settled' ? 'success' : data?.status.toLocaleLowerCase())}
        </>
      )
    },
    {
      label: 'Amount Paid',
      value: data?.amount_paid ? `${formatAmount(data.amount_paid)} ${data?.currency}` : 'Not Available'
    },
    {
      label: 'Currency',
      value: data?.currency ? <strong>{switchCurrency[data?.currency as keyof typeof switchCurrency]}</strong> : 'Not Available'
    },
    {
      label: 'Date Created',
      value: data?.created_at ? getDateAndTime(data?.created_at) : 'Not Available'
    },
    {
      label: 'Date Completed',
      value: data?.updated_at ? getDateAndTime(data?.updated_at) : 'Not Available',
      hidden: data?.status.toLowerCase() === 'success' || data?.status.toLowerCase() === 'settled'
    },
    {
      label: 'Settlement Time',
      value: data?.updated_at ? getDateAndTime(data?.updated_at) : 'Not Available',
      hidden: data?.status.toLowerCase() === 'success' || data?.status.toLowerCase() === 'rejected'
    }
  ];
}
