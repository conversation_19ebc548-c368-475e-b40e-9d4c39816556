import { useParams } from 'react-router-dom';

import TransactionDetails from '+containers/Dashboard/TransactionDetailsNew';
import { VirtualBankServices } from '+services/virtual-bank-services';
import { PoolAccountReferenceDetailsType } from '+types/virtual-account-types';
import { formatAmount, switchTrxnMessage } from '+utils';

import { generateMoreDetailsFrom, generateTransactionSummaryFrom } from './transactionDetailsHelpers';

export default function PoolAccountTransactionDetails() {
  const { id } = useParams<{ id: string }>();
  const { data: transactionData, isLoading: isFetchingTransactionData } = VirtualBankServices.useGetPoolAccountReferenceTransaction({
    id,
    errorMessage: `There has been an error this account's information`
  });

  const data = transactionData?.data;
  const summaryList = generateTransactionSummaryFrom(data as PoolAccountReferenceDetailsType);
  const moreDetailsList = generateMoreDetailsFrom(data as PoolAccountReferenceDetailsType);
  return (
    <>
      <TransactionDetails isLoading={isFetchingTransactionData}>
        <TransactionDetails.Header
          heading={formatAmount(data?.amount_paid)}
          currency={data?.currency}
          summaries={summaryList}
          statusLabels={[
            {
              status: switchTrxnMessage[(data as PoolAccountReferenceDetailsType)?.status.toLocaleLowerCase()]?.name,
              statusBg: switchTrxnMessage[(data as PoolAccountReferenceDetailsType)?.status.toLocaleLowerCase()]?.backgroundColor,
              statusColor: switchTrxnMessage[(data as PoolAccountReferenceDetailsType)?.status.toLocaleLowerCase()]?.color
            }
          ]}
        />
        <TransactionDetails.Section heading="More Transaction Details" summaries={moreDetailsList} />
      </TransactionDetails>
    </>
  );
}
