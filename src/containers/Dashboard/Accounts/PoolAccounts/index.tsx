import { useState } from 'react';

import CurrencyTabs from '+containers/Dashboard/Shared/CurrencyTabs';
import Table from '+containers/Dashboard/Shared/Table';
import { useFeedbackHandler, useSearchQuery } from '+hooks';
import { VirtualBankServices } from '+services/virtual-bank-services';
import useStore from '+store';
import { PoolAccountData } from '+types';
import { filteredOutObjectProperty, history, queriesParams } from '+utils';

import VirtualAccountsFilter from '../VirtualAccounts/Shared/VirtualAccountsFilter';
import GenerateReferenceModal from './PoolAccountsModal/GenerateReferenceModal';
import PoolAccountsRow from './PoolAccountsRow';

import './index.scss';

export default function PoolAccounts() {
  const searchQuery = useSearchQuery();
  const { feedbackInit } = useFeedbackHandler();
  const setSearchQuery = (value: string) => {
    searchQuery.setQuery({ currency: value });
  };

  const page = (searchQuery.value.page as string) || '1';
  const limit = (searchQuery.value.limit as string) || '10';
  const currency = (searchQuery.value.currency as string) || 'GHS';
  const [showGenerateReference, setShowGenerateReference] = useState(false);
  const sortingParams = {
    ...filteredOutObjectProperty(searchQuery.value, [queriesParams.page, queriesParams.limit])
  };

  const getTableHeaders = () => {
    return [
      {
        value: 'Reference'
      },
      {
        value: 'Customer Name'
      },
      {
        value: 'Date Created'
      }
    ];
  };

  const {
    data,
    isFetching: isFetchingTableData,
    refetch: refetchTableData
  } = VirtualBankServices.useGetPoolAccountReference({
    params: { page, limit, currency, ...sortingParams },
    onError: e => {
      const error = e as { response?: { data?: { message: string } } };
      const errorMessage = error.response?.data?.message || 'There has been an error getting pool accounts information';
      feedbackInit({
        componentLevel: false,
        message: errorMessage,
        type: 'danger'
      });
    }
  });
  const tableData = data as PoolAccountData;
  const paging = tableData?.data?.paging;
  const availableCurrencies = useStore(storedState => storedState.availableCurrencies);
  const defaultMerchant = useStore(state => state.defaultMerchant);
  const poolAccountsCurrencies = defaultMerchant.productAccess?.pool_account || {};

  const availablePoolAccountCurrencies = Object.keys(poolAccountsCurrencies).filter(
    e => poolAccountsCurrencies[e as keyof typeof poolAccountsCurrencies] === true
  );
  return (
    <div className="pool-accounts">
      <div className="row">
        <button type="button" className="btn btn-link" onClick={() => history.goBack()}>
          <i className="os-icon os-icon-arrow-left7" />
          <span style={{ fontSize: '16px', fontWeight: '500' }}>Go Back</span>
        </button>
      </div>
      <section className="heading-box">
        <div className="header-text">
          <h4 className="header">Generate References</h4>
          <p className="desc">Effortlessly generate payment references for your customers to use when making payments.</p>
        </div>
        <div className="page-action-btns">
          <button
            onClick={() => setShowGenerateReference(true)}
            data-testid="page-action-btn"
            className="btn btn-primary p-2"
            type="button"
          >
            <span className="os-icon os-icon-plus" />
            <span>Generate Reference</span>
          </button>
        </div>
      </section>
      <section className="os-tabs-w">
        <div className="os-tabs-controls os-tabs-complex">
          <CurrencyTabs
            currencies={availableCurrencies}
            activeCurrency={currency}
            onClick={setSearchQuery}
            disabledOption
            allowedCurrencies={availablePoolAccountCurrencies}
          />
        </div>
      </section>
      <div
        className="os-tabs-controls os-tabs-complex settlement-tabs"
        style={{
          flexDirection: 'column',
          alignItems: 'flex-start',
          gap: '20px',
          marginRight: '0px'
        }}
      >
        <div style={{ width: '100%' }}>
          <VirtualAccountsFilter type={'pool_accounts'} totalCount={paging?.total_items} Tab={'References'} />
        </div>
      </div>
      <Table
        tableClassName={'--vba-pool'}
        headings={getTableHeaders()}
        hasPagination
        loading={isFetchingTableData}
        current={paging?.current}
        totalItems={paging?.total_items || 0}
        pageSize={paging?.page_size}
        limitAction={c => searchQuery.setQuery({ limit: String(c) })}
        actionFn={c => searchQuery.setQuery({ page: String(c) })}
        annotation="accounts"
        emptyStateHeading={`No Pool Accounts References yet.`}
        emptyStateMessage={`You currently have not created any Pool Accounts References.`}
        tableWrapperClassName="vba-container sub"
      >
        <PoolAccountsRow rowData={tableData?.data?.data} />
      </Table>
      {showGenerateReference && (
        <GenerateReferenceModal
          onClose={() => setShowGenerateReference(false)}
          refetch={() => {
            void refetchTableData();
          }}
          currency={currency}
        />
      )}
    </div>
  );
}
