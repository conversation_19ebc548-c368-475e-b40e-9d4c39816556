@import 'styles/base/variables';

.webhook-title {
  align-items: center;
  border-bottom: 0.5px solid #a9afbc;
  display: flex;
  justify-content: space-between;

  & > span {
    color: #4e555b;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;

    @media (max-width: 768px) {
      font-size: 0.875rem;
    }
  }

  & > div {
    margin: 0 0 0.75rem 3rem;

    button {
      margin-left: 1.5rem;
    }
  }
}
.webhooks-section {
  display: block;

  .webhook-details {
    padding: 0;
    border-right: none;
    width: 100%;

    .webhook-metadata {
      p {
        color: #636c72;
        display: flex;
        font-size: 0.9rem;
        font-weight: 500;

        span {
          color: #a9afbc;
          flex-basis: 35% !important;
        }

        span:nth-of-type(2) {
          color: #414f5f;
          font-weight: 400;
        }
      }
    }

    .response-span {
      font-weight: 400;
      border-radius: 9px;
      padding: 4px 7px;
      font-size: 12px;
      margin-right: 8px;
    }

    .status-text {
      color: #414f5f;
      font-size: 13px;
      font-weight: 400;
      margin: 3px 7px 5px 0;
    }

    .notification {
      color: #94a7b7;
      font-size: 14px;
      font-weight: 400;
    }

    .webhook-url {
      padding: 14px 20px;
      border-radius: 10px;
      border: 2px solid #dde2ec;
      background: #f9fbfd;
      color: #414f5f;
      font-size: 14px;
      letter-spacing: -0.057px;
      margin-top: 5px;
    }

    .webhook-date {
      color: #94a7b7;
      font-size: 12px !important;
      font-weight: 400;
      line-height: 24px;
    }

    .status-margin {
      margin: 3px 7px 5px 0;
    }

    .status-smaller {
      width: 5px;
      height: 5px;
    }
  }

  .webhook-attempt {
    color: #aabdce;
    font-size: 10px !important;
    font-style: normal;
    margin-bottom: 4px;
    line-height: 22px;
  }

  .webhook-payload {
    padding: 0;
    width: 100%;
    margin-top: 50px;
  }

  .copy-wrapper {
    color: #2376f3;
    font-weight: 500;
    gap: 0.5rem;

    img {
      height: 456px;
      width: 556px;
    }
  }

  @media (min-width: $breakpoint-desktop) {
    display: flex;

    .webhook-details {
      padding-right: 40px;
      border-right: 1px solid #d5d8db;
      width: 50%;
    }

    .webhook-payload {
      padding-left: 40px;
      width: 50%;
      margin-top: 0;
    }
  }
}

.btn-webhook {
  background: #eaf2fe;
  border: none;
  color: #414f5f;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.6rem 1.75rem;
  border-radius: 8px;
}

.text-tooltip--content {
  color: white;
  font-weight: 400;
  font-size: 0.8rem;
}

.no-webhook {
  margin: 1rem 0 0;
  text-align: center;
}
