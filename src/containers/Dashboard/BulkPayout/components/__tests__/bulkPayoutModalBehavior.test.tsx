import { act, renderHook } from '@testing-library/react';

import { mockProfile, mockSessionStorage } from '+mock/shared-mocks';
import useStore from '+store';

import useBulkPayoutTipModal from '../useBulkPayoutTipModal';

useStore.setState({ profile: mockProfile });

describe('Bulk Payout Modal - "Don\'t Show Me Again" Behavior', () => {
  test('SCENARIO 1: First time user - modal should show', () => {
    mockSessionStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useBulkPayoutTipModal());

    const shouldShow = result.current.checkShouldShowModal();
    expect(shouldShow).toBe(true);
    expect(mockSessionStorage.getItem).toHaveBeenCalledWith('<EMAIL>');
  });

  test('SCENARIO 2: User clicks X button - auto-checks checkbox and marks as shown', () => {
    mockSessionStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useBulkPayoutTipModal());

    act(() => {
      result.current.setShowHintsModal(true);
    });

    expect(result.current.showHintsModal).toBe(true);

    act(() => {
      result.current.handleClose();
    });

    expect(result.current.showHintsModal).toBe(false);
    expect(mockSessionStorage.setItem).toHaveBeenCalledWith('<EMAIL>', 'true');
  });

  test('SCENARIO 3: Page refresh after X click - modal should NOT show', () => {
    mockSessionStorage.getItem.mockReturnValue('true');

    const { result } = renderHook(() => useBulkPayoutTipModal());

    const shouldShow = result.current.checkShouldShowModal();
    expect(shouldShow).toBe(false);
  });

  test('SCENARIO 4: After logout/login - modal should show again', () => {
    mockSessionStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useBulkPayoutTipModal());

    const shouldShow = result.current.checkShouldShowModal();
    expect(shouldShow).toBe(true);
  });
});
