import { act, renderHook } from '@testing-library/react';

import { mockProfile, mockSessionStorage } from '+mock/shared-mocks';
import useStore from '+store';

import useBulkPayoutTipModal from '../useBulkPayoutTipModal';

useStore.setState({ profile: mockProfile });

describe('useBulkPayoutTipModal', () => {
  it('should return initial state', () => {
    mockSessionStorage.getItem.mockReturnValue(null);
    mockSessionStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useBulkPayoutTipModal());

    expect(result.current.showHintsModal).toBe(false);
    expect(typeof result.current.setShowHintsModal).toBe('function');
    expect(typeof result.current.getBulkPayoutHintsProps).toBe('function');
    expect(typeof result.current.checkShouldShowModal).toBe('function');
    expect(typeof result.current.markAsShown).toBe('function');
    expect(typeof result.current.handleClose).toBe('function');
  });

  it('should check if modal should show based on session storage', () => {
    mockSessionStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useBulkPayoutTipModal());

    act(() => {
      const shouldShow = result.current.checkShouldShowModal();
      expect(shouldShow).toBe(true);
    });

    expect(mockSessionStorage.getItem).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should not show modal if already shown', () => {
    mockSessionStorage.getItem.mockReturnValue('true');

    const { result } = renderHook(() => useBulkPayoutTipModal());

    act(() => {
      const shouldShow = result.current.checkShouldShowModal();
      expect(shouldShow).toBe(false);
    });
  });

  it('should mark modal as shown when markAsShown is called', () => {
    const { result } = renderHook(() => useBulkPayoutTipModal());

    act(() => {
      result.current.markAsShown();
    });

    expect(mockSessionStorage.setItem).toHaveBeenCalledWith('<EMAIL>', 'true');
  });

  it('should handle close correctly - auto-check checkbox and mark as shown', () => {
    const { result } = renderHook(() => useBulkPayoutTipModal());

    act(() => {
      result.current.setShowHintsModal(true);
    });

    expect(result.current.showHintsModal).toBe(true);

    act(() => {
      result.current.handleClose();
    });

    expect(result.current.showHintsModal).toBe(false);
    expect(mockSessionStorage.setItem).toHaveBeenCalledWith('<EMAIL>', 'true');
  });
});
