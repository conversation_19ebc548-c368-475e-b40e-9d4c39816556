import { useState } from 'react';

import useStore from '+store';

import bulkPayoutInfo2 from '+assets/img/dashboard/bulkpayoutHints/bulk_pyt_acc_number.svg';
import bulkPayoutInfo3 from '+assets/img/dashboard/bulkpayoutHints/bulk_pyt_cellformat.svg';
import bulkPayoutInfo1 from '+assets/img/dashboard/bulkpayoutHints/bulk_pyt_template.svg';

const useBulkPayoutTipModal = () => {
  const [showHintsModal, setShowHintsModal] = useState<boolean>(false);
  const [dontShowAgain, setDontShowAgain] = useState<boolean>(false);
  const profile = useStore(state => state?.profile);

  const sessionKey = `bulkPayoutTipShown_${profile.email}`;

  const checkShouldShowModal = () => {
    if (!profile.email) return false;
    const hasShown = sessionStorage.getItem(sessionKey);
    return !hasShown;
  };

  const markAsShown = () => {
    if (profile.email) {
      sessionStorage.setItem(sessionKey, 'true');
    }
  };

  const handleClose = () => {
    // Auto-check the "Don't Show Me Again" checkbox when X is clicked
    setDontShowAgain(true);
    // Always mark as shown when closing via X button (auto-checkbox behavior)
    markAsShown();
    setShowHintsModal(false);
  };

  const getBulkPayoutHintsProps = () => {
    return {
      onSubmit: handleClose,
      secondButtonFinalText: 'Done',
      removeIconOnFinalStep: true,
      hasFirstButton: true,
      hasCheckBox: true,
      checked: dontShowAgain,
      onCheckedChange: () => setDontShowAgain(!dontShowAgain),
      size: 'md' as const,
      data: [
        {
          title: (
            <>
              <span className="text-accent">Important Tip</span> for creating Bulk Payout Sheet.
            </>
          ),
          description:
            'Account numbers starting with zeros would be erased in columns due to cell formatting.(e.g. “0*********” would be interpreted as “*********”). Here are two ways to fix!',
          image: bulkPayoutInfo1
        },
        {
          title: (
            <>
              You can begin account numbers with an <span className="text-success">apostrophe (&apos;)</span>
            </>
          ),
          description:
            'Modify the entry in each cell that has a number starting with (0) by adding an (‘) before the leading zero (0) to solve this. E.g ‘**********',
          image: bulkPayoutInfo2
        },
        {
          title: (
            <>
              You can change the cell format to <span className="text-success">“Plain Text”</span>
            </>
          ),
          description: 'You would retain the leading zero (0) in the cells if you change the cell formatting to “Plain text” or “Number”',
          image: bulkPayoutInfo3
        }
      ]
    };
  };

  return {
    showHintsModal,
    setShowHintsModal,
    getBulkPayoutHintsProps,
    checkShouldShowModal,
    markAsShown,
    handleClose
  };
};
export default useBulkPayoutTipModal;
