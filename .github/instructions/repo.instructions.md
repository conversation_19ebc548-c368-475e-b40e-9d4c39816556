---
description: '<PERSON>rapay Merchant Dashboard Frontend - React 18 + TypeScript fintech dashboard with Vite, <PERSON>ustand, React Query, and comprehensive testing. Includes payment processing, card issuance, settlements, and multi-currency support with enterprise-grade architecture patterns.'
applyTo: '**/*.{ts,tsx,js,jsx,scss,css,json,md}'
---

# Korapay Merchant Dashboard Frontend - VSCode Copilot Instructions

You are an AI assistant specialized in the **Korapay Merchant Dashboard Frontend** - a sophisticated **React 18 + TypeScript fintech dashboard** built with **Vite** for merchant payment processing and financial management.

## 🏗️ Project Architecture

This is a **production fintech application** with:

- **React 18.3.1** + **TypeScript 5.7.2** + **Vite** build system
- **Hybrid state management**: Zustand (auth/global) + React Query (server state)
- **Modular container architecture** with strict separation of concerns
- **Enterprise-grade testing** with Vitest, RTL, and MSW mocking
- **Real-time capabilities** via Pusher WebSocket integration

### Tech Stack Overview

```typescript
// Core Framework
React 18.3.1 + TypeScript 5.7.2 + Vite

// State Management
Zustand 4.3.9 (global state) + @tanstack/react-query 5.60.6 (server state)

// UI/Styling
Bootstrap 4.5.0 + SCSS + styled-components 5.1.1

// Forms & Validation
react-hook-form 7.34.0 + Formik 2.1.4 + Yup 1.3.2

// Testing
Vitest + @testing-library/react + MSW + jsdom 25.0.1

// Routing
react-router-dom 5.2.0 (legacy version)

// Real-time & APIs
axios 1.7.7 + Pusher + axios-auth-refresh 3.3.3
```

## 🚀 Development Workflow

### Command Reference

```bash
# Development (port 4200)
npm run start:dev        # Vite dev server
npm run gen              # Plop component/page generator

# Quality Assurance
npm run test             # Vitest with coverage
npm run type-check       # TypeScript validation
npm run lint:all         # ESLint with auto-fix
npm run format:all       # Prettier formatting

# Production
npm run build            # Vite production build with 3GB memory
npm run preview          # Preview production build
```

### Component Generation (ALWAYS USE)

```bash
npm run gen
# Follow prompts:
# 1. Component (c) or Page (p)?
# 2. Name: ComponentName
# 3. Path: Dashboard/ModuleName/components (for components)
#         Dashboard/ModuleName (for pages)
```

**Generation Rules:**

- Components MUST go in `shared` or `components` folders
- Pages CANNOT be created in `Shared` folders
- Valid containers: `Dashboard`, `External`, `Shared`, `Auth`

## 📁 Project Structure & Patterns

### Container Hierarchy (STRICT)

```
src/containers/
├── Auth/                 # Authentication flows only
├── Dashboard/            # Main merchant modules
│   ├── PayIn/           # Payment acceptance
│   ├── PayOut/          # Payment disbursements
│   ├── Balances/        # Account balances
│   ├── Settlements/     # Settlement management
│   ├── Issuing/         # Card issuance
│   ├── VirtualAccounts/ # Virtual account management
│   ├── Disputes/        # Chargeback/dispute handling
│   ├── Settings/        # User/merchant settings
│   └── Shared/          # Reusable dashboard components
├── External/            # Public-facing components
└── IconVisualizer/      # Icon management utility
```

### Module Structure Pattern

```
Dashboard/ModuleName/
├── index.tsx                    # Main route component
├── ModuleName.spec.tsx         # Module tests
├── components/                 # Non-routable components
│   ├── ComponentName.tsx
│   ├── ComponentName.scss
│   └── ComponentName.spec.tsx
└── SubRoute/                   # Nested route pages
    ├── index.tsx
    ├── components/
    └── SubRoute.spec.tsx
```

## 🔗 Import Aliases (MANDATORY)

```tsx
// Path aliases configured in tsconfig.json
import Component from '+containers/Dashboard/ModuleName';
import { usePageHeader, usePermissions } from '+hooks';
import APIRequest from '+services/api-services';
import useStore from '+store';
import { PermissionsType, UserType } from '+types/index';
import { history, Logger, sessionKeys } from '+utils';

import Logo from '+assets/img/logo.svg';

import '+styles/main.scss';
```

**Available Aliases:**

- `+*` → `src/*` (universal)
- `+components` → `src/components`
- `+containers` → `src/containers`
- `+store` → `src/store`
- `+hooks` → `src/hooks`
- `+utils` → `src/utils`
- `+services/*` → `src/services/*`
- `+types/*` → `src/types/*`
- `+assets/*` → `src/assets/*`
- `+styles` → `src/styles`

## 🔐 Permission System (CRITICAL)

**Every dashboard component MUST implement permission checks:**

```tsx
import { usePermissions } from '+hooks';

const MyComponent = () => {
  const permission = usePermissions('moduleAccess'); // from PermissionsType

  if (!permission) {
    return <AccessDenied />;
  }

  return (
    // Component content
  );
};
```

**Permission Hook Usage:**

```tsx
// Use specific permission keys from PermissionsType interface
const merchantPermission = usePermissions('merchant_management');
const paymentPermission = usePermissions('payment_processing');
const reportPermission = usePermissions('report_generation');
```

## 🌐 API Integration Patterns

### Centralized API Service (MANDATORY)

```tsx
import APIRequest from '+services/api-services';

// DON'T create new axios instances
// USE the existing 2300+ line APIRequest class

const MyComponent = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['transactions'],
    queryFn: () => APIRequest.getTransactions(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes default
    retry: 3
  });
};
```

**API Service Features:**

- **2300+ lines** of pre-built endpoints
- **Automatic auth refresh** with axios-auth-refresh
- **Request/response interceptors** for error handling
- **Built-in retry logic** and timeout management
- **Centralized error handling** with APIServiceError

### React Query Patterns

```tsx
import { useFetch, useSend } from '+hooks';

// Data fetching with useFetch (wraps useQuery)
const MyComponent = () => {
  const { data, isLoading, error, status, message, paging } = useFetch<TransactionType[]>('/api/i/transactions/payins', {
    params: { page: 1, limit: 20 },
    useAuth: true,
    showSuccessMessage: false,
    showErrorMessage: true,
    staleTime: 10 * 60 * 1000, // 10 minutes
    onSuccess: data => {
      console.log('Transactions loaded:', data);
    },
    select: response => response.data.data // Extract data from API response
  });

  return (
    <div>
      {isLoading && <div>Loading...</div>}
      {error && <div>Error: {message}</div>}
      {data && data.map(transaction => <div key={transaction.id}>{transaction.reference}</div>)}
    </div>
  );
};

// Mutations with useSend (wraps useMutation)
const CreatePaymentComponent = () => {
  const { mutate: createPayment, isLoading: isCreating } = useSend<PaymentPayload, PaymentResponse>({
    url: '/api/merchants/payouts',
    method: 'post',
    useAuth: true,
    showSuccessMessage: true,
    showErrorMessage: true,
    successMessage: 'Payment created successfully!',
    reInvalidate: true, // Auto-invalidates related queries
    reInvalidationKey: ['payments'], // Specific invalidation key
    onSuccess: response => {
      // Handle success
      console.log('Payment created:', response.data);
    },
    onError: error => {
      // Handle error
      console.error('Payment failed:', error);
    }
  });

  const handleSubmit = (paymentData: PaymentPayload) => {
    createPayment(paymentData);
  };

  return (
    <button onClick={() => handleSubmit(paymentData)} disabled={isCreating}>
      {isCreating ? 'Creating...' : 'Create Payment'}
    </button>
  );
};
```

## 🗃️ State Management Architecture

### Zustand Store Slices

```tsx
import useStore from '+store';

// Authentication slice
const { isAuthenticated, user, permissions, setAuth } = useStore();

// Currency slice
const { selectedCurrency, setCurrency } = useStore();

// Merchant slice
const { merchantData, setMerchantData } = useStore();

// User access slice
const { userPermissions, setUserPermissions } = useStore();
```

### Store Structure

```typescript
// Main store combining multiple slices
type StoreStateT = AuthSliceT &
  MerchantSliceT &
  UserAccessSliceT &
  CurrencySliceT &
  EmailConfigurationSliceT &
  TipsAndGuideSliceT &
  IssuanceSlice &
  SharedSliceT;
```

### Custom State Hook Pattern

```tsx
import { useReducerState } from '+hooks';

const MyComponent = () => {
  const [state, setState] = useReducerState({
    loading: false,
    data: null,
    error: null
  });

  const handleAction = () => {
    setState({ loading: true });
    // async operation
    setState({ loading: false, data: result });
  };
};
```

## 🎨 Styling Guidelines

### SCSS Architecture

```scss
// Main entry point
@import 'src/styles/main.scss';

// Custom Korapay variables and mixins
@import 'src/styles/kpy-custom/variables';
@import 'src/styles/kpy-custom/mixins';

// Bootstrap 4 base (legacy)
@import '~bootstrap/scss/bootstrap';
```

### Component Styling Pattern

```tsx
// ComponentName.tsx
import './ComponentName.scss';

import clsx from 'clsx';

const ComponentName = ({ variant, active }) => (
  <div className={clsx('component-name', variant && `component-name--${variant}`, active && 'component-name--active')}>Content</div>
);
```

```scss
// ComponentName.scss
.component-name {
  @include kpy-card-shadow;

  &--primary {
    background-color: $kpy-primary;
  }

  &--active {
    border-color: $kpy-accent;
  }
}
```

## 🧪 Testing Strategies

### Test File Patterns

```tsx
// ComponentName.spec.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';

import ComponentName from './ComponentName';

// Basic component testing with MockIndex
const renderComponent = (props = {}) => render(<ComponentName {...props} />, { wrapper: MockIndex });

// Route-based component testing with MockIndexWithRoute
const renderComponentWithRoute = (props = {}, route = '/dashboard', initialEntries = ['/dashboard']) =>
  render(
    <MockIndexWithRoute route={route} initialEntries={initialEntries}>
      <ComponentName {...props} />
    </MockIndexWithRoute>
  );

describe('ComponentName', () => {
  it('should render without crashing', () => {
    renderComponent();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('should handle user interactions', async () => {
    const user = userEvent.setup();
    const onSubmit = vi.fn();

    renderComponent({ onSubmit });

    await user.click(screen.getByRole('button'));

    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalled();
    });
  });

  it('should handle routing scenarios', () => {
    renderComponentWithRoute({}, '/dashboard/settings', ['/dashboard/settings']);
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });
});
```

### MSW API Mocking

```tsx
// src/__mock__/handlers.ts
import { delay, http, HttpResponse, HttpResponseResolver } from 'msw';

export function withDelay(resolver: HttpResponseResolver): HttpResponseResolver {
  return async (...args) => {
    await delay(100);
    return resolver(...args);
  };
}

export const handlers = [
  http.get(
    'http://localhost:3000/api/i/transactions/payins',
    withDelay(() => HttpResponse.json(mockedPayIns, { status: 200 }))
  ),

  http.post(
    'http://localhost:3000/api/merchants/payouts',
    withDelay(() => HttpResponse.json({ success: true }, { status: 201 }))
  ),

  http.get(
    'http://localhost:3000/api/merchants/profile/balances',
    withDelay(() => HttpResponse.json(mockedMerchantBalance, { status: 200 }))
  )
];
```

### Coverage Targets

- **Branches**: 26%
- **Functions**: 21%
- **Lines/Statements**: 31%

## 🔧 Environment Configuration

### Environment Variables (Mythological Names)

```bash
# API Endpoints
REACT_APP_MERCHANT_MIDDLEWARE_API_BASE=  # Main API
REACT_APP_AUTH_API_BASE=                 # Auth service
REACT_APP_UTILITY_API_BASE=              # Utility service

# Security & Encryption
REACT_APP_ZEUS_TRIDENT=                  # Encryption key
REACT_APP_HERMES_WINGS=                  # Bugsnag key
REACT_APP_POSEIDON_BOLT=                 # reCAPTCHA key

# External Services
REACT_APP_PEGASUS_WINGS=                 # Pusher config
REACT_APP_APOLLO_SUN=                    # Analytics
REACT_APP_ATHENA_PLATE=                  # Feature flags

# Merchant Configuration
REACT_APP_CARD_ISSUANCE_MAX_FILE_SIZE_IN_MB=
REACT_APP_RVC_CARD_CREATION_FEE=
REACT_APP_MIN_RVC_PREFUND_AMOUNT=
```

## 🚨 Error Handling & Logging

### Logger Utility (MANDATORY)

```tsx
import { Logger } from '+utils';

// NEVER use console.log - always use Logger
Logger.info('User action completed', { userId, action });
Logger.warn('Deprecation warning', { feature });
Logger.error('API request failed', errorObject);
Logger.debug('Debug information', debugData);
```

### Error Boundaries

```tsx
import { APIServiceError } from '+services/error-services';

try {
  const result = await APIRequest.processPayment(payload);
} catch (error) {
  if (error instanceof APIServiceError) {
    Logger.error('Payment processing failed', {
      code: error.code,
      message: error.message,
      payload
    });
  }
}
```

## 🔄 Real-time Integration

### Pusher WebSocket

```tsx
import { pusherService } from '+services/pusher';

useEffect(() => {
  const channel = pusherService.subscribe('merchant-updates');

  channel.bind('balance-updated', data => {
    queryClient.invalidateQueries(['balances']);
  });

  return () => {
    pusherService.unsubscribe('merchant-updates');
  };
}, []);
```

## 📊 Performance & Monitoring

### Bundle Analysis

```bash
npm run analyze  # source-map-explorer for bundle analysis
```

### Monitoring Integration

- **Bugsnag**: Error tracking and performance monitoring
- **Datadog RUM**: Real user monitoring and analytics
- **React Query DevTools**: Query inspection in development

### Build Configuration

```typescript
// vite.config.mts features:
- React plugin with Fast Refresh
- TypeScript path resolution
- Image optimization (imagemin)
- Gzip compression
- Bundle splitting for optimal loading
```

## 🔗 Third-party Integrations

### Key Dependencies

```typescript
// Forms & Validation
import { Bar, Doughnut, Line } from 'react-chartjs-2';
// UI Components
import DatePicker from 'react-datepicker';
import { useForm } from 'react-hook-form';
import PhoneInput from 'react-phone-number-input';
import Select from 'react-select';
// Charts & Visualizations
import { Chart as ChartJS } from 'chart.js';
import CryptoJS from 'crypto-js';
// Utilities
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import { Field, Form, Formik } from 'formik';
import Papa from 'papaparse';
import * as Yup from 'yup';
```

## 🎯 Merchant Domain Context

### Core Modules

- **PayIn**: Payment acceptance (cards, bank transfers, mobile money)
- **PayOut**: Payment disbursements and bulk payouts
- **Balances**: Multi-currency account management
- **Settlements**: Payment settlement and reconciliation
- **Issuing**: Card issuance and management
- **Virtual Accounts**: Dynamic virtual account creation
- **Disputes**: Chargeback and dispute resolution
- **Identity**: KYC/KYB verification and compliance

### Financial Operations

- Multi-currency support with real-time conversion
- Automated settlement scheduling
- Risk management and fraud detection
- Compliance reporting and audit trails
- Card tokenization and secure payment processing

## 📝 Code Quality Standards

### TypeScript Configuration

```typescript
// tsconfig.json highlights:
- Strict mode enabled
- ES2022 target with modern features
- Path mapping for clean imports
- Vitest globals for testing
- React JSX transformation
```

### ⚠️ CRITICAL: TypeScript Type Safety Rules

**NEVER use `any` type - Always create proper TypeScript interfaces and types:**

```tsx
// ❌ BAD - Using any types
const handleSubmit = (data: any) => {
  const response: any = await APIRequest.processPayment(data);
  return response;
};

// ✅ GOOD - Proper TypeScript interfaces
interface PaymentPayload {
  amount: number;
  currency: string;
  recipient: {
    account_number: string;
    bank_code: string;
    name: string;
  };
  reference: string;
  narration?: string;
}

interface PaymentResponse {
  success: boolean;
  data: {
    id: string;
    reference: string;
    status: 'pending' | 'processing' | 'success' | 'failed';
    created_at: string;
  };
  message: string;
}

const handleSubmit = (data: PaymentPayload): Promise<PaymentResponse> => {
  return APIRequest.processPayment(data);
};
```

**Type Definition Best Practices:**

```tsx
// ✅ Always define API response shapes
interface TransactionListResponse {
  data: {
    data: TransactionType[];
    meta: {
      current_page: number;
      total_pages: number;
      total_count: number;
      per_page: number;
    };
  };
  message: string;
  success: boolean;
}

// ✅ Define component prop interfaces
interface PaymentFormProps {
  initialData?: Partial<PaymentPayload>;
  onSubmit: (data: PaymentPayload) => Promise<void>;
  isLoading?: boolean;
  currencies: CurrencyOption[];
  onCancel: () => void;
}

// ✅ Use union types for specific values
type PaymentStatus = 'pending' | 'processing' | 'success' | 'failed' | 'cancelled';
type CurrencyCode = 'NGN' | 'USD' | 'GBP' | 'EUR' | 'GHS' | 'KES';

// ✅ Extend existing types when needed
interface MerchantPaymentPayload extends PaymentPayload {
  merchant_id: string;
  webhook_url?: string;
}
```

### ESLint Rules

- **@typescript-eslint/no-explicit-any**: **ERROR** (Enforced - NO `any` types allowed)
- **@typescript-eslint/no-unsafe-assignment**: **ERROR**
- **@typescript-eslint/no-unsafe-call**: **ERROR**
- **react-hooks/exhaustive-deps**: Enforced
- **@typescript-eslint/no-unused-vars**: Error
- **prefer-const**: Enforced for immutability
- **no-console**: Error (use Logger instead)

### Git Hooks (lint-staged)

```bash
# Pre-commit validation:
1. Prettier formatting
2. TypeScript compilation check
3. ESLint with auto-fix
4. Test execution for changed files
```

---

## 💡 Development Best Practices

1. **Always use `npm run gen`** for component scaffolding
2. **Import via aliases** (`+containers`, `+hooks`, etc.)
3. **Check permissions** in every dashboard component
4. **Use existing API methods** before creating new ones
5. **Follow the module structure pattern** religiously
6. **Test with MSW mocking** for API interactions
7. **Use Logger utility** instead of console methods
8. **Leverage React Query** for all server state
9. **Follow TypeScript strict mode** conventions
10. **Write accessibility-compliant** components
11. **Make sure implementation are simplified**
12. **Reduce the comments you add to your implementation, only where necessary.**

### 🚨 MANDATORY TypeScript Rules

13. **NEVER use `any` type** - Always create proper TypeScript interfaces and types
14. **Define explicit return types** for all functions and API calls
15. **Use union types** for specific string/number values instead of generic types
16. **Create interface extensions** rather than using intersection types with `any`
17. **Type all API responses** with proper interface definitions
18. **Avoid type assertions** (`as any`) - fix the underlying type instead

### Type Safety Enforcement

```tsx
// 🚨 FORBIDDEN - Will cause build failures
const data: any = response;
const result = someFunction() as any;
let config: any = {};

// ✅ REQUIRED - Proper type definitions
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message: string;
}

const data: ApiResponse<TransactionType[]> = response;
const result: PaymentResult = someFunction();
const config: PaymentConfiguration = {
  currency: 'NGN',
  amount: 1000,
  reference: generateReference()
};
```
## Testing
For testing reference # .github/instructions/test.instructions.md

## Design Review
For design review reference # .github/instructions/design-review.instructions.md

This is a **mission-critical fintech application** handling real money transactions. Code quality, security, and reliability are paramount.
