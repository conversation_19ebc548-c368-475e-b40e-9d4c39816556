# Testing Instructions for business-admin-fe

Author: Guidance for GitHub Copilot and contributors when creating or updating tests.

### User Event Handling

**Always await user-event interactions** in modern versions:

```tsx
import { user } from '@testing-library/user-event';

// ✅ Correct: Await user interactions
await user.click(screen.getByRole('button', { name: /edit/i }));
await user.type(screen.getByLabelText(/amount/i), '1000');
await user.selectOptions(screen.getByRole('combobox'), 'NGN');

// ❌ Incorrect: Missing await
user.click(screen.getByRole('button')); // Can cause timing issues
```

### Async Assertion Patterns

```tsx
// ✅ Use findBy for elements that appear after async operations
const editDialog = await screen.findByText(/edit withdrawal limit/i);

// ✅ Use waitFor for complex assertions
await waitFor(() => {
  expect(screen.getByTestId('confirm-button')).toBeEnabled();
});

// ✅ Combine waitFor with meaningful assertions
await waitFor(() => {
  expect(payload).toEqual(expectedPayload);
  expect(screen.getByText(/changes saved/i)).toBeInTheDocument();
});

// ❌ Avoid: Immediate synchronous queries after async actions
user.click(button);
expect(screen.getByText('Loading...')).toBeInTheDocument(); // May fail due to timing
```

### React Query Testing

When mutation side effects are expected (e.g. invalidation), assert on UI feedback (messages, buttons disabled/enabled). Don't mock `useMutation` implementation; rely on MSW handlers and network layer mocks.

**Test the user-observable behavior:**

````tsx
// ✅ Test UI state changes during async operations
await user.click(screen.getByRole('button', { name: /save/i }));

// Assert loading state
expect(screen.getByRole('button', { name: /saving/i })).toBeDisabled();

// Assert success state
await screen.findByText(/changes saved successfully/i);
expect(screen.getByRole('button', { name: /save/i })).toBeEnabled();
```ng tests.

## Core Principles

1. **User-centric**: Tests must exercise behavior as a user would (DOM queries by role/text, not implementation details).
2. **AAA Pattern**: Structure every test into Arrange, Act, Assert clearly (add blank lines between sections for readability).
3. **Single Responsibility**: One behavioral expectation per test; split scenarios instead of asserting many unrelated things.
4. **Integration Bias**: Prefer integration-style component tests over shallow/unit unless a pure function is isolated.
5. **Meaningful Assertions**: Avoid tests written only to bump coverage; assert observable outcomes, not internal variables.
6. **Avoid Library Internals**: Don't test React, react-query, Zustand, or router internals—only your usage outcomes.
7. **No Snapshot Tests**: Prefer explicit semantic assertions; broad or granular Jest/Vitest snapshots are disallowed to avoid brittle noise.
8. **Accessibility First**: Add axe (`jest-axe`) checks for each new React component or dialog surface.
9. **Deterministic Data**: Use mock server handlers (MSW) and mock store utilities already present under `+mock`.
10. **Clean Up Redundancy**: Remove or refuse duplicate tests covering the same path unless they add unique edge coverage.
11. **Use `screen` for all queries**: Avoid destructuring results from `render`.
12. **Prefer `user-event` over `fireEvent`**: Use realistic interactions and await all user events.
13. **`data-testid` is a last resort**: When accessible queries fail, use semantic selectors first.
14. **No manual `cleanup`**: It runs automatically.
15. **Consistent Async Handling**: Always await user interactions and use proper async patterns.
16. **Business-focused Test Names**: Use "should" prefix and describe behavior from user/business perspective.

## Directory & File Conventions

- Component tests live in a `__test__` or `__tests__` folder adjacent to the component.
- Hook tests live under `src/hooks/__test__` with naming `<hookName>.spec.tsx`.
- New component/file => create sibling `__test__/FileName.spec.tsx` (PascalCase original -> camel/pascal mixing OK, match existing style).
- Prefer `.spec.tsx` (or `.spec.ts` for pure utilities).
- **Mock factories**: Create `__test__/factories.ts` for feature-specific builders, import with relative paths.

## Test Naming Conventions

### Test Description Patterns

**Always use "should" prefix** for clear behavioral expectations:

```tsx
// ✅ Good: Clear, business-focused, grammatically correct
it('should enable confirm button when consent is given and validation passes', async () => {

it('should show warning when per-transaction minimum exceeds daily limit', async () => {

it('should initialize withdrawal limits to zero for custom merchants', async () => {

// ❌ Avoid: Unclear, missing context, poor grammar
it('enables confirm when consent and no validation errors', async () => {

it('shows warning when per transaction min below allowed', async () => {

it('initializes zero withdrawal limits for custom_merchants', async () => {
````

### Naming Guidelines

- **Use "should" prefix**: Makes tests read like specifications
- **Be specific**: Include relevant context (user role, conditions, expected outcome)
- **Business language**: Focus on user/business behavior, not technical implementation
- **Proper grammar**: Complete sentences with appropriate articles
- **Avoid abbreviations**: Write out full words for clarity
- **Include conditions**: When applicable, mention prerequisites or constraints

## Imports & Aliases

Use existing path aliases (e.g. `+hooks`, `+types`, `+utils`, `+mock/MockIndex`). Don’t import via relative deep paths if an alias exists.

## Rendering & Wrappers

Use `MockIndex` for rendering components needing providers (React Query, Router, Stores, Theme, etc.). Example:

```tsx
render(
  <MockIndex>
    <MyComponent propA="x" />
  </MockIndex>
);
```

For hooks, use `renderHook` with `createHookWrapper()` from `+mock/reactQueryHookWrapper` where applicable.

## Query Guidelines (Testing Library)

### Query Priority Order

1. **`getByRole`/`findByRole`**: Preferred for buttons, inputs, links (most accessible)
2. **`getByLabelText`/`findByLabelText`**: For form controls with proper labels
3. **`getByText`/`findByText`**: For static content and button text
4. **`getByDisplayValue`**: For form inputs with values
5. **`getByTestId`/`findByTestId`**: Last resort only when semantic queries fail

### Query Best Practices

- Always import and query via `screen` (avoid destructuring returns from `render`) for readability and consistency.
- Prefer `@testing-library/user-event` interactions (`user.type`, `user.click`) over `fireEvent` for realism.
- **ALWAYS await user-event interactions** in modern versions: `await user.click()`, `await user.type()`
- Use `fireEvent` only for edge cases user-event cannot cover (e.g. low-level events like resize, scroll).
- Avoid `container.querySelector` unless unavoidable - prefer semantic queries.
- Wait for async changes using `findBy*` or `waitFor`, not arbitrary `setTimeout`.
- `cleanup` is automatic; never call it manually.

### Robust Selector Patterns

```tsx
// ✅ Preferred: Semantic and accessible
screen.getByRole('button', { name: /submit|confirm/i });
screen.getByLabelText(/amount|limit/i);

// ⚠️ Acceptable: When semantic queries aren't available
screen.getByText(/expected user-visible text/i);

// ❌ Avoid: Brittle implementation details
screen.getByPlaceholderText('Enter value'); // Brittle - placeholder can change
screen.getByTestId('generic-button'); // Generic test-ids provide no context
screen.getByClassName('btn-primary'); // Implementation detail

// ✅ Better alternatives
screen.getByRole('textbox', { name: /settlement amount/i });
screen.getByTestId('confirm-withdrawal-button'); // Specific, descriptive test-id
```

## Async & React Query

When mutation side effects are expected (e.g. invalidation), assert on UI feedback (messages, buttons disabled/enabled). Don’t mock `useMutation` implementation; rely on MSW handlers and network layer mocks.

## Mocking Strategy

### HTTP Mocking with MSW

- Use MSW (`server`, `http`, `HttpResponse`) for API calls.
- All handlers reside centrally under `+mock/handlers` (no per-test or per-feature ad-hoc handler files).
- Mock data organization: Feature-specific builders in `__test__/factories.ts`, cross-feature builders in `+mock/mockData`. Avoid scattered mock files.
- Do NOT mock implementation details of React components, hooks, or utilities under test.
- Only mock external modules for: network failure simulation, permission hooks (`useSetUserAccess`) when access gating is the behavior.
- Provide realistic payloads reflecting `+types` definitions.
- Use `server.use(...)` inside a test only to temporarily override an existing path defined in `+mock/handlers` (e.g. different status code or payload). Never introduce a brand‑new endpoint path solely inside a test—add it to central handlers instead.

### Mock Data Best Practices

**Avoid inline complex mock objects in tests:**

```tsx
// ✅ Better: Use reusable builders from central factories
import { buildWithdrawalLimitConfig } from '+mock/factories';

// ❌ Avoid: Complex inline mock data
server.use(
  http.get('/api/config', () =>
    HttpResponse.json({
      data: {
        setting: {
          transaction: {
            disbursement: {
              bank_account: {
                limits: {
                  web: {
                    daily: { settlement_account: 5000, non_settlement_account: 4000 },
                    per_transaction: {
                      settlement_account: { min: 100, max: 3000 },
                      non_settlement_account: { min: 200, max: 1000 }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })
  )
);

const mockConfig = buildWithdrawalLimitConfig({
  web: {
    daily: { settlement_account: 5000, non_settlement_account: 4000 },
    per_transaction: {
      settlement_account: { min: 100, max: 3000 },
      non_settlement_account: { min: 200, max: 1000 }
    }
  }
});

server.use(http.get('/api/config', () => HttpResponse.json(mockConfig)));
```

**Create reusable mock factories:**

- Extract repeated mock structures into builder functions
- **Central factories**: Place in `+mock/factories.ts` as the primary location for all test data builders
- **Cross-feature factories**: Place in `+mock/mockData` for simple data structures and legacy compatibility
- Use TypeScript interfaces that match `+types` definitions
- Provide sensible defaults with override capability

## Mock Data Organization & Handlers

### Centralization Strategy

Similar to how `+mock/handlers.ts` centralizes all API endpoint mocking, we now use `+mock/factories.ts` as the central location for all test data builders. This provides:

- **Single source of truth** for test data creation
- **Easy discoverability** of existing builders
- **Consistent patterns** across all tests
- **Better maintainability** when data structures change

### Organization Rules

- **API Handlers**: Define and export all endpoint handlers from `+mock/handlers.ts`
- **Test Data Builders**: Define and export all data builders from `+mock/factories.ts`
- **Legacy Mock Data**: Simple data structures can remain in `+mock/mockData` for backward compatibility
- **No scattered mock files**: Avoid creating per-test or per-feature mock files

### Factory Naming Conventions

```typescript
// ✅ Good: Clear, descriptive naming with build prefix
export const buildWithdrawalLimitConfig = (overrides = {}) => ({ ... });
export const buildEditDetailsCardProps = (overrides = {}) => ({ ... });
export const buildTransactionLimit = (overrides = {}) => ({ ... });

// ❌ Avoid: Generic or unclear naming
export const mockConfig = { ... }; // Use in +mock/mockData instead
export const data = { ... }; // Too generic
export const createLimit = (overrides = {}) => ({ ... }); // Use "build" prefix
```

### Factory Organization in `+mock/factories.ts`

Group builders by feature/domain with clear section headers:

```typescript
// =============================================================================
// WITHDRAWAL LIMITS FACTORIES
// =============================================================================

export const buildTransactionLimit = (overrides = {}) => ({ ... });
export const buildWithdrawalLimitConfig = (overrides = {}) => ({ ... });

// =============================================================================
// CARD ISSUANCE FACTORIES
// =============================================================================

export const buildCardDetails = (overrides = {}) => ({ ... });
export const buildCardTransaction = (overrides = {}) => ({ ... });
```

### Implementation Guidelines

- Prefer builders (`buildX(overrides)`) to reduce duplication
- When modifying an existing mock shape, update the central source—avoid copy/paste divergence
- Override behavior in a specific test with `server.use(existingHandlerWithDifferentResponse)` only when the path already exists centrally
- If a new API path is needed for a feature, add it to `+mock/handlers` first, then write tests
- Align field names & types strictly with definitions under `+types`; update mocks if the type surface changes

### Example: Centralized Factories with Handlers

```ts
// +mock/factories.ts (excerpt)
export const buildAccount = (overrides: Partial<{id: string; name: string; status: string}> = {}) => ({
  id: 'acc_123',
  name: 'Primary Account',
  status: 'active',
  ...overrides
});

export const buildWithdrawalLimitConfig = (overrides = {}) => ({
  web: {
    daily: { settlement_account: 100000, non_settlement_account: 80000 },
    per_transaction: { settlement_account: { min: 100, max: 1000 } }
  },
  ...overrides
});

// +mock/handlers.ts (excerpt)
import { http, HttpResponse } from 'msw';
import { buildAccount, buildWithdrawalLimitConfig } from '+mock/factories';

export const accountHandlers = [
  http.get('/api/accounts/:id', ({ params }) => HttpResponse.json(buildAccount({ id: params.id as string }))),
  http.get('/admin/settings/merchants/configuration', () =>
    HttpResponse.json({ data: { setting: { transaction: { disbursement: { limits: buildWithdrawalLimitConfig() } } } } })
  ),
];

// In a test file
import { server } from '+mock/mockServers';
import { http, HttpResponse } from 'msw';
import { buildAccount, buildWithdrawalLimitConfig } from '+mock/factories';

it('should display custom withdrawal limits for test scenario', async () => {
  // Override existing path with different payload (path already defined centrally)
  server.use(
    http.get('/admin/settings/merchants/configuration', () =>
      HttpResponse.json({
        data: {
          setting: {
            transaction: {
              disbursement: {
                limits: buildWithdrawalLimitConfig({
                  web: { daily: { settlement_account: 50000 } }
                })
              }
            }
          }
        }
      })
    )
  );

  const { render } = renderWithProviders(<EditDetailsCard {...buildEditDetailsCardProps()} />);

  expect(await screen.findByText(/50,000/)).toBeInTheDocument();
});
```

Key points:

- Central handlers own the route definitions; tests only override responses.
- No new handler files per test.
- Builder reused for consistency.
- Override limited in scope to this test execution.

### Factory Organization Example

```text
src/
├── __mock__/
│   ├── factories.ts              // ✅ Central repository for ALL test data builders
│   ├── handlers.ts               // ✅ Central repository for ALL API handlers
│   └── mockData.ts               // ✅ Legacy simple data structures
├── containers/Dashboard/ProductConfig/components/
│   ├── __test__/
│   │   └── EditDetailsCard.spec.tsx  // Uses +mock/factories
│   ├── hooks/
│   │   └── __test__/
│   │       └── useLimit.spec.tsx     // Uses +mock/factories
│   └── EditDetailsCard.tsx

src/__mock__/
└── mockData.ts                   // Cross-feature builders only
```

```tsx
// __test__/factories.ts - Feature-specific builders
export const buildWithdrawalLimitConfig = (overrides: any = {}) => ({
  // withdrawal limit specific structure
});

export const buildEditDetailsCardProps = (overrides: any = {}) => ({
  // component props specific to this feature
});

// +mock/mockData.ts - Cross-feature builders only
export const buildApiResponse = (overrides: any = {}) => ({
  // generic API response structure used by many features
});
```

## State & Stores (Zustand)

Pattern summary:

- A custom `create` wrapper in `+mock/mockStore` captures each store's initial state and automatically resets it after every test (via `afterEach`).
- Production stores should import `create` from 'zustand'; tests that need reset semantics rely on the jest module factory override pointing `zustand` to the mock wrapper (already configured in the test environment).
- Persisted stores (using `persist` + `createJSONStorage`) are safe because the mock reset uses `store.setState(initialState, true)` forcing a full replace.

Guidelines:

1. Prefer interacting with store state via rendered UI (buttons, inputs) rather than calling setters directly—keeps tests user-centric.
2. If direct store manipulation is unavoidable (e.g. seeding complex baseline state):
   - Import the store hook directly (e.g. `import useChargebackStore from '+store/chargebackStore'`).
   - Use `act(() => useChargebackStore.getState().setChargebackData(fixture))` before rendering.
3. Never rely on state leakage between tests; the auto-reset ensures isolation. If a test breaks when run alone, rework it.
4. Avoid asserting against internal middleware artifacts (devtools/persist metadata). Only assert the business-facing slice values.
5. For transient updates that depend on asynchronous queues, wrap mutations in `act` and then `await waitFor` on the UI change.
6. Do not mock individual store modules; central reset logic is sufficient. Mock only external dependencies the stores consume (e.g. network layer) if necessary via MSW.
7. When adding a new store file, ensure its initial state is a plain serializable object so snapshot-like deep equality (during reset) is deterministic.

Example (seeding state explicitly when UI path is too expensive):

```tsx
import { buildChargeback } from '+mock/mockData';
import useChargebackStore from '+store/chargebackStore';

it('lists existing chargeback entries', async () => {
  const seed = buildChargeback({ id: 'cb_1', amount: 500 });
  act(() => {
    useChargebackStore.getState().setChargebackData({
      processor: 'visa',
      currency: 'USD',
      deadline: '2025-12-31',
      chargebacks: [seed]
    });
  });
  render(
    <MockIndex>
      <ChargebackList />
    </MockIndex>
  );
  expect(await screen.findByText(/visa/i)).toBeInTheDocument();
  expect(screen.getByText(/500/)).toBeInTheDocument();
});
```

If you find yourself seeding more than ~10 lines of state repeatedly, introduce a builder in `+mock/mockData` and reuse it.

## Formatting & Numeric Inputs

For currency or other formatted numeric inputs:

- Assert user-visible formatted display (e.g. `1,000.00`) rather than internal raw values.
- Trigger input changes with realistic raw user entry and assert the UI reflects formatted output and side effects (callbacks, button enablement, etc.).

## Multi-Mode / Variant Components

For components that expose multiple modes, tabs, or variants (e.g. channel types, view/edit states):

- Ensure at least one test exercises each distinct mode.
- Cover transitions between modes (state persistence vs intentional reset).
- Assert gating conditions (e.g. consent checkbox enabling a confirm button) where applicable.
- Avoid reintroducing assertions for deprecated logic paths that have been removed from the implementation.

## Accessibility (a11y)

### Mandatory Accessibility Testing

**Every new component must include accessibility tests:**

```tsx
import { axe } from 'jest-axe';

it('should be accessible', async () => {
  const { container } = render(
    <MockIndex>
      <MyComponent />
    </MockIndex>
  );

  const results = await axe(container);
  expect(results).toHaveNoViolations();
});
```

### Accessibility Best Practices

- **Fix violations, don't silence**: If axe reports violations, fix the component rather than disabling rules
- **Test with realistic data**: Run axe tests with populated components, not empty states
- **Include keyboard navigation**: Test tab order and keyboard interactions
- **Screen reader support**: Ensure proper labeling and ARIA attributes
- **Focus management**: Test focus handling in modals and dynamic content

### Common Accessibility Issues to Test

```tsx
// ✅ Proper form labeling
screen.getByLabelText(/withdrawal amount/i); // Ensures label association

// ✅ Button accessibility
screen.getByRole('button', { name: /confirm withdrawal/i }); // Accessible name required

// ✅ Focus management in modals
const modal = screen.getByRole('dialog');
expect(modal).toHaveFocus(); // Modal should trap focus

// ✅ Error announcement
expect(screen.getByRole('alert')).toHaveTextContent(/validation error/i);
```

**Note**: Group multiple related assertions within one test that already executed `axe` to keep test performance reasonable.

## Comprehensive Test Coverage

### Essential Test Categories

Every significant component should include tests for:

1. **Happy Path**: Primary user workflows function correctly
2. **Validation**: Form validation and business rule enforcement
3. **Error States**: Network failures, API errors, invalid data
4. **Permissions**: Access control and authorization scenarios
5. **Edge Cases**: Boundary values, empty states, unusual inputs
6. **Accessibility**: Screen reader and keyboard navigation support

### Edge Cases To Cover

Consider (choose those relevant per component):

- **Data States**: Empty / zero / null value objects, missing required fields
- **Permissions**: Denied access (e.g. no Edit button), role-based restrictions
- **Network Issues**: API error states (MSW 500 response) => error banner or message
- **Numeric Boundaries**: Large values, negative numbers, decimal precision limits
- **Form Validation**: Required fields, format validation, cross-field dependencies
- **State Management**: Tab switching, mode toggling, data persistence vs reset
- **Loading States**: Buttons disabled during API calls, spinner/skeleton components
- **User Interactions**: Rapid clicking, invalid input sequences, keyboard navigation

### Error Scenario Testing

```tsx
// ✅ Include network error testing
server.use(http.get('/api/config', () => HttpResponse.error()));

render(
  <MockIndex>
    <MyComponent />
  </MockIndex>
);
expect(await screen.findByText(/error loading configuration/i)).toBeInTheDocument();

// ✅ Include validation error testing
await user.type(screen.getByLabelText(/amount/i), '-100');
expect(screen.getByText(/amount must be positive/i)).toBeInTheDocument();
```

## Test Data Builders

When repetitive object shapes emerge for a feature domain, create mock factories:

- **Feature-specific builders**: Place in `__test__/factories.ts` within the feature folder closest to tests using them
- **Cross-feature builders**: Place in `+mock/mockData` for builders used across multiple features
- Use descriptive factory function names: `buildWithdrawalLimitConfig()`, `buildEditDetailsCardProps()`
- Support partial overrides with sensible defaults

```tsx
// __test__/factories.ts
export const buildWithdrawalLimitConfig = (overrides: any = {}) => ({
  web: {
    daily: { settlement_account: 100000, non_settlement_account: 80000 },
    per_transaction: {
      settlement_account: { min: 100, max: 1000 },
      non_settlement_account: { min: 200, max: 900 }
    }
  },
  api: { per_transaction: { min: 50, max: 500 } },
  ...overrides
});
```

## Axe Performance

Group multiple related assertions within one test that already executed `axe`—don’t run axe in every micro-test for the same render to keep test time reasonable.

## Hooks Testing Pattern

```tsx
const { result } = renderHook(() => useFeedbackHandler(), { wrapper: createHookWrapper() });
act(() => result.current.feedbackInit({...}));
await waitFor(() => expect(result.current.data.isActive).toBe(true));
```

Never assert on internal refs or closure variables not part of the public return API.

## Failure Messaging

Use clear assertion messages only when custom logic clarifies intent. Avoid unnecessary messages that duplicate expected text.

## Lint & Types

All test code must pass TypeScript and ESLint. **Prefer explicit types for complex fixture objects; let inference handle trivial literals.**

### TypeScript Best Practices for Tests

**MANDATORY: Avoid `any` types in test code**

- **Use proper TypeScript interfaces** that match `+types` definitions
- **Import types from the types directory** for test data builders
- **Prefer type-safe factory functions** over generic `any` parameters
- **Define explicit return types** for complex mock builders

```tsx
// ✅ Good: Explicit types from +types
import { WithdrawalLimitType, TransactionLimitType, EditDetailsCardPropsType } from '+types/productConfig';

export const buildWithdrawalLimitConfig = (overrides: Partial<WithdrawalLimitType> = {}): WithdrawalLimitType => ({
  api: { per_transaction: { min: 50, max: 500 } },
  web: {
    daily: { settlement_account: 100000, non_settlement_account: 80000 },
    per_transaction: {
      settlement_account: { min: 100, max: 1000 },
      non_settlement_account: { min: 200, max: 900 }
    }
  },
  ...overrides
});

// ❌ Avoid: Generic any types
export const buildWithdrawalLimitConfig = (overrides: any = {}) => ({
  // loses type safety and IntelliSense
});
```

**Type Safety Benefits:**

- Ensures mock data matches actual interfaces
- Provides IntelliSense and autocompletion
- Catches type mismatches at compile time
- Makes refactoring safer when types change
- Improves code readability and maintainability

## Do / Don’t Quick List

Do:

- **Use explicit TypeScript types - NEVER use `any`** in test code
- Use `jest-axe` for new UI surfaces.
- Use MSW for HTTP.
- Assert visible strings & roles.
- Keep tests deterministic.
- Separate happy path & edge cases.
- Use `screen` consistently for queries.
- Use `user-event` over `fireEvent`.
- Focus only on user-facing behavior and interaction with the Component.
- Make sure implementation are simplified
- Reduce the comments you add to your implementation, only add comments where it necessary
- Import types from `+types` directory for mock data builders

Don’t:

- Mock React Query internals.
- Test library implementation details.
- Chain many unrelated asserts in one test.
- Depend on internal store/private function signatures.
- Sprinkle `data-testid` liberally—treat them as last resort when accessible queries fail.
- Manually call `cleanup()`.
- Don't have implementation detail within test

## Adding New Feature Tests Checklist

1. Identify new/changed files (git diff against `staging`).
2. Determine user-facing behaviors introduced.
3. Add/augment tests in sibling `__test__` folders.
4. Add accessibility test.
5. Add edge-case scenarios (error, empty, permission denied, boundary values).
6. Ensure MSW handlers exist or extend existing ones.
7. Run `npm test` (Vitest) & ensure no violations.
8. Refactor fixtures/builders if duplication introduced.
9. Remove obsolete tests invalidated by logic changes.
10. Open PR with concise summary of behavioral coverage.

## Example Minimal Component Test

```tsx
it('enables confirm after consent', async () => {
  render(
    <MockIndex>
      <MyFeatureComponent />
    </MockIndex>
  );
  user.click(screen.getByRole('button', { name: /edit/i }));
  const confirmBtn = screen.getByRole('button', { name: /confirm/i });
  expect(confirmBtn).toBeDisabled();
  user.click(screen.getByTestId('consent-checkbox'));
  await waitFor(() => expect(confirmBtn).toBeEnabled());
});
```

## Test Quality Best Practices

### Code Organization and DRY Principles

**Extract reusable test utilities:**

```tsx
// ✅ Create custom render helpers for component families
const renderWithdrawalLimitComponent = (props = {}) => {
  const defaultProps = {
    title: 'limits',
    currency: 'NGN',
    category: 'payouts',
    paymentMethod: 'bank_account',
    merchantId: '23',
    ...props
  };

  return render(
    <MockIndex>
      <EditDetailsCard {...defaultProps} />
    </MockIndex>
  );
};

// ✅ Create assertion helpers for complex validations
const expectWithdrawalLimitValidation = (errorMessage: string) => {
  expect(screen.getByRole('alert')).toHaveTextContent(errorMessage);
  expect(screen.getByRole('button', { name: /confirm/i })).toBeDisabled();
};
```

**Avoid repetitive mock setup:**

```tsx
// ❌ Avoid: Repeated mock configuration in each test
// ✅ Better: Extract to shared builders in +mock/mockData
const buildWithdrawalLimitMock = (overrides = {}) => ({
  /* factory */
});
```

### Comprehensive Coverage Requirements

For every major component, ensure you test:

1. **Happy Path Flows**: Primary user interactions work as expected
2. **Form Validation**: All validation rules and error states
3. **Permission Gates**: Access control based on user permissions
4. **Network Scenarios**: Loading states, success responses, error handling
5. **Edge Cases**: Boundary values, empty states, invalid inputs
6. **Accessibility**: Screen reader support and keyboard navigation
7. **State Management**: Data persistence and proper cleanup

### Assertion Quality

**Use descriptive, specific assertions:**

```tsx
// ❌ Weak: Generic, unclear assertions
expect(screen.getByTestId('button')).toBeDisabled();

// ✅ Strong: Specific, meaningful assertions
expect(screen.getByRole('button', { name: /confirm withdrawal/i })).toBeDisabled('Button should be disabled until consent is given');

// ✅ Business-focused assertions
expect(screen.getByText(/daily limit: ₦5,000.00/i)).toBeInTheDocument();
```

### Performance and Maintainability

- **Group related assertions**: Don't run `axe` in every micro-test
- **Use semantic queries**: Prefer `getByRole` over `getByTestId`
- **Avoid implementation details**: Test behavior, not internal state
- **Keep tests isolated**: Each test should be independently runnable
- **Use realistic data**: Mock data should match production scenarios

## Maintenance Guidance

- Revisit tests when refactoring to remove obsolete validation or flows.
- Prefer enhancing existing tests over creating near-duplicates.
- Keep this file updated when patterns evolve (e.g., if adding RTL custom render helper, update examples here).

---

Generated: 2025-09-11. Updated with comprehensive best practices and quality guidelines.
